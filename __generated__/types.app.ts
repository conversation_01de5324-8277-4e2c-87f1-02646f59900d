import gql from 'graphql-tag';
import * as Urql from 'urql';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Date: { input: any; output: any; }
  JSON: { input: any; output: any; }
};

export type Query = {
  __typename?: 'Query';
  getStrategyConnectors: Array<Maybe<Connector>>;
  getStrategy: Strategy;
  getStrategies: Array<Strategy>;
  getStrategiesByPage: Array<Strategy>;
  getStrategiesCount: Scalars['Int']['output'];
  getBattlefieldStrategy: BattlefieldStrategy;
  getTeamBattlefieldStrategy: Array<BattlefieldStrategy>;
  /**
   * Only return onboard battlefield strategies:
   * - getBattlefieldStrategiesByPage
   * - getBattlefieldStrategies
   * - getBattlefieldStrategiesCountByFilter
   * - getBattlefieldStrategiesByFilter
   */
  getBattlefieldStrategiesByPage: Array<BattlefieldStrategy>;
  getBattlefieldStrategies: Array<BattlefieldStrategy>;
  getBattlefieldStrategiesCountByFilter: Scalars['Int']['output'];
  getBattlefieldStrategiesByFilter: Array<BattlefieldStrategy>;
  getStrategyTemplates?: Maybe<Array<StrategyTemplate>>;
  getSupportedExchanges: Array<ExchangePairsMarket>;
  me: User;
  getLivetradingPairs: Array<PairsMarket>;
  getBroker: BrokerTask;
  traderTasksCursor: TraderTaskConnection;
  /**
   * Infinite Scrolling loading notifications of user,
   * The very first query should pass "" to after
   */
  getNotifications: NotificationsConnection;
  isStrategyNameRepeat: Scalars['Boolean']['output'];
  apiVersion: VersionInfo;
  getTraderLockAmount: Scalars['Float']['output'];
  getCompetitions: Array<Competition>;
  getCompetition: Competition;
  getBattlefieldStrategiesCount: Scalars['Int']['output'];
  getCompetitionTeamId: Scalars['Int']['output'];
  getCompetitionTeamMembers: Array<CompetitionTeamMember>;
  getCompetitionTeamInfo: CompetitionTeamInfo;
  getSubscriptionPlans?: Maybe<Array<SubscriptionPlan>>;
  getChangeUserSubscriptionPlanInfo: ChangeUserSubscriptionPlanInfo;
  getExchangePairRate: Scalars['Float']['output'];
  getForexRate: Scalars['Float']['output'];
  /** CLUB */
  getClubsByPage: Array<Club>;
  getClubByIds: Array<Club>;
  getClubsCount: Scalars['Int']['output'];
  getMemberClubsByPage: Array<Club>;
  getMemberClubPerformanceByIds: Array<ClubPerformance>;
  getMemberClubsCount: Scalars['Int']['output'];
  getClubStrategiesByPage: Array<ClubStrategy>;
  getClubStrategiesByIds: Array<ClubStrategy>;
  getClubStrategiesCount: Scalars['Int']['output'];
  getManagedClubs: Array<Club>;
  getClubMembersByRoles: Array<UserClubMembership>;
  getClubMembershipPlans: Array<ClubMembershipPlan>;
  getUserClubMembership: Array<UserClubMembership>;
  getNewestClubStrategy: Array<ClubStrategy>;
  getNewClubStrategyCount: Array<Scalars['Int']['output']>;
  getUserClubLastSeen: Array<UserClubLastSeen>;
  /** COUPON */
  getUserCoupons: Array<UserCoupon>;
  getCouponByCode?: Maybe<Coupon>;
};


export type QueryGetStrategyConnectorsArgs = {
  strategyId: Scalars['ID']['input'];
};


export type QueryGetStrategyArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGetStrategiesByPageArgs = {
  page: Scalars['Int']['input'];
  countsPerPage: Scalars['Int']['input'];
};


export type QueryGetBattlefieldStrategyArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGetTeamBattlefieldStrategyArgs = {
  competitionId: Scalars['ID']['input'];
  strategyStatuses: Array<InputMaybe<StrategyStatus>>;
};


export type QueryGetBattlefieldStrategiesByPageArgs = {
  page: Scalars['Int']['input'];
  countsPerPage: Scalars['Int']['input'];
};


export type QueryGetBattlefieldStrategiesCountByFilterArgs = {
  filter?: InputMaybe<BattlefieldStrategyFilterInput>;
};


export type QueryGetBattlefieldStrategiesByFilterArgs = {
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  filter?: InputMaybe<BattlefieldStrategyFilterInput>;
};


export type QueryGetStrategyTemplatesArgs = {
  locale?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetSupportedExchangesArgs = {
  investmentType?: InputMaybe<StrategyInvestmentType>;
};


export type QueryGetLivetradingPairsArgs = {
  investmentType?: InputMaybe<StrategyInvestmentType>;
  exchange?: InputMaybe<SupportedExchange>;
};


export type QueryGetBrokerArgs = {
  id: Scalars['ID']['input'];
};


export type QueryTraderTasksCursorArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<TraderTaskFilterInput>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryGetNotificationsArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
};


export type QueryIsStrategyNameRepeatArgs = {
  name: Scalars['String']['input'];
};


export type QueryGetTraderLockAmountArgs = {
  strategyId: Scalars['ID']['input'];
  baseAmount: Scalars['Float']['input'];
  baseCurrency: Scalars['String']['input'];
  lockGainRatio: Scalars['Float']['input'];
};


export type QueryGetCompetitionsArgs = {
  statuses?: InputMaybe<Array<CompetitionStatus>>;
  types?: InputMaybe<Array<CompetitionType>>;
};


export type QueryGetCompetitionArgs = {
  competitionId: Scalars['ID']['input'];
};


export type QueryGetCompetitionTeamIdArgs = {
  competitionId?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetCompetitionTeamMembersArgs = {
  teamId?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetCompetitionTeamInfoArgs = {
  competitionId?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetSubscriptionPlansArgs = {
  period?: InputMaybe<Scalars['String']['input']>;
  periodUnit?: InputMaybe<Scalars['String']['input']>;
  isClubMember?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryGetChangeUserSubscriptionPlanInfoArgs = {
  newPlanId: Scalars['Int']['input'];
  userCouponId?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetExchangePairRateArgs = {
  exchange: SupportedExchange;
  base: Scalars['String']['input'];
  quote: Scalars['String']['input'];
};


export type QueryGetForexRateArgs = {
  base: SupportedFiat;
  quote: SupportedFiat;
};


export type QueryGetClubsByPageArgs = {
  page: Scalars['Int']['input'];
  countsPerPage: Scalars['Int']['input'];
  orderBy?: InputMaybe<PrismaClubOrderBy>;
  keyword?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetClubByIdsArgs = {
  clubIds: Array<Scalars['Int']['input']>;
};


export type QueryGetClubsCountArgs = {
  keyword?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetMemberClubsByPageArgs = {
  page: Scalars['Int']['input'];
  countsPerPage: Scalars['Int']['input'];
  orderBy?: InputMaybe<PrismaMemberClubOrderBy>;
  keyword?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetMemberClubPerformanceByIdsArgs = {
  clubIds?: InputMaybe<Array<Scalars['Int']['input']>>;
};


export type QueryGetMemberClubsCountArgs = {
  keyword?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetClubStrategiesByPageArgs = {
  page: Scalars['Int']['input'];
  countsPerPage: Scalars['Int']['input'];
  filter?: InputMaybe<PrismaClubFilterBy>;
  keyword?: InputMaybe<Scalars['String']['input']>;
  clubId: Scalars['Int']['input'];
};


export type QueryGetClubStrategiesByIdsArgs = {
  ids: Array<Scalars['Int']['input']>;
};


export type QueryGetClubStrategiesCountArgs = {
  clubId: Scalars['Int']['input'];
  filter?: InputMaybe<PrismaClubFilterBy>;
  keyword?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetClubMembersByRolesArgs = {
  clubId: Scalars['Int']['input'];
  roles?: InputMaybe<Array<ClubMemberRole>>;
};


export type QueryGetClubMembershipPlansArgs = {
  clubId: Scalars['Int']['input'];
};


export type QueryGetUserClubMembershipArgs = {
  clubId: Scalars['Int']['input'];
};


export type QueryGetNewestClubStrategyArgs = {
  clubIds: Array<Scalars['Int']['input']>;
};


export type QueryGetNewClubStrategyCountArgs = {
  clubIds: Array<Scalars['Int']['input']>;
};


export type QueryGetUserCouponsArgs = {
  type?: InputMaybe<CouponType>;
};


export type QueryGetCouponByCodeArgs = {
  code: Scalars['String']['input'];
  battlefieldStrategyId?: InputMaybe<Scalars['Int']['input']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  login?: Maybe<Scalars['String']['output']>;
  loginWithThirdPartyToken?: Maybe<Scalars['String']['output']>;
  refetchThirdPartyApiKey?: Maybe<ExchangeApiKey>;
  signup: SignupResult;
  enterReferralCode: Scalars['Boolean']['output'];
  changeLocale: SupportedLocale;
  changeUserRole: UserRole;
  /** Return OK or throw error */
  resendActivationEmail: Scalars['String']['output'];
  refreshToken?: Maybe<Scalars['String']['output']>;
  activateAccount: ActivateAccountResult;
  requestResetAccountPassword: RequestResetAccountPasswordResult;
  resetAccountPassword: ResetAccountPasswordResult;
  changePassword: ChangePasswordResult;
  updateUser: UpdateUserResult;
  updateUserFirstName: UpdateUserFirstNameResult;
  /** Return OK when success */
  setNotificationsSeen: Scalars['String']['output'];
  /**
   * JSON argument: initAssetsWallet: {Bitfinex: {ETH: 0, USDT: 10000}}, crypto currency of targetPair need to be setted (to 0 or some),
   * targetExchangePair: array of {exchange, pair}, ex: [ {exchange: BITFINEX, pair: "ETH/USDT"} ]
   */
  launchBacktest: BacktestTask;
  launchSimulation: SimulationTask;
  launchTrader: TraderTask;
  stopBroker: BrokerTask;
  /** Alias of stopBroker */
  stopTask: BrokerTask;
  /** Return 'OK' when success */
  deleteBrokerTask: Scalars['String']['output'];
  /** Return 'OK' when success */
  deleteAllFinishedBrokerTasks: Scalars['String']['output'];
  /** Return 'OK' when success */
  deleteBacktest: Scalars['String']['output'];
  /** Return 'OK' when success */
  deleteSimulation: Scalars['String']['output'];
  /** Return 'OK' when success */
  deleteTrader: Scalars['String']['output'];
  updateTraderUserSetting: Scalars['Boolean']['output'];
  createStrategy?: Maybe<Strategy>;
  createStrategyByTemplate?: Maybe<Strategy>;
  changeStrategyTemplate?: Maybe<Strategy>;
  duplicateStrategy?: Maybe<Strategy>;
  updateStrategy?: Maybe<Strategy>;
  createStrategyTaskConfig: StrategyTaskConfig;
  updateStrategyTaskConfig: StrategyTaskConfig;
  deleteStrategyTaskConfig: StrategyTaskConfig;
  /** OK or exception */
  deleteStrategy: Scalars['String']['output'];
  createExchangeApiKey?: Maybe<ExchangeApiKey>;
  /** OK or exception */
  deleteExchangeApiKey: Scalars['String']['output'];
  createUserWatchlist: UserWatchlist;
  renameUserWatchlist: UserWatchlist;
  deleteUserWatchlist: Scalars['Boolean']['output'];
  /**
   * Only add to default watchlist when userWatchlistId is not given;
   * otherwise, add to both default watchlist and specified watchlist.
   */
  addBattlefieldStrategyToWatchlist: BattlefieldStrategy;
  /**
   * Delete from all watchlists when userWatchlistId is not given;
   * otherwise, only delete from specified watchlist.
   */
  deleteBattlefieldStrategyFromWatchlist: BattlefieldStrategy;
  /**
   * First request upload avator, then use post to upload file to cloud storage, example:
   * <form action="https://storage.googleapis.com/crypto-arsenal-avatars" method="post" id="the-form" enctype="multipart/form-data">
   * <input type="text" name="key" value="FILE_NAME IN RESULT">
   * <input type="hidden" name="bucket" value="crypto-arsenal-avatars">
   * <input type="hidden" name="Content-Type" value="image/jpeg">
   * <input type="hidden" name="GoogleAccessId" value="<EMAIL>">
   * <!-- <input type="hidden" name="success_action_redirect" value="http://www.example.com/success_notification.html"> -->
   * <input type="hidden" name="policy" value="POLICY IN RESULT">
   * <input type="hidden" name="signature" value="POLICYSIG IN RESULT">
   * <input name="file" type="file">
   * <input type="submit" value="Upload">
   * </form>
   */
  requestUploadImage: RequestUploadImageResult;
  requestUploadOctetStream: RequestUploadFileResult;
  /** After upload to cloud storage, use confirm api to change avatar */
  confirmImageUploaded: ConfirmImageUploadedResult;
  changeDefaultAvatar: Scalars['Boolean']['output'];
  createUserWallet: CreateUserWalletResult;
  createDepositAddress: CreateDepositAddressResult;
  createWithdrawalAddress: Scalars['Boolean']['output'];
  deleteWithdrawalAddress: Scalars['Boolean']['output'];
  withdrawAssets: WithdrawResult;
  remoteStrategyHeartbeat: RemoteStrategyHeartbeatResult;
  requestEnable2Fa: RequestEnable2FaResult;
  confirmEnable2Fa: ConfirmEnable2FaResult;
  disable2Fa: Disable2FaResult;
  requestEmailOtp: Scalars['Boolean']['output'];
  requestBindEmailOtp: Scalars['Boolean']['output'];
  bindEmail: Scalars['Boolean']['output'];
  /** return if email send to service@ca */
  feedback: Scalars['Boolean']['output'];
  validateCompetitionPasscode: Scalars['Boolean']['output'];
  registryBattlefieldStrategy: BattlefieldStrategy;
  lockBattlefieldStrategy: Strategy;
  unlockBattlefieldStrategy: Strategy;
  cancelStrategyRegistry: Strategy;
  offboardStrategy: Strategy;
  ackRejection: Strategy;
  ackOffboarding: Strategy;
  createBattlefieldStrategyComment: BattlefieldStrategyComment;
  editBattlefieldStrategyComment: BattlefieldStrategyComment;
  deleteBattlefieldStrategyComment: Scalars['Boolean']['output'];
  likeBattlefieldStrategyComment: BattlefieldStrategyComment;
  unlikeBattlefieldStrategyComment: BattlefieldStrategyComment;
  followBattlefieldStrategyDeveloper: PublicUser;
  unfollowBattlefieldStrategyDeveloper: PublicUser;
  registryCompetitionTeam: RegistryCompetitionTeamResult;
  changeUserSubscriptionPlan: Scalars['Boolean']['output'];
  changeSubscriptionRenewUsedUserCouponId: Scalars['Boolean']['output'];
  redeemGiftcode: Scalars['Boolean']['output'];
  addStrategyConnector: Connector;
  removeStrategyConnector: Scalars['Boolean']['output'];
  updateConnectorRecipe: Connector;
  setUserNotificationPreference?: Maybe<Scalars['JSON']['output']>;
  /** CLUB */
  createClub?: Maybe<Club>;
  createClubMembershipPlan?: Maybe<ClubMembershipPlan>;
  joinClubMembershipPlan?: Maybe<UserClubMembership>;
  changeClubMembershipPlan?: Maybe<UserClubMembership>;
  createUserClubMembership?: Maybe<UserClubMembership>;
  deleteUserClubMembership?: Maybe<Scalars['Boolean']['output']>;
  updateClubStats?: Maybe<Club>;
  createClubStrategy?: Maybe<ClubStrategy>;
  deleteAllClubStrategy?: Maybe<Scalars['Boolean']['output']>;
  updateClubStrategyPermission?: Maybe<ClubStrategy>;
  addClubAdmin: ClubAdmin;
  addClubPartner: ClubAdmin;
  updateUserClubLastSeen: UserClubLastSeen;
  requestUploadClubImage: RequestUploadImageResult;
  confirmClubImageUploaded: ConfirmImageUploadedResult;
  /** COUPON */
  redeemCouponByCode: UserCoupon;
  /** Return 'OK' when success */
  submitBusinessInquiry: Scalars['String']['output'];
  /** Get nonce for SIWE message */
  getSiweNonce: Scalars['String']['output'];
  /**
   * Verify SIWE message and signature
   * Returns auth token if verification succeeds
   */
  verifySiweMessage: SiweVerifyResult;
  /**
   * Create a payment for battlefield strategy subscription
   * use mainnet (ETHEREUM) in production, testnet (SEPOLIA) in staging
   */
  createBattlefieldStrategySubscriptionPayment: Payment;
  updateAndGetPayment: Payment;
  /** Login via third-party OAuth provider */
  loginOauth?: Maybe<LoginOauthResult>;
  loginOauthContinue?: Maybe<LoginOauthResult>;
};


export type MutationLoginArgs = {
  email?: InputMaybe<Scalars['String']['input']>;
  password: Scalars['String']['input'];
  token?: InputMaybe<Scalars['String']['input']>;
};


export type MutationLoginWithThirdPartyTokenArgs = {
  provider: UserThirdPartyProvider;
  exchange?: InputMaybe<Scalars['String']['input']>;
  token: Scalars['String']['input'];
};


export type MutationRefetchThirdPartyApiKeyArgs = {
  exchange?: InputMaybe<Scalars['String']['input']>;
};


export type MutationSignupArgs = {
  user?: InputMaybe<UserInput>;
  locale?: InputMaybe<SupportedLocale>;
  referralCode?: InputMaybe<Scalars['String']['input']>;
};


export type MutationEnterReferralCodeArgs = {
  referralCode?: InputMaybe<Scalars['String']['input']>;
};


export type MutationChangeLocaleArgs = {
  locale: SupportedLocale;
};


export type MutationChangeUserRoleArgs = {
  role: UserRole;
};


export type MutationResendActivationEmailArgs = {
  email: Scalars['String']['input'];
};


export type MutationActivateAccountArgs = {
  token: Scalars['String']['input'];
};


export type MutationRequestResetAccountPasswordArgs = {
  email: Scalars['String']['input'];
};


export type MutationResetAccountPasswordArgs = {
  token: Scalars['String']['input'];
  password: Scalars['String']['input'];
};


export type MutationChangePasswordArgs = {
  password?: InputMaybe<ChangePasswordInput>;
};


export type MutationUpdateUserArgs = {
  user?: InputMaybe<UpdateUserInput>;
};


export type MutationUpdateUserFirstNameArgs = {
  name: Scalars['String']['input'];
};


export type MutationLaunchBacktestArgs = {
  backtestInput: LaunchBacktestInput;
};


export type MutationLaunchSimulationArgs = {
  simulationInput: LaunchSimulationInput;
};


export type MutationLaunchTraderArgs = {
  traderInput: LaunchTraderInput;
};


export type MutationStopBrokerArgs = {
  brokerId: Scalars['ID']['input'];
};


export type MutationStopTaskArgs = {
  taskId: Scalars['ID']['input'];
};


export type MutationDeleteBrokerTaskArgs = {
  taskId: Scalars['ID']['input'];
};


export type MutationDeleteAllFinishedBrokerTasksArgs = {
  strategyId: Scalars['ID']['input'];
  brokerType: BrokerType;
};


export type MutationDeleteBacktestArgs = {
  backtestId: Scalars['ID']['input'];
};


export type MutationDeleteSimulationArgs = {
  taskId: Scalars['ID']['input'];
};


export type MutationDeleteTraderArgs = {
  taskId: Scalars['ID']['input'];
};


export type MutationUpdateTraderUserSettingArgs = {
  taskId: Scalars['ID']['input'];
  userSetting: UpdateTraderUserSettingInput;
};


export type MutationCreateStrategyArgs = {
  strategy: StrategyInput;
};


export type MutationCreateStrategyByTemplateArgs = {
  templateId?: InputMaybe<Scalars['ID']['input']>;
  strategy?: InputMaybe<StrategyTemplateInput>;
  recipe?: InputMaybe<Scalars['JSON']['input']>;
};


export type MutationChangeStrategyTemplateArgs = {
  templateId?: InputMaybe<Scalars['ID']['input']>;
  strategyId?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationDuplicateStrategyArgs = {
  strategyId: Scalars['ID']['input'];
};


export type MutationUpdateStrategyArgs = {
  strategyId: Scalars['ID']['input'];
  strategy: StrategyInput;
};


export type MutationCreateStrategyTaskConfigArgs = {
  strategyId: Scalars['ID']['input'];
  strategyTaskConfigInput: StrategyTaskConfigInput;
};


export type MutationUpdateStrategyTaskConfigArgs = {
  strategyTaskConfigInput: StrategyTaskConfigInput;
};


export type MutationDeleteStrategyTaskConfigArgs = {
  strategyTaskConfigId: Scalars['ID']['input'];
};


export type MutationDeleteStrategyArgs = {
  strategyId: Scalars['ID']['input'];
};


export type MutationCreateExchangeApiKeyArgs = {
  apiKey: Scalars['String']['input'];
  apiSecret: Scalars['String']['input'];
  apiPassword?: InputMaybe<Scalars['String']['input']>;
  comment: Scalars['String']['input'];
  exchange: SupportedExchange;
  subaccount?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteExchangeApiKeyArgs = {
  exchangeApiKeyId: Scalars['ID']['input'];
};


export type MutationCreateUserWatchlistArgs = {
  name: Scalars['String']['input'];
};


export type MutationRenameUserWatchlistArgs = {
  userWatchlistId: Scalars['ID']['input'];
  name: Scalars['String']['input'];
};


export type MutationDeleteUserWatchlistArgs = {
  userWatchlistId: Scalars['ID']['input'];
};


export type MutationAddBattlefieldStrategyToWatchlistArgs = {
  battlefieldStrategyId: Scalars['ID']['input'];
  userWatchlistId?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationDeleteBattlefieldStrategyFromWatchlistArgs = {
  battlefieldStrategyId: Scalars['ID']['input'];
  userWatchlistId?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationRequestUploadImageArgs = {
  imageType: ImageType;
};


export type MutationRequestUploadOctetStreamArgs = {
  fileType: OctetStreamType;
};


export type MutationConfirmImageUploadedArgs = {
  filename: Scalars['String']['input'];
  imageType: ImageType;
};


export type MutationChangeDefaultAvatarArgs = {
  defaultAvatar: DefaultAvatar;
};


export type MutationCreateUserWalletArgs = {
  currencyName: Scalars['String']['input'];
};


export type MutationCreateDepositAddressArgs = {
  currencyName: Scalars['String']['input'];
  tokenName?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateWithdrawalAddressArgs = {
  currencyName: Scalars['String']['input'];
  tokenName?: InputMaybe<Scalars['String']['input']>;
  label: Scalars['String']['input'];
  address: Scalars['String']['input'];
  memo?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteWithdrawalAddressArgs = {
  address: Scalars['String']['input'];
};


export type MutationWithdrawAssetsArgs = {
  otpToken?: InputMaybe<Scalars['String']['input']>;
  emailOtp: Scalars['String']['input'];
  currencyName: Scalars['String']['input'];
  tokenName?: InputMaybe<Scalars['String']['input']>;
  address: Scalars['String']['input'];
  amount: Scalars['String']['input'];
};


export type MutationRemoteStrategyHeartbeatArgs = {
  strategyToken: Scalars['String']['input'];
};


export type MutationConfirmEnable2FaArgs = {
  password: Scalars['String']['input'];
  token: Scalars['String']['input'];
};


export type MutationDisable2FaArgs = {
  password: Scalars['String']['input'];
  token: Scalars['String']['input'];
};


export type MutationRequestBindEmailOtpArgs = {
  email: Scalars['String']['input'];
};


export type MutationBindEmailArgs = {
  email: Scalars['String']['input'];
  otp: Scalars['String']['input'];
};


export type MutationFeedbackArgs = {
  email: Scalars['String']['input'];
  name: Scalars['String']['input'];
  content: Scalars['String']['input'];
};


export type MutationValidateCompetitionPasscodeArgs = {
  competitionId: Scalars['ID']['input'];
  passcode: Scalars['String']['input'];
};


export type MutationRegistryBattlefieldStrategyArgs = {
  battlefieldStrategyInput: BattlefieldStrategyInput;
  strategyId: Scalars['ID']['input'];
  competitionId?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationLockBattlefieldStrategyArgs = {
  strategyId: Scalars['ID']['input'];
  reasons?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};


export type MutationUnlockBattlefieldStrategyArgs = {
  strategyId: Scalars['ID']['input'];
  reasons?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};


export type MutationCancelStrategyRegistryArgs = {
  strategyId: Scalars['ID']['input'];
  reasons?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};


export type MutationOffboardStrategyArgs = {
  strategyId: Scalars['ID']['input'];
  reasons?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};


export type MutationAckRejectionArgs = {
  strategyId: Scalars['ID']['input'];
};


export type MutationAckOffboardingArgs = {
  strategyId: Scalars['ID']['input'];
};


export type MutationCreateBattlefieldStrategyCommentArgs = {
  battlefieldStrategyId: Scalars['ID']['input'];
  battlefieldStrategyCommentInput: BattlefieldStrategyCommentInput;
};


export type MutationEditBattlefieldStrategyCommentArgs = {
  battlefieldStrategyCommentInput: BattlefieldStrategyCommentInput;
};


export type MutationDeleteBattlefieldStrategyCommentArgs = {
  battlefieldStrategyCommentId: Scalars['ID']['input'];
};


export type MutationLikeBattlefieldStrategyCommentArgs = {
  battlefieldStrategyCommentId: Scalars['ID']['input'];
};


export type MutationUnlikeBattlefieldStrategyCommentArgs = {
  battlefieldStrategyCommentId: Scalars['ID']['input'];
};


export type MutationFollowBattlefieldStrategyDeveloperArgs = {
  developerId: Scalars['ID']['input'];
};


export type MutationUnfollowBattlefieldStrategyDeveloperArgs = {
  developerId: Scalars['ID']['input'];
};


export type MutationRegistryCompetitionTeamArgs = {
  CompetitionTeamInput: CompetitionTeamInput;
};


export type MutationChangeUserSubscriptionPlanArgs = {
  newPlanId: Scalars['Int']['input'];
  payment: PaymentMethod;
  currencyName?: InputMaybe<Scalars['String']['input']>;
  userCouponId?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationChangeSubscriptionRenewUsedUserCouponIdArgs = {
  userCouponId?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationRedeemGiftcodeArgs = {
  giftcode?: InputMaybe<Scalars['String']['input']>;
};


export type MutationAddStrategyConnectorArgs = {
  strategyId: Scalars['ID']['input'];
  type: ConnectorTypeEnum;
  name: Scalars['String']['input'];
  recipe?: InputMaybe<Scalars['JSON']['input']>;
};


export type MutationRemoveStrategyConnectorArgs = {
  strategyId: Scalars['ID']['input'];
  connectorId: Scalars['ID']['input'];
};


export type MutationUpdateConnectorRecipeArgs = {
  strategyId: Scalars['ID']['input'];
  connectorId: Scalars['ID']['input'];
  recipe?: InputMaybe<Scalars['JSON']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};


export type MutationSetUserNotificationPreferenceArgs = {
  notificationPreference: Scalars['JSON']['input'];
};


export type MutationCreateClubArgs = {
  club: ClubInput;
};


export type MutationCreateClubMembershipPlanArgs = {
  clubMembershipPlan: ClubMembershipPlanInput;
};


export type MutationJoinClubMembershipPlanArgs = {
  clubMembershipPlanId: Scalars['Int']['input'];
  clubId: Scalars['Int']['input'];
  payment: PaymentMethod;
  currencyName?: InputMaybe<Scalars['String']['input']>;
};


export type MutationChangeClubMembershipPlanArgs = {
  newClubMembershipPlanId: Scalars['Int']['input'];
  clubId: Scalars['Int']['input'];
  payment: PaymentMethod;
  currencyName?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateUserClubMembershipArgs = {
  clubMembershipPlanId: Scalars['Int']['input'];
  clubId: Scalars['Int']['input'];
};


export type MutationDeleteUserClubMembershipArgs = {
  clubMembershipPlanId: Scalars['Int']['input'];
  clubId: Scalars['Int']['input'];
};


export type MutationUpdateClubStatsArgs = {
  clubId: Scalars['Int']['input'];
  clubStatsInput: ClubStatsInput;
};


export type MutationCreateClubStrategyArgs = {
  clubStrategyInput: ClubStrategyInput;
  clubId: Scalars['Int']['input'];
  strategyId: Scalars['Int']['input'];
};


export type MutationUpdateClubStrategyPermissionArgs = {
  clubId: Scalars['Int']['input'];
  clubStrategyId: Scalars['Int']['input'];
  permissionScope: ClubStrategyPermissionScope;
  totalAdopters?: InputMaybe<Scalars['Int']['input']>;
  publicTimeConstraintPeriod?: InputMaybe<Scalars['Int']['input']>;
  publicTimeConstraintPeriodUnit?: InputMaybe<Scalars['String']['input']>;
  clubMembershipId?: InputMaybe<Array<Scalars['Int']['input']>>;
};


export type MutationAddClubAdminArgs = {
  userId: Scalars['Int']['input'];
  clubId: Scalars['Int']['input'];
};


export type MutationAddClubPartnerArgs = {
  userId: Scalars['Int']['input'];
  clubId: Scalars['Int']['input'];
};


export type MutationUpdateUserClubLastSeenArgs = {
  clubId: Scalars['Int']['input'];
};


export type MutationRequestUploadClubImageArgs = {
  imageType: ClubImageType;
  clubId: Scalars['Int']['input'];
};


export type MutationConfirmClubImageUploadedArgs = {
  filename: Scalars['String']['input'];
  imageType: ClubImageType;
  clubId: Scalars['Int']['input'];
};


export type MutationRedeemCouponByCodeArgs = {
  code: Scalars['String']['input'];
  type?: InputMaybe<CouponType>;
};


export type MutationSubmitBusinessInquiryArgs = {
  businessInquiryInput: BusinessInquiryInput;
};


export type MutationGetSiweNonceArgs = {
  address: Scalars['String']['input'];
};


export type MutationVerifySiweMessageArgs = {
  message: Scalars['String']['input'];
  signature: Scalars['String']['input'];
};


export type MutationCreateBattlefieldStrategySubscriptionPaymentArgs = {
  battlefieldStrategySubscriptionId: Scalars['ID']['input'];
  network?: InputMaybe<PaymentNetwork>;
};


export type MutationUpdateAndGetPaymentArgs = {
  paymentId: Scalars['ID']['input'];
};


export type MutationLoginOauthArgs = {
  code: Scalars['String']['input'];
  provider: Scalars['String']['input'];
  force?: InputMaybe<Scalars['Boolean']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
  extraInfo?: InputMaybe<Scalars['JSON']['input']>;
};


export type MutationLoginOauthContinueArgs = {
  provider: Scalars['String']['input'];
  oauthSessionId: Scalars['String']['input'];
  username?: InputMaybe<Scalars['String']['input']>;
};

export type Subscription = {
  __typename?: 'Subscription';
  brokerTaskStatusChanged: BrokerTask;
  remoteStrategyOnNewTask: RemoteStrategyOnNewTaskMsg;
  strategyStatusChanged: Strategy;
};


export type SubscriptionBrokerTaskStatusChangedArgs = {
  strategyId?: InputMaybe<Scalars['ID']['input']>;
  taskType?: InputMaybe<BrokerType>;
};


export type SubscriptionStrategyStatusChangedArgs = {
  strategyId?: InputMaybe<Scalars['ID']['input']>;
};

export enum UserRole {
  Investor = 'INVESTOR',
  Quant = 'QUANT'
}

export enum UserThirdPartyProvider {
  Chainup = 'CHAINUP'
}

export enum SortOrder {
  Asc = 'ASC',
  Desc = 'DESC'
}

export enum ImageType {
  Avatar = 'AVATAR',
  GovIdFront = 'GOV_ID_FRONT',
  GovIdBack = 'GOV_ID_BACK',
  Portrait = 'PORTRAIT'
}

export enum ClubImageType {
  Avatar = 'AVATAR',
  HeroImage = 'HERO_IMAGE'
}

export enum DefaultAvatar {
  Default_1 = 'DEFAULT_1',
  Default_2 = 'DEFAULT_2',
  Default_3 = 'DEFAULT_3',
  Default_4 = 'DEFAULT_4',
  Default_5 = 'DEFAULT_5',
  Default_6 = 'DEFAULT_6',
  Default_7 = 'DEFAULT_7'
}

export enum StrategyTemplateType {
  TechnicalIndicator = 'TECHNICAL_INDICATOR',
  Tradingview = 'TRADINGVIEW',
  Api = 'API',
  SklearnModel = 'SKLEARN_MODEL'
}

export enum OctetStreamType {
  SklearnModel = 'SKLEARN_MODEL'
}

export enum ConnectorTypeEnum {
  SklearnModel = 'SKLEARN_MODEL',
  Tradingview = 'TRADINGVIEW',
  Api = 'API'
}

export enum ConnectorBoundType {
  Inbound = 'INBOUND',
  Outbound = 'OUTBOUND'
}

export enum CacheControlScope {
  Public = 'PUBLIC',
  Private = 'PRIVATE'
}

export enum SupportedFiat {
  Usd = 'USD',
  Eur = 'EUR',
  Jpy = 'JPY',
  Gbp = 'GBP',
  Chf = 'CHF',
  Cad = 'CAD',
  Aud = 'AUD',
  Nzd = 'NZD',
  Zar = 'ZAR',
  Twd = 'TWD'
}

export enum SupportedLocale {
  ZhTw = 'zh_TW',
  EnUs = 'en_US'
}

export enum SupportedExchange {
  Okex = 'OKEX',
  Okextestnet = 'OKEXTESTNET',
  Binance = 'BINANCE',
  Bitfinex = 'BITFINEX',
  Bitget = 'BITGET',
  Bybit = 'BYBIT',
  Cryptocom = 'CRYPTOCOM',
  Ftx = 'FTX',
  Huobi = 'HUOBI',
  Kraken = 'KRAKEN',
  Ace = 'ACE',
  Orderly = 'ORDERLY',
  Woo = 'WOO',
  Zke = 'ZKE'
}

export enum BrokerType {
  Backtest = 'BACKTEST',
  Simulation = 'SIMULATION',
  Trader = 'TRADER'
}

export enum BrokerLauncher {
  Editor = 'EDITOR',
  StrategyList = 'STRATEGY_LIST',
  Battlefield = 'BATTLEFIELD',
  BattlefieldVerifying = 'BATTLEFIELD_VERIFYING',
  Competition = 'COMPETITION',
  CompetitionVerifying = 'COMPETITION_VERIFYING'
}

export enum BrokerStatus {
  Pending = 'PENDING',
  Running = 'RUNNING',
  Stopped = 'STOPPED',
  Finished = 'FINISHED',
  Error = 'ERROR'
}

export enum BattlefieldStrategyCommentReactionType {
  Like = 'LIKE'
}

export enum BrokerResult {
  Sigterm = 'SIGTERM',
  Userstop = 'USERSTOP',
  Systemstop = 'SYSTEMSTOP',
  Stoploss = 'STOPLOSS',
  Lockgain = 'LOCKGAIN',
  Riskcontrol = 'RISKCONTROL',
  SubscriptionExpired = 'SUBSCRIPTION_EXPIRED',
  PaymentExpired = 'PAYMENT_EXPIRED',
  Error = 'ERROR'
}

export enum StrategyInvestmentType {
  Spot = 'SPOT',
  UsdMFutures = 'USD_M_FUTURES',
  CoinMFutures = 'COIN_M_FUTURES'
}

export enum StrategyInvestmentTrend {
  Long = 'LONG',
  Short = 'SHORT',
  LongAndShort = 'LONG_AND_SHORT'
}

export enum LogType {
  Debug = 'DEBUG',
  Info = 'INFO',
  Notice = 'NOTICE',
  SystemInfo = 'SYSTEM_INFO',
  ExchangeError = 'EXCHANGE_ERROR',
  SystemError = 'SYSTEM_ERROR'
}

export enum StrategyType {
  SinglePair = 'SINGLE_PAIR',
  MultiPair = 'MULTI_PAIR'
}

export enum StrategyOptionType {
  Number = 'NUMBER',
  Boolean = 'BOOLEAN',
  String = 'STRING',
  Selected = 'SELECTED'
}

export enum OrderType {
  Limit = 'LIMIT',
  Market = 'MARKET'
}

export enum OrderStatus {
  New = 'NEW',
  PartiallyFilled = 'PARTIALLY_FILLED',
  Filled = 'FILLED',
  Canceled = 'CANCELED'
}

export enum BattlefieldStrategyType {
  Arena = 'ARENA',
  Club = 'CLUB',
  Competition = 'COMPETITION',
  Chainup = 'CHAINUP'
}

export enum BattlefieldStrategyChargingMethod {
  Free = 'FREE',
  MonthlySubscription = 'MONTHLY_SUBSCRIPTION',
  ProfitSharing = 'PROFIT_SHARING',
  MonthlySubscriptionOrProfitSharing = 'MONTHLY_SUBSCRIPTION_OR_PROFIT_SHARING',
  CaSubscription = 'CA_SUBSCRIPTION'
}

export enum BattlefieldStrategyDuration {
  ShortTerm = 'SHORT_TERM',
  MediumTerm = 'MEDIUM_TERM',
  LongTerm = 'LONG_TERM'
}

export enum BrokerChargingMethod {
  Free = 'FREE',
  MonthlySubscription = 'MONTHLY_SUBSCRIPTION',
  ProfitSharing = 'PROFIT_SHARING',
  CaSubscription = 'CA_SUBSCRIPTION'
}

export enum BrokerActivityType {
  BuyExecuted = 'BUY_EXECUTED',
  SellExecuted = 'SELL_EXECUTED',
  OpenLongExecuted = 'OPEN_LONG_EXECUTED',
  CloseLongExecuted = 'CLOSE_LONG_EXECUTED',
  OpenShortExecuted = 'OPEN_SHORT_EXECUTED',
  CloseShortExecuted = 'CLOSE_SHORT_EXECUTED',
  UserMessage = 'USER_MESSAGE',
  SystemMessage = 'SYSTEM_MESSAGE'
}

export enum SupportStrategyLanguage {
  Js = 'JS',
  Python = 'PYTHON'
}

export enum SocialLinkType {
  Facebook = 'FACEBOOK',
  Instagram = 'INSTAGRAM',
  Medium = 'MEDIUM',
  Discord = 'DISCORD',
  Telegram = 'TELEGRAM',
  Twitter = 'TWITTER',
  Website = 'WEBSITE',
  Tradingview = 'TRADINGVIEW'
}

/** TASK_OP: use field task to query related task & strategy data */
export enum NotificationType {
  TaskOp = 'TASK_OP',
  Deposit = 'DEPOSIT',
  BattlefieldStrategy = 'BATTLEFIELD_STRATEGY'
}

/** Only VERIFIED strategies or self strategies can be launch */
export enum StrategyStatus {
  Dev = 'DEV',
  Registry = 'REGISTRY',
  Aborted = 'ABORTED',
  Rejected = 'REJECTED',
  Verified = 'VERIFIED',
  Onboard = 'ONBOARD',
  Locked = 'LOCKED',
  Offboard = 'OFFBOARD'
}

export enum CompetitionType {
  Typical = 'TYPICAL',
  Team = 'TEAM',
  Individual = 'INDIVIDUAL'
}

export enum CompetitionStatus {
  NotStart = 'NOT_START',
  Started = 'STARTED',
  Registering = 'REGISTERING',
  Ranking = 'RANKING',
  End = 'END'
}

export enum CompetitionTeamMemberRole {
  Leader = 'LEADER',
  Member = 'MEMBER'
}

export enum PaymentMethod {
  CaWallet = 'CA_WALLET',
  CreditCard = 'CREDIT_CARD'
}

export enum ExchangeApiKeyStatus {
  None = 'NONE',
  LiveTrading = 'LIVE_TRADING',
  Invalid = 'INVALID'
}

export enum UserWalletHistoryType {
  Deposit = 'DEPOSIT',
  Withdraw = 'WITHDRAW',
  WithdrawLock = 'WITHDRAW_LOCK',
  WithdrawUnlock = 'WITHDRAW_UNLOCK',
  WithdrawFee = 'WITHDRAW_FEE',
  WithdrawFeeLock = 'WITHDRAW_FEE_LOCK',
  WithdrawFeeUnlock = 'WITHDRAW_FEE_UNLOCK',
  Subscription = 'SUBSCRIPTION',
  ProfitSplitLock = 'PROFIT_SPLIT_LOCK',
  ProfitSplitUnlock = 'PROFIT_SPLIT_UNLOCK',
  ProfitSplitSent = 'PROFIT_SPLIT_SENT',
  ProfitSplitReceive = 'PROFIT_SPLIT_RECEIVE',
  ProfitSplitCharge = 'PROFIT_SPLIT_CHARGE',
  System = 'SYSTEM'
}

export enum PaymentStatus {
  Unreceived = 'UNRECEIVED',
  Underpayment = 'UNDERPAYMENT',
  Processing = 'PROCESSING',
  Completed = 'COMPLETED',
  Expired = 'EXPIRED'
}

export enum PaymentNetwork {
  Ethereum = 'ETHEREUM',
  Sepolia = 'SEPOLIA',
  ArbitrumOne = 'ARBITRUM_ONE',
  ArbitrumSepolia = 'ARBITRUM_SEPOLIA'
}

export type Connector = {
  __typename?: 'Connector';
  id: Scalars['ID']['output'];
  owner: User;
  name: Scalars['String']['output'];
  recipe: Scalars['JSON']['output'];
  type: ConnectorType;
};

export type ConnectorType = {
  __typename?: 'ConnectorType';
  id: Scalars['ID']['output'];
  bound: ConnectorBoundType;
  supportedBrokerTypes: Array<Maybe<BrokerType>>;
  name: ConnectorTypeEnum;
  recipe: Scalars['JSON']['output'];
};

export enum IsLivetradableReasonType {
  Granted = 'GRANTED',
  BattlefieldStrategyLimitExceeded = 'BATTLEFIELD_STRATEGY_LIMIT_EXCEEDED',
  CaSubscriptionLivetradeLimitExceeded = 'CA_SUBSCRIPTION_LIVETRADE_LIMIT_EXCEEDED',
  CaSubscriptionLevelValidationError = 'CA_SUBSCRIPTION_LEVEL_VALIDATION_ERROR',
  ClubMembershipLivetradeLimitExceeded = 'CLUB_MEMBERSHIP_LIVETRADE_LIMIT_EXCEEDED',
  ClubMembershipLevelValidationError = 'CLUB_MEMBERSHIP_LEVEL_VALIDATION_ERROR',
  ReferrerValidationError = 'REFERRER_VALIDATION_ERROR',
  UnexpectedError = 'UNEXPECTED_ERROR',
  TradingPairInUseError = 'TRADING_PAIR_IN_USE_ERROR',
  UserNotLoggedInError = 'USER_NOT_LOGGED_IN_ERROR'
}

export type IsLivetradableForCurrentUserResult = {
  __typename?: 'IsLivetradableForCurrentUserResult';
  isLivetradable: Scalars['Boolean']['output'];
  reasonType: IsLivetradableReasonType;
  reason: Scalars['String']['output'];
};

export enum VerifiedStatusLevel {
  Yellow = 'YELLOW',
  White = 'WHITE',
  DarkBlue = 'DARK_BLUE'
}

export type Strategy = {
  __typename?: 'Strategy';
  id: Scalars['ID']['output'];
  owner: User;
  name?: Maybe<Scalars['String']['output']>;
  code?: Maybe<Scalars['String']['output']>;
  isBacktestable: Scalars['Boolean']['output'];
  isLivetradableForCurrentUser: IsLivetradableForCurrentUserResult;
  desc: Scalars['String']['output'];
  investmentType?: Maybe<StrategyInvestmentType>;
  investmentTrend?: Maybe<StrategyInvestmentTrend>;
  leverage: Scalars['Int']['output'];
  templateName?: Maybe<Scalars['String']['output']>;
  templateSerialNumber?: Maybe<Scalars['Int']['output']>;
  isParameterized: Scalars['Boolean']['output'];
  isRemote: Scalars['Boolean']['output'];
  version: Scalars['Int']['output'];
  targetCurrency?: Maybe<Scalars['String']['output']>;
  baseCurrency?: Maybe<Scalars['String']['output']>;
  baseExchange?: Maybe<SupportedExchange>;
  /**
   * Mark remote strategy is live or not
   * Remote strategy call remoteStrategyHeartbeat with remoteToken to mark strategy is live
   */
  isConnected?: Maybe<Scalars['Boolean']['output']>;
  isBinary: Scalars['Boolean']['output'];
  binaryFilename?: Maybe<Scalars['String']['output']>;
  language?: Maybe<SupportStrategyLanguage>;
  remoteToken?: Maybe<Scalars['String']['output']>;
  type: StrategyType;
  note?: Maybe<Scalars['String']['output']>;
  status: StrategyStatus;
  isBacktesting: Scalars['Boolean']['output'];
  isSimulating: Scalars['Boolean']['output'];
  isTrading: Scalars['Boolean']['output'];
  updatedAt: Scalars['Date']['output'];
  createdAt: Scalars['Date']['output'];
  options?: Maybe<Array<StrategyOption>>;
  strategyTaskConfigs?: Maybe<Array<StrategyTaskConfig>>;
  /** get linked club strategy */
  clubStrategy?: Maybe<ClubStrategy>;
  /** get linked battlefield strategy */
  battlefieldStrategy?: Maybe<BattlefieldStrategy>;
  backtestTasks: Array<BacktestTask>;
  backtestTasksCursor?: Maybe<BacktestTaskConnection>;
  simulationTasksCursor?: Maybe<SimulationTaskConnection>;
  traderTasksCursor?: Maybe<TraderTaskConnection>;
  simulationTasks: Array<SimulationTask>;
  traderTasks: Array<TraderTask>;
  verifiedStatus?: Maybe<VerifiedStatusLevel>;
};


export type StrategyBacktestTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  onlyEditor?: InputMaybe<Scalars['Boolean']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type StrategyBacktestTasksCursorArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<BrokerTaskFilterBy>;
  orderBy?: InputMaybe<BrokerTaskOrderBy>;
};


export type StrategySimulationTasksCursorArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<BrokerTaskFilterBy>;
  orderBy?: InputMaybe<BrokerTaskOrderBy>;
};


export type StrategyTraderTasksCursorArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  filterBy?: InputMaybe<BrokerTaskFilterBy>;
  orderBy?: InputMaybe<BrokerTaskOrderBy>;
};


export type StrategySimulationTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type StrategyTraderTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};

export type StrategyOption = {
  __typename?: 'StrategyOption';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  type: StrategyOptionType;
  defaultValue: Scalars['String']['output'];
  desc: Scalars['String']['output'];
  tips?: Maybe<Scalars['String']['output']>;
};

export type StrategyTaskConfig = {
  __typename?: 'StrategyTaskConfig';
  id: Scalars['ID']['output'];
  label: Scalars['String']['output'];
  rangeStart?: Maybe<Scalars['Date']['output']>;
  rangeEnd?: Maybe<Scalars['Date']['output']>;
  spread?: Maybe<Scalars['Float']['output']>;
  fee?: Maybe<Scalars['Float']['output']>;
  initBaseAmount?: Maybe<Scalars['Float']['output']>;
};

export type StrategyTemplate = {
  __typename?: 'StrategyTemplate';
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
  code?: Maybe<Scalars['String']['output']>;
  desc?: Maybe<Scalars['String']['output']>;
  note?: Maybe<Scalars['String']['output']>;
  investmentType?: Maybe<StrategyInvestmentType>;
  investmentTrend?: Maybe<StrategyInvestmentTrend>;
  locale?: Maybe<Scalars['String']['output']>;
  language?: Maybe<SupportStrategyLanguage>;
  isParameterized: Scalars['Boolean']['output'];
  isBinary: Scalars['Boolean']['output'];
  type: StrategyType;
  targetCurrency?: Maybe<Scalars['String']['output']>;
  baseCurrency?: Maybe<Scalars['String']['output']>;
  baseExchange?: Maybe<SupportedExchange>;
  templateType?: Maybe<StrategyTemplateType>;
};

export type UserWatchlistItems = {
  __typename?: 'UserWatchlistItems';
  battlefieldStrategies: Array<BattlefieldStrategy>;
};

export type ExchangeAsset = {
  __typename?: 'ExchangeAsset';
  currency: Scalars['String']['output'];
  amount: Scalars['Float']['output'];
  lockedAmount: Scalars['Float']['output'];
  /** Notice: when failed to calculated to equivalent eth value(coinmarket maintain or something wrong), just return null */
  equivalentEthAmount?: Maybe<Scalars['Float']['output']>;
  equivalentUsdAmount?: Maybe<Scalars['Float']['output']>;
};

export type NotificationEntry = {
  __typename?: 'NotificationEntry';
  time: Scalars['Date']['output'];
  message: Scalars['String']['output'];
  unseen: Scalars['Boolean']['output'];
  strategy?: Maybe<Strategy>;
  task?: Maybe<BrokerTask>;
  type: NotificationType;
};

export type DepositAddress = {
  __typename?: 'DepositAddress';
  address: Scalars['String']['output'];
  qrcodeDataUrl: Scalars['String']['output'];
  currencyName: Scalars['String']['output'];
  tokenName?: Maybe<Scalars['String']['output']>;
  tokenSymbol?: Maybe<Scalars['String']['output']>;
};

export type WithdrawalAddresses = {
  __typename?: 'WithdrawalAddresses';
  address: Scalars['String']['output'];
  currencyName: Scalars['String']['output'];
  tokenName?: Maybe<Scalars['String']['output']>;
  tokenSymbol?: Maybe<Scalars['String']['output']>;
  label: Scalars['String']['output'];
  memo?: Maybe<Scalars['String']['output']>;
};

export type UserWalletHistory = {
  __typename?: 'UserWalletHistory';
  time: Scalars['Date']['output'];
  type: Scalars['String']['output'];
  event?: Maybe<Scalars['String']['output']>;
  balance: Scalars['String']['output'];
  balanceChange: Scalars['String']['output'];
  availableBalance: Scalars['String']['output'];
  availableBalanceChange: Scalars['String']['output'];
  txHash?: Maybe<Scalars['String']['output']>;
  txStatus?: Maybe<Scalars['String']['output']>;
  txAmount?: Maybe<Scalars['Float']['output']>;
  txNetwork?: Maybe<Scalars['String']['output']>;
  txToken?: Maybe<Scalars['String']['output']>;
  txWithdrawFee?: Maybe<Scalars['Float']['output']>;
  txFromAddress?: Maybe<Scalars['String']['output']>;
  txToAddress?: Maybe<Scalars['String']['output']>;
};

export enum TargetExchange {
  Twd = 'TWD',
  Usd = 'USD'
}

export type UserWallet = {
  __typename?: 'UserWallet';
  currencyName: Scalars['String']['output'];
  balance: Scalars['String']['output'];
  availableBalance: Scalars['String']['output'];
  lockedSplit: Scalars['String']['output'];
  paidSplit: Scalars['String']['output'];
  receivedSplit: Scalars['String']['output'];
  receivedSplitFromStrategy: Scalars['String']['output'];
  receivedSplitFromReferral: Scalars['String']['output'];
  strategyManagementFee: Scalars['String']['output'];
  subscriptionFee: Scalars['String']['output'];
  depositAddresses?: Maybe<Array<DepositAddress>>;
  withdrawalAddresses?: Maybe<Array<WithdrawalAddresses>>;
  exchangeRate: Scalars['Float']['output'];
};


export type UserWalletExchangeRateArgs = {
  target?: InputMaybe<TargetExchange>;
};

export type SubscriptionPlan = {
  __typename?: 'SubscriptionPlan';
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  description: Scalars['String']['output'];
  price?: Maybe<Scalars['Float']['output']>;
  priceUnit?: Maybe<Scalars['String']['output']>;
  period?: Maybe<Scalars['Int']['output']>;
  periodUnit?: Maybe<Scalars['String']['output']>;
  level?: Maybe<Scalars['Int']['output']>;
  apiKeyLimits: Scalars['Int']['output'];
  backtestLimits: Scalars['Int']['output'];
  simulationLimits: Scalars['Int']['output'];
  livetradeLimits: Scalars['Int']['output'];
  tradingVolumeLimits: Scalars['Int']['output'];
  adoptedStrategyChargeRate: Scalars['Float']['output'];
  originalPricePerMonth?: Maybe<Scalars['String']['output']>;
  discountedPricePerMonth?: Maybe<Scalars['String']['output']>;
  strategyTemplatesNumber?: Maybe<Scalars['Int']['output']>;
};

export type UserSubscription = {
  __typename?: 'UserSubscription';
  plan: SubscriptionPlan;
  startedDate?: Maybe<Scalars['Date']['output']>;
  expiredDate?: Maybe<Scalars['Date']['output']>;
  renewedDate?: Maybe<Scalars['Date']['output']>;
  renewPlan?: Maybe<SubscriptionPlan>;
  isSufficientForRenewal: Scalars['Boolean']['output'];
  contractedAdoptedStrategyChargeRate?: Maybe<Scalars['Float']['output']>;
};

export type ChangeUserSubscriptionPlanInfo = {
  __typename?: 'ChangeUserSubscriptionPlanInfo';
  currentSubscription: UserSubscription;
  newSubscription: UserSubscription;
  resumedSubscription?: Maybe<UserSubscription>;
  isImmediateEffect: Scalars['Boolean']['output'];
  originalPaymentAmount?: Maybe<Scalars['Float']['output']>;
  paymentAmount?: Maybe<Scalars['Float']['output']>;
  paymentUnit?: Maybe<Scalars['String']['output']>;
  nextOriginalPaymentAmount?: Maybe<Scalars['Float']['output']>;
  nextPaymentAmount?: Maybe<Scalars['Float']['output']>;
  nextPaymentUnit?: Maybe<Scalars['String']['output']>;
};

export type UserEvmAddress = {
  __typename?: 'UserEvmAddress';
  address: Scalars['String']['output'];
  isPrimary: Scalars['Boolean']['output'];
};

export type User = {
  id: Scalars['ID']['output'];
  username: Scalars['String']['output'];
  avatarUrl: Scalars['String']['output'];
  description: Scalars['String']['output'];
  totalFollowers: Scalars['Int']['output'];
  isFollowedByCurrentUser?: Maybe<Scalars['Boolean']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  passwordStrength: Scalars['String']['output'];
  referrer?: Maybe<User>;
  telegramBotChatId?: Maybe<Scalars['Float']['output']>;
  telegramBotActivateToken?: Maybe<Scalars['String']['output']>;
  notificationPreference: Scalars['JSON']['output'];
  locale: SupportedLocale;
  role: UserRole;
  referralCode: Scalars['String']['output'];
  kycStatus?: Maybe<Scalars['String']['output']>;
  kycComment?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  middleName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  isEnable2Fa: Scalars['Boolean']['output'];
  /** The very first time query this field will be true, only become false after the first query to this field */
  isFirstTimeLoggedIn: Scalars['Boolean']['output'];
  /** get strategies created by the user, get all strategy with query { getStrategies } */
  strategies: Array<Strategy>;
  backtestTasks: Array<BacktestTask>;
  simulationTasks: Array<SimulationTask>;
  traderTasks: Array<TraderTask>;
  traderTasksFilterOptions: TraderTasksFilterOptionsResult;
  traderTasksCountByFilter: Scalars['Int']['output'];
  traderTasksByFilter: Array<TraderTask>;
  traderTasksCursor: TraderTaskConnection;
  exchangeApiKeys: Array<ExchangeApiKey>;
  userWallets?: Maybe<Array<UserWallet>>;
  userWalletHistories?: Maybe<Array<UserWalletHistory>>;
  watchlist: UserWatchlistItems;
  userWatchlists: Array<Maybe<UserWatchlist>>;
  watchlistBattlefieldStrategiesCountByFilter: Scalars['Int']['output'];
  watchlistBattlefieldStrategiesByFilter: Array<BattlefieldStrategy>;
  /** @deprecated Use `getNotification` with pagination support */
  notifications: Array<NotificationEntry>;
  battlefieldStrategies: Array<BattlefieldStrategy>;
  subscriptionPlan: SubscriptionPlan;
  subscription: UserSubscription;
  subscriptionRenewUsedUserCouponId?: Maybe<Scalars['ID']['output']>;
  apiKeyUsage: Scalars['Int']['output'];
  backtestUsage: Scalars['Int']['output'];
  simulationUsage: Scalars['Int']['output'];
  livetradeUsage: Scalars['Int']['output'];
  canCreateClub: Scalars['Boolean']['output'];
  isClubMember: Scalars['Boolean']['output'];
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
  /** EVM addresses associated with the user */
  userEvmAddresses: Array<UserEvmAddress>;
  /** Get user's payments */
  payments: Array<Payment>;
  /** Get user's battlefield strategy subscriptions */
  battlefieldStrategySubscriptions: Array<BattlefieldStrategySubscription>;
};


export type UserBacktestTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  onlyEditor?: InputMaybe<Scalars['Boolean']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type UserSimulationTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type UserTraderTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type UserTraderTasksCountByFilterArgs = {
  filter?: InputMaybe<TraderTaskFilterInput>;
};


export type UserTraderTasksByFilterArgs = {
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  filter?: InputMaybe<TraderTaskFilterInput>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type UserTraderTasksCursorArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<TraderTaskFilterInput>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type UserExchangeApiKeysArgs = {
  exchangeApiKeyId?: InputMaybe<Scalars['Int']['input']>;
};


export type UserUserWalletHistoriesArgs = {
  page: Scalars['Int']['input'];
  countsPerPage: Scalars['Int']['input'];
  currencyName?: InputMaybe<Scalars['String']['input']>;
  historyTypes?: InputMaybe<Array<InputMaybe<UserWalletHistoryType>>>;
};


export type UserWatchlistBattlefieldStrategiesCountByFilterArgs = {
  filter?: InputMaybe<BattlefieldStrategyFilterInput>;
  userWatchlistId?: InputMaybe<Scalars['ID']['input']>;
};


export type UserWatchlistBattlefieldStrategiesByFilterArgs = {
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  filter?: InputMaybe<BattlefieldStrategyFilterInput>;
  userWatchlistId?: InputMaybe<Scalars['ID']['input']>;
};

export type PublicUser = User & {
  __typename?: 'PublicUser';
  id: Scalars['ID']['output'];
  username: Scalars['String']['output'];
  avatarUrl: Scalars['String']['output'];
  description: Scalars['String']['output'];
  totalFollowers: Scalars['Int']['output'];
  isFollowedByCurrentUser?: Maybe<Scalars['Boolean']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  passwordStrength: Scalars['String']['output'];
  referrer?: Maybe<User>;
  telegramBotChatId?: Maybe<Scalars['Float']['output']>;
  telegramBotActivateToken?: Maybe<Scalars['String']['output']>;
  notificationPreference: Scalars['JSON']['output'];
  locale: SupportedLocale;
  role: UserRole;
  referralCode: Scalars['String']['output'];
  kycStatus?: Maybe<Scalars['String']['output']>;
  kycComment?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  middleName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  isEnable2Fa: Scalars['Boolean']['output'];
  /** The very first time query this field will be true, only become false after the first query to this field */
  isFirstTimeLoggedIn: Scalars['Boolean']['output'];
  /** get strategies created by the user, get all strategy with query { getStrategies } */
  strategies: Array<Strategy>;
  backtestTasks: Array<BacktestTask>;
  simulationTasks: Array<SimulationTask>;
  traderTasks: Array<TraderTask>;
  traderTasksFilterOptions: TraderTasksFilterOptionsResult;
  traderTasksCountByFilter: Scalars['Int']['output'];
  traderTasksByFilter: Array<TraderTask>;
  traderTasksCursor: TraderTaskConnection;
  exchangeApiKeys: Array<ExchangeApiKey>;
  userWallets?: Maybe<Array<UserWallet>>;
  userWalletHistories?: Maybe<Array<UserWalletHistory>>;
  watchlist: UserWatchlistItems;
  userWatchlists: Array<Maybe<UserWatchlist>>;
  watchlistBattlefieldStrategiesCountByFilter: Scalars['Int']['output'];
  watchlistBattlefieldStrategiesByFilter: Array<BattlefieldStrategy>;
  /** @deprecated Use `getNotification` with pagination support */
  notifications: Array<NotificationEntry>;
  battlefieldStrategies: Array<BattlefieldStrategy>;
  subscriptionPlan: SubscriptionPlan;
  subscription: UserSubscription;
  subscriptionRenewUsedUserCouponId?: Maybe<Scalars['ID']['output']>;
  apiKeyUsage: Scalars['Int']['output'];
  backtestUsage: Scalars['Int']['output'];
  simulationUsage: Scalars['Int']['output'];
  livetradeUsage: Scalars['Int']['output'];
  createdClubs: Array<Club>;
  clubAdmins: Array<ClubAdmin>;
  canCreateClub: Scalars['Boolean']['output'];
  isClubMember: Scalars['Boolean']['output'];
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
  /** EVM addresses associated with the user */
  userEvmAddresses: Array<UserEvmAddress>;
  /** Get user's payments */
  payments: Array<Payment>;
  /** Get user's battlefield strategy subscriptions */
  battlefieldStrategySubscriptions: Array<BattlefieldStrategySubscription>;
};


export type PublicUserBacktestTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  onlyEditor?: InputMaybe<Scalars['Boolean']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type PublicUserSimulationTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type PublicUserTraderTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type PublicUserTraderTasksCountByFilterArgs = {
  filter?: InputMaybe<TraderTaskFilterInput>;
};


export type PublicUserTraderTasksByFilterArgs = {
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  filter?: InputMaybe<TraderTaskFilterInput>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type PublicUserTraderTasksCursorArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<TraderTaskFilterInput>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type PublicUserExchangeApiKeysArgs = {
  exchangeApiKeyId?: InputMaybe<Scalars['Int']['input']>;
};


export type PublicUserUserWalletHistoriesArgs = {
  page: Scalars['Int']['input'];
  countsPerPage: Scalars['Int']['input'];
  currencyName?: InputMaybe<Scalars['String']['input']>;
  historyTypes?: InputMaybe<Array<InputMaybe<UserWalletHistoryType>>>;
};


export type PublicUserWatchlistBattlefieldStrategiesCountByFilterArgs = {
  filter?: InputMaybe<BattlefieldStrategyFilterInput>;
  userWatchlistId?: InputMaybe<Scalars['ID']['input']>;
};


export type PublicUserWatchlistBattlefieldStrategiesByFilterArgs = {
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  filter?: InputMaybe<BattlefieldStrategyFilterInput>;
  userWatchlistId?: InputMaybe<Scalars['ID']['input']>;
};

export type PrivateUser = User & {
  __typename?: 'PrivateUser';
  id: Scalars['ID']['output'];
  username: Scalars['String']['output'];
  avatarUrl: Scalars['String']['output'];
  description: Scalars['String']['output'];
  totalFollowers: Scalars['Int']['output'];
  isFollowedByCurrentUser?: Maybe<Scalars['Boolean']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  passwordStrength: Scalars['String']['output'];
  referrer?: Maybe<User>;
  telegramBotChatId?: Maybe<Scalars['Float']['output']>;
  telegramBotActivateToken?: Maybe<Scalars['String']['output']>;
  notificationPreference: Scalars['JSON']['output'];
  locale: SupportedLocale;
  role: UserRole;
  referralCode: Scalars['String']['output'];
  kycStatus?: Maybe<Scalars['String']['output']>;
  kycComment?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  middleName?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  isEnable2Fa: Scalars['Boolean']['output'];
  /** The very first time query this field will be true, only become false after the first query to this field */
  isFirstTimeLoggedIn: Scalars['Boolean']['output'];
  /** get strategies created by the user, get all strategy with query { getStrategies } */
  strategies: Array<Strategy>;
  backtestTasks: Array<BacktestTask>;
  simulationTasks: Array<SimulationTask>;
  traderTasks: Array<TraderTask>;
  traderTasksFilterOptions: TraderTasksFilterOptionsResult;
  traderTasksCountByFilter: Scalars['Int']['output'];
  traderTasksByFilter: Array<TraderTask>;
  traderTasksCursor: TraderTaskConnection;
  exchangeApiKeys: Array<ExchangeApiKey>;
  userWallets?: Maybe<Array<UserWallet>>;
  userWalletHistories?: Maybe<Array<UserWalletHistory>>;
  watchlist: UserWatchlistItems;
  userWatchlists: Array<Maybe<UserWatchlist>>;
  watchlistBattlefieldStrategiesCountByFilter: Scalars['Int']['output'];
  watchlistBattlefieldStrategiesByFilter: Array<BattlefieldStrategy>;
  /** @deprecated Use `getNotification` with pagination support */
  notifications: Array<NotificationEntry>;
  battlefieldStrategies: Array<BattlefieldStrategy>;
  subscriptionPlan: SubscriptionPlan;
  subscription: UserSubscription;
  subscriptionRenewUsedUserCouponId?: Maybe<Scalars['ID']['output']>;
  apiKeyUsage: Scalars['Int']['output'];
  backtestUsage: Scalars['Int']['output'];
  simulationUsage: Scalars['Int']['output'];
  livetradeUsage: Scalars['Int']['output'];
  canCreateClub: Scalars['Boolean']['output'];
  isClubMember: Scalars['Boolean']['output'];
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
  /** EVM addresses associated with the user */
  userEvmAddresses: Array<UserEvmAddress>;
  /** Get user's payments */
  payments: Array<Payment>;
  /** Get user's battlefield strategy subscriptions */
  battlefieldStrategySubscriptions: Array<BattlefieldStrategySubscription>;
};


export type PrivateUserBacktestTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  onlyEditor?: InputMaybe<Scalars['Boolean']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type PrivateUserSimulationTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type PrivateUserTraderTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type PrivateUserTraderTasksCountByFilterArgs = {
  filter?: InputMaybe<TraderTaskFilterInput>;
};


export type PrivateUserTraderTasksByFilterArgs = {
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  filter?: InputMaybe<TraderTaskFilterInput>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type PrivateUserTraderTasksCursorArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<TraderTaskFilterInput>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type PrivateUserExchangeApiKeysArgs = {
  exchangeApiKeyId?: InputMaybe<Scalars['Int']['input']>;
};


export type PrivateUserUserWalletHistoriesArgs = {
  page: Scalars['Int']['input'];
  countsPerPage: Scalars['Int']['input'];
  currencyName?: InputMaybe<Scalars['String']['input']>;
  historyTypes?: InputMaybe<Array<InputMaybe<UserWalletHistoryType>>>;
};


export type PrivateUserWatchlistBattlefieldStrategiesCountByFilterArgs = {
  filter?: InputMaybe<BattlefieldStrategyFilterInput>;
  userWatchlistId?: InputMaybe<Scalars['ID']['input']>;
};


export type PrivateUserWatchlistBattlefieldStrategiesByFilterArgs = {
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  filter?: InputMaybe<BattlefieldStrategyFilterInput>;
  userWatchlistId?: InputMaybe<Scalars['ID']['input']>;
};

export type ExchangeApiKeyInfo = {
  __typename?: 'ExchangeApiKeyInfo';
  id: Scalars['ID']['output'];
  comment?: Maybe<Scalars['String']['output']>;
  exchange: SupportedExchange;
  subaccount?: Maybe<Scalars['String']['output']>;
  apiKey: Scalars['String']['output'];
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
};

export type ExchangeApiKey = {
  __typename?: 'ExchangeApiKey';
  id: Scalars['ID']['output'];
  comment?: Maybe<Scalars['String']['output']>;
  exchange: SupportedExchange;
  subaccount?: Maybe<Scalars['String']['output']>;
  apiKey: Scalars['String']['output'];
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
  status: ExchangeApiKeyStatus;
  exchangeAssets: Array<ExchangeAsset>;
  restrictedLeverage?: Maybe<Scalars['Int']['output']>;
};


export type ExchangeApiKeyExchangeAssetsArgs = {
  investmentType?: InputMaybe<StrategyInvestmentType>;
};


export type ExchangeApiKeyRestrictedLeverageArgs = {
  pair: Scalars['String']['input'];
  investmentType: StrategyInvestmentType;
};

export type BrokerTaskOps = {
  __typename?: 'BrokerTaskOps';
  id: Scalars['ID']['output'];
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
  time: Scalars['Date']['output'];
  exchange: Scalars['String']['output'];
  type: OrderType;
  pair: Scalars['String']['output'];
  amount: Scalars['Float']['output'];
  price: Scalars['Float']['output'];
  assets: Scalars['JSON']['output'];
  log?: Maybe<Scalars['String']['output']>;
  commissionAmount?: Maybe<Scalars['Float']['output']>;
  commissionAsset?: Maybe<Scalars['String']['output']>;
};

export type ProfitVector = {
  __typename?: 'ProfitVector';
  points: Array<ProfitVectorPoint>;
};

export type MarketPricePoint = {
  __typename?: 'MarketPricePoint';
  currency: Scalars['String']['output'];
  price: Scalars['Float']['output'];
  open?: Maybe<Scalars['Float']['output']>;
  high?: Maybe<Scalars['Float']['output']>;
  low?: Maybe<Scalars['Float']['output']>;
  close?: Maybe<Scalars['Float']['output']>;
  volume?: Maybe<Scalars['Float']['output']>;
};

export type ProfitVectorPoint = {
  __typename?: 'ProfitVectorPoint';
  time: Scalars['Date']['output'];
  profit: Scalars['Float']['output'];
  marketPrices: Array<MarketPricePoint>;
};

export type LogEntry = {
  __typename?: 'LogEntry';
  time: Scalars['Date']['output'];
  msg: Scalars['String']['output'];
  type: LogType;
};

export type BrokerActivity = {
  __typename?: 'BrokerActivity';
  time: Scalars['Date']['output'];
  type: BrokerActivityType;
  price?: Maybe<Scalars['Float']['output']>;
  amount?: Maybe<Scalars['Float']['output']>;
  /** orderExecuted: to be deprecated */
  orderExecuted?: Maybe<Scalars['Boolean']['output']>;
  orderStatus?: Maybe<OrderStatus>;
  message?: Maybe<Scalars['String']['output']>;
  exchange?: Maybe<SupportedExchange>;
  pair?: Maybe<Scalars['String']['output']>;
};

export type RemoteStrategyOnNewTaskMsg = {
  __typename?: 'RemoteStrategyOnNewTaskMsg';
  jobName: Scalars['String']['output'];
  task: BrokerTask;
};

export type AssetsValue = {
  __typename?: 'AssetsValue';
  currency: Scalars['String']['output'];
  amount: Scalars['Float']['output'];
  equivalentQuoteAmount: Scalars['Float']['output'];
};

export type MonthlyProfit = {
  __typename?: 'MonthlyProfit';
  year: Scalars['Int']['output'];
  monthIndex: Scalars['Int']['output'];
  profit: Scalars['Float']['output'];
};

export type BrokerTask = {
  id: Scalars['ID']['output'];
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
  rangeEnd?: Maybe<Scalars['Date']['output']>;
  type: BrokerType;
  options: Scalars['JSON']['output'];
  strategyInvestmentType?: Maybe<Scalars['String']['output']>;
  strategyInvestmentTrend?: Maybe<Scalars['String']['output']>;
  roi?: Maybe<Scalars['Float']['output']>;
  roiRealized?: Maybe<Scalars['Float']['output']>;
  roiUnrealized?: Maybe<Scalars['Float']['output']>;
  sharpeRatio?: Maybe<Scalars['Float']['output']>;
  mdd?: Maybe<Scalars['Float']['output']>;
  status: BrokerStatus;
  ops: Array<BrokerTaskOps>;
  strategyId: Scalars['ID']['output'];
  strategy?: Maybe<Strategy>;
  targetExchangePair: Scalars['JSON']['output'];
  profitVector: ProfitVector;
  logs: Array<LogEntry>;
  activities: ActivitiesConnection;
  performance: TaskPerformance;
  /**
   * Get activities download URL.
   * Note: Please do not query this unless you really need.
   */
  activitiesLogUrl?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['String']['output']>;
  lockGainRatio?: Maybe<Scalars['Float']['output']>;
  stopLossRatio?: Maybe<Scalars['Float']['output']>;
  initAssetsWallet: Scalars['JSON']['output'];
  initBaseAmount: Scalars['Float']['output'];
  leverage: Scalars['Int']['output'];
  baseCurrency: Scalars['String']['output'];
  baseExchange: SupportedExchange;
  strategyType: StrategyType;
  autoClosePosition?: Maybe<Scalars['Boolean']['output']>;
  profitSharingRatio?: Maybe<Scalars['Float']['output']>;
  chargingMethod?: Maybe<BrokerChargingMethod>;
  initAssetsValues?: Maybe<Array<AssetsValue>>;
  latestAssetsValues?: Maybe<Array<AssetsValue>>;
  monthlyProfits?: Maybe<Array<MonthlyProfit>>;
  exchangeApiKeys?: Maybe<Array<ExchangeApiKeyInfo>>;
  bfsSubscription?: Maybe<BattlefieldStrategySubscription>;
};


export type BrokerTaskProfitVectorArgs = {
  pointsCount?: InputMaybe<Scalars['Int']['input']>;
  period?: InputMaybe<Scalars['Int']['input']>;
  from?: InputMaybe<Scalars['Date']['input']>;
  to?: InputMaybe<Scalars['Date']['input']>;
};


export type BrokerTaskActivitiesArgs = {
  first: Scalars['Int']['input'];
  after: Scalars['String']['input'];
  activityTypes?: InputMaybe<Array<InputMaybe<BrokerActivityType>>>;
  keywords?: InputMaybe<Scalars['String']['input']>;
};

export type BacktestTask = BrokerTask & {
  __typename?: 'BacktestTask';
  id: Scalars['ID']['output'];
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
  type: BrokerType;
  launcher: BrokerLauncher;
  options: Scalars['JSON']['output'];
  strategyInvestmentType?: Maybe<Scalars['String']['output']>;
  strategyInvestmentTrend?: Maybe<Scalars['String']['output']>;
  roi?: Maybe<Scalars['Float']['output']>;
  roiRealized?: Maybe<Scalars['Float']['output']>;
  roiUnrealized?: Maybe<Scalars['Float']['output']>;
  sharpeRatio?: Maybe<Scalars['Float']['output']>;
  mdd?: Maybe<Scalars['Float']['output']>;
  status: BrokerStatus;
  ops: Array<BrokerTaskOps>;
  strategyId: Scalars['ID']['output'];
  strategy?: Maybe<Strategy>;
  targetExchangePair: Scalars['JSON']['output'];
  profitVector: ProfitVector;
  logs: Array<LogEntry>;
  activities: ActivitiesConnection;
  performance: TaskPerformance;
  /**
   * Get activities download URL.
   * Note: Please do not query this unless you really need.
   */
  activitiesLogUrl?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['String']['output']>;
  rangeStart?: Maybe<Scalars['Date']['output']>;
  rangeEnd?: Maybe<Scalars['Date']['output']>;
  progress?: Maybe<Scalars['Float']['output']>;
  lockGainRatio?: Maybe<Scalars['Float']['output']>;
  stopLossRatio?: Maybe<Scalars['Float']['output']>;
  initAssetsWallet: Scalars['JSON']['output'];
  initBaseAmount: Scalars['Float']['output'];
  leverage: Scalars['Int']['output'];
  baseCurrency: Scalars['String']['output'];
  baseExchange: SupportedExchange;
  strategyType: StrategyType;
  autoClosePosition?: Maybe<Scalars['Boolean']['output']>;
  profitSharingRatio?: Maybe<Scalars['Float']['output']>;
  chargingMethod?: Maybe<BrokerChargingMethod>;
  initAssetsValues?: Maybe<Array<AssetsValue>>;
  latestAssetsValues?: Maybe<Array<AssetsValue>>;
  monthlyProfits?: Maybe<Array<MonthlyProfit>>;
  exchangeApiKeys?: Maybe<Array<ExchangeApiKeyInfo>>;
  bfsSubscription?: Maybe<BattlefieldStrategySubscription>;
};


export type BacktestTaskProfitVectorArgs = {
  pointsCount?: InputMaybe<Scalars['Int']['input']>;
  period?: InputMaybe<Scalars['Int']['input']>;
  from?: InputMaybe<Scalars['Date']['input']>;
  to?: InputMaybe<Scalars['Date']['input']>;
};


export type BacktestTaskActivitiesArgs = {
  first: Scalars['Int']['input'];
  after: Scalars['String']['input'];
  activityTypes?: InputMaybe<Array<InputMaybe<BrokerActivityType>>>;
  keywords?: InputMaybe<Scalars['String']['input']>;
};

export type SimulationTask = BrokerTask & {
  __typename?: 'SimulationTask';
  id: Scalars['ID']['output'];
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
  rangeEnd?: Maybe<Scalars['Date']['output']>;
  type: BrokerType;
  options: Scalars['JSON']['output'];
  strategyInvestmentType?: Maybe<Scalars['String']['output']>;
  strategyInvestmentTrend?: Maybe<Scalars['String']['output']>;
  roi?: Maybe<Scalars['Float']['output']>;
  roiRealized?: Maybe<Scalars['Float']['output']>;
  roiUnrealized?: Maybe<Scalars['Float']['output']>;
  sharpeRatio?: Maybe<Scalars['Float']['output']>;
  mdd?: Maybe<Scalars['Float']['output']>;
  status: BrokerStatus;
  ops: Array<BrokerTaskOps>;
  strategyId: Scalars['ID']['output'];
  strategy?: Maybe<Strategy>;
  targetExchangePair: Scalars['JSON']['output'];
  profitVector: ProfitVector;
  logs: Array<LogEntry>;
  activities: ActivitiesConnection;
  performance: TaskPerformance;
  /**
   * Get activities download URL.
   * Note: Please do not query this unless you really need.
   */
  activitiesLogUrl?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['String']['output']>;
  lockGainRatio?: Maybe<Scalars['Float']['output']>;
  stopLossRatio?: Maybe<Scalars['Float']['output']>;
  initAssetsWallet: Scalars['JSON']['output'];
  initBaseAmount: Scalars['Float']['output'];
  leverage: Scalars['Int']['output'];
  baseCurrency: Scalars['String']['output'];
  baseExchange: SupportedExchange;
  strategyType: StrategyType;
  autoClosePosition?: Maybe<Scalars['Boolean']['output']>;
  profitSharingRatio?: Maybe<Scalars['Float']['output']>;
  chargingMethod?: Maybe<BrokerChargingMethod>;
  initAssetsValues?: Maybe<Array<AssetsValue>>;
  latestAssetsValues?: Maybe<Array<AssetsValue>>;
  monthlyProfits?: Maybe<Array<MonthlyProfit>>;
  exchangeApiKeys?: Maybe<Array<ExchangeApiKeyInfo>>;
  bfsSubscription?: Maybe<BattlefieldStrategySubscription>;
};


export type SimulationTaskProfitVectorArgs = {
  pointsCount?: InputMaybe<Scalars['Int']['input']>;
  period?: InputMaybe<Scalars['Int']['input']>;
  from?: InputMaybe<Scalars['Date']['input']>;
  to?: InputMaybe<Scalars['Date']['input']>;
};


export type SimulationTaskActivitiesArgs = {
  first: Scalars['Int']['input'];
  after: Scalars['String']['input'];
  activityTypes?: InputMaybe<Array<InputMaybe<BrokerActivityType>>>;
  keywords?: InputMaybe<Scalars['String']['input']>;
};

export type TraderTask = BrokerTask & {
  __typename?: 'TraderTask';
  id: Scalars['ID']['output'];
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
  rangeEnd?: Maybe<Scalars['Date']['output']>;
  type: BrokerType;
  options: Scalars['JSON']['output'];
  strategyInvestmentType?: Maybe<Scalars['String']['output']>;
  strategyInvestmentTrend?: Maybe<Scalars['String']['output']>;
  roi?: Maybe<Scalars['Float']['output']>;
  roiRealized?: Maybe<Scalars['Float']['output']>;
  roiUnrealized?: Maybe<Scalars['Float']['output']>;
  sharpeRatio?: Maybe<Scalars['Float']['output']>;
  mdd?: Maybe<Scalars['Float']['output']>;
  status: BrokerStatus;
  ops: Array<BrokerTaskOps>;
  strategyId: Scalars['ID']['output'];
  /** Return null if strategy is deleted */
  strategy?: Maybe<Strategy>;
  targetExchangePair: Scalars['JSON']['output'];
  profitVector: ProfitVector;
  logs: Array<LogEntry>;
  activities: ActivitiesConnection;
  performance: TaskPerformance;
  /**
   * Get activities download URL.
   * Note: Please do not query this unless you really need.
   */
  activitiesLogUrl?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Scalars['String']['output']>;
  lockGainRatio?: Maybe<Scalars['Float']['output']>;
  stopLossRatio?: Maybe<Scalars['Float']['output']>;
  initAssetsWallet: Scalars['JSON']['output'];
  initBaseAmount: Scalars['Float']['output'];
  leverage: Scalars['Int']['output'];
  initAssetsEquivalentEthAmount?: Maybe<Scalars['Float']['output']>;
  initAssetsEquivalentUsdAmount?: Maybe<Scalars['Float']['output']>;
  baseCurrency: Scalars['String']['output'];
  baseExchange: SupportedExchange;
  strategyType: StrategyType;
  autoClosePosition?: Maybe<Scalars['Boolean']['output']>;
  profitSharingRatio?: Maybe<Scalars['Float']['output']>;
  chargingMethod?: Maybe<BrokerChargingMethod>;
  initAssetsValues?: Maybe<Array<AssetsValue>>;
  latestAssetsValues?: Maybe<Array<AssetsValue>>;
  monthlyProfits?: Maybe<Array<MonthlyProfit>>;
  exchangeApiKeys?: Maybe<Array<ExchangeApiKeyInfo>>;
  bfsSubscription?: Maybe<BattlefieldStrategySubscription>;
};


export type TraderTaskProfitVectorArgs = {
  pointsCount?: InputMaybe<Scalars['Int']['input']>;
  period?: InputMaybe<Scalars['Int']['input']>;
  from?: InputMaybe<Scalars['Date']['input']>;
  to?: InputMaybe<Scalars['Date']['input']>;
};


export type TraderTaskActivitiesArgs = {
  first: Scalars['Int']['input'];
  after: Scalars['String']['input'];
  activityTypes?: InputMaybe<Array<InputMaybe<BrokerActivityType>>>;
  keywords?: InputMaybe<Scalars['String']['input']>;
};

export type TraderTasksFilterOptionsResult = {
  __typename?: 'TraderTasksFilterOptionsResult';
  exchanges?: Maybe<Array<Maybe<ExchangePairsMarket>>>;
};

export type PairsMarket = {
  __typename?: 'PairsMarket';
  symbol: Scalars['String']['output'];
  base: Scalars['String']['output'];
  quote: Scalars['String']['output'];
  settle?: Maybe<Scalars['String']['output']>;
};

export type ExchangePairsMarket = {
  __typename?: 'ExchangePairsMarket';
  exchange: SupportedExchange;
  type: StrategyInvestmentType;
  defaultFee: Scalars['Float']['output'];
  pairs: Array<Scalars['String']['output']>;
  markets?: Maybe<Array<PairsMarket>>;
};

export type ActivateAccountResult = {
  __typename?: 'ActivateAccountResult';
  isOk: Scalars['Boolean']['output'];
  user?: Maybe<PublicUser>;
};

export type RequestResetAccountPasswordResult = {
  __typename?: 'RequestResetAccountPasswordResult';
  isOk: Scalars['Boolean']['output'];
};

export type ResetAccountPasswordResult = {
  __typename?: 'ResetAccountPasswordResult';
  isOk: Scalars['Boolean']['output'];
};

export type SignupResult = {
  __typename?: 'SignupResult';
  isOk: Scalars['Boolean']['output'];
  user?: Maybe<PublicUser>;
};

export type UpdateUserResult = {
  __typename?: 'UpdateUserResult';
  isOk: Scalars['Boolean']['output'];
};

export type UpdateUserFirstNameResult = {
  __typename?: 'UpdateUserFirstNameResult';
  isOk: Scalars['Boolean']['output'];
};

export type ChangePasswordResult = {
  __typename?: 'ChangePasswordResult';
  isOk: Scalars['Boolean']['output'];
};

export type CreateUserWalletResult = {
  __typename?: 'CreateUserWalletResult';
  isOk: Scalars['Boolean']['output'];
  userWallet?: Maybe<UserWallet>;
};

export type CreateDepositAddressResult = {
  __typename?: 'CreateDepositAddressResult';
  isOk: Scalars['Boolean']['output'];
  address?: Maybe<Scalars['String']['output']>;
};

export type WithdrawResult = {
  __typename?: 'WithdrawResult';
  isOk: Scalars['Boolean']['output'];
};

export type RequestUploadImageResult = {
  __typename?: 'requestUploadImageResult';
  isOk: Scalars['Boolean']['output'];
  filename: Scalars['String']['output'];
  url: Scalars['String']['output'];
  fields: Scalars['JSON']['output'];
};

export type RequestUploadFileResult = {
  __typename?: 'requestUploadFileResult';
  isOk: Scalars['Boolean']['output'];
  filename: Scalars['String']['output'];
  url: Scalars['String']['output'];
  fields: Scalars['JSON']['output'];
};

export type ConfirmImageUploadedResult = {
  __typename?: 'ConfirmImageUploadedResult';
  isOk: Scalars['Boolean']['output'];
};

export type RemoteStrategyHeartbeatResult = {
  __typename?: 'RemoteStrategyHeartbeatResult';
  isOk: Scalars['Boolean']['output'];
};

export type RequestEnable2FaResult = {
  __typename?: 'RequestEnable2FaResult';
  isOk: Scalars['Boolean']['output'];
  qrcodeDataUrl?: Maybe<Scalars['String']['output']>;
  secret: Scalars['String']['output'];
};

export type ConfirmEnable2FaResult = {
  __typename?: 'ConfirmEnable2FaResult';
  isOk: Scalars['Boolean']['output'];
};

export type Disable2FaResult = {
  __typename?: 'Disable2FaResult';
  isOk: Scalars['Boolean']['output'];
};

export type RegistryCompetitionTeamResult = {
  __typename?: 'registryCompetitionTeamResult';
  isOk: Scalars['Boolean']['output'];
};

export type PageInfo = {
  __typename?: 'PageInfo';
  startCursor?: Maybe<Scalars['String']['output']>;
  endCursor?: Maybe<Scalars['String']['output']>;
  hasPreviousPage: Scalars['Boolean']['output'];
  hasNextPage: Scalars['Boolean']['output'];
};

export type BattlefieldStrategyCommentsEdge = {
  __typename?: 'BattlefieldStrategyCommentsEdge';
  cursor: Scalars['String']['output'];
  node: BattlefieldStrategyComment;
};

export type BattlefieldStrategyCommentsConnection = {
  __typename?: 'BattlefieldStrategyCommentsConnection';
  totalCount: Scalars['Int']['output'];
  edges: Array<BattlefieldStrategyCommentsEdge>;
  pageInfo: PageInfo;
};

export type NotificationsEdge = {
  __typename?: 'NotificationsEdge';
  cursor: Scalars['String']['output'];
  node: NotificationEntry;
};

export type NotificationsConnection = {
  __typename?: 'NotificationsConnection';
  totalCount: Scalars['Int']['output'];
  edges: Array<NotificationsEdge>;
  pageInfo: PageInfo;
};

export type ActivitiesEdge = {
  __typename?: 'ActivitiesEdge';
  cursor: Scalars['String']['output'];
  node: BrokerActivity;
};

export type ActivitiesConnection = {
  __typename?: 'ActivitiesConnection';
  totalCount: Scalars['Int']['output'];
  edges: Array<ActivitiesEdge>;
  pageInfo: PageInfo;
};

export type VersionInfo = {
  __typename?: 'VersionInfo';
  lastCommitTime: Scalars['Date']['output'];
  lastCommitHash: Scalars['String']['output'];
};

export type BattlefieldStrategyComment = {
  __typename?: 'BattlefieldStrategyComment';
  id: Scalars['ID']['output'];
  commentUser: PublicUser;
  comment?: Maybe<Scalars['String']['output']>;
  rating: Scalars['Float']['output'];
  totalLikes: Scalars['Int']['output'];
  isLikedByCurrentUser: Scalars['Boolean']['output'];
  time?: Maybe<Scalars['Date']['output']>;
};

export type BattlefieldStrategyRecord = {
  __typename?: 'BattlefieldStrategyRecord';
  event: Scalars['String']['output'];
  reasons: Array<Maybe<Scalars['String']['output']>>;
  time: Scalars['Date']['output'];
};

export type BattlefieldStrategySubscriptionPlan = {
  __typename?: 'BattlefieldStrategySubscriptionPlan';
  id: Scalars['Int']['output'];
  exchange?: Maybe<SupportedExchange>;
  originalPrice: Scalars['Float']['output'];
  discountedPrice?: Maybe<Scalars['Float']['output']>;
  priceUnit: Scalars['String']['output'];
  period: Scalars['Int']['output'];
  periodUnit: Scalars['String']['output'];
};

export type BattlefieldStrategy = {
  __typename?: 'BattlefieldStrategy';
  id: Scalars['ID']['output'];
  owner: User;
  strategy: Strategy;
  /** Null when no data */
  currentRoiRanking?: Maybe<Scalars['Int']['output']>;
  previousRoiRanking?: Maybe<Scalars['Int']['output']>;
  rankingChange?: Maybe<Scalars['Int']['output']>;
  rankingTaskId?: Maybe<Scalars['ID']['output']>;
  rankingAt?: Maybe<Scalars['Date']['output']>;
  /** 1 for 100% */
  rankingRoi?: Maybe<Scalars['Float']['output']>;
  /** 1 for 100% */
  rankingMdd?: Maybe<Scalars['Float']['output']>;
  rankingSharpeRatio?: Maybe<Scalars['Float']['output']>;
  rankingWinRate?: Maybe<Scalars['Float']['output']>;
  rankingProfitVector?: Maybe<ProfitVector>;
  /** No ranking task, no data */
  rankingTaskPerformance?: Maybe<TaskPerformance>;
  /** @deprecated Use `liveInvestmentAmount` */
  totalInvestmentAmount?: Maybe<Scalars['Float']['output']>;
  battlefieldStrategyAllowedMarkets?: Maybe<Array<Maybe<BattlefieldStrategyAllowedMarket>>>;
  suggestedExchange?: Maybe<SupportedExchange>;
  suggestedPair?: Maybe<Scalars['String']['output']>;
  suggestedMinInvestmentAmount?: Maybe<Scalars['Float']['output']>;
  suggestedTotalInvestmentAmount?: Maybe<Scalars['Float']['output']>;
  suggestedTotalAdoptors?: Maybe<Scalars['Int']['output']>;
  suggestedStopPoints?: Maybe<Scalars['Float']['output']>;
  suggestedTakeProfit?: Maybe<Scalars['Float']['output']>;
  suggestedInvestmentType?: Maybe<StrategyInvestmentType>;
  suggestedInvestmentTrend?: Maybe<StrategyInvestmentTrend>;
  suggestedLeverage: Scalars['Int']['output'];
  suggestedLeverageUpperBound: Scalars['Int']['output'];
  suggestedLeverageLowerBound: Scalars['Int']['output'];
  /**
   * profitSharingRatio * profitSharingRatioDiscount = profitSharingRatioAfterDiscount
   * value 0.05 means 5%
   * profitSharingRatioDiscount 0.8 means 20% off
   */
  profitSharingRatio: Scalars['Float']['output'];
  profitSharingRatioDiscount: Scalars['Float']['output'];
  profitSharingRatioAfterDiscount: Scalars['Float']['output'];
  strategyName?: Maybe<Scalars['String']['output']>;
  strategyDesc?: Maybe<Scalars['String']['output']>;
  strategyBacktestSummary?: Maybe<Scalars['JSON']['output']>;
  liveInvestmentAmount: Scalars['Float']['output'];
  liveAdoptorCount: Scalars['Int']['output'];
  liveProfitAndLoss: Scalars['Float']['output'];
  liveAverageRoi: Scalars['Float']['output'];
  totalLaunchedInvestmentAmount: Scalars['Float']['output'];
  totalLaunchedAdoptorCount: Scalars['Int']['output'];
  totalProfitAndLoss: Scalars['Float']['output'];
  totalAverageRoi: Scalars['Float']['output'];
  totalCommentCount?: Maybe<Scalars['Int']['output']>;
  totalTradersRoi: Scalars['Float']['output'];
  totalTradersRoiRealized: Scalars['Float']['output'];
  totalTradersRoiUnrealized: Scalars['Float']['output'];
  numberOfTradesPerMonth?: Maybe<Scalars['Int']['output']>;
  averageHoldingDays: Scalars['Float']['output'];
  averageCommentRating?: Maybe<Scalars['Float']['output']>;
  submittedAt?: Maybe<Scalars['Date']['output']>;
  abortedAt?: Maybe<Scalars['Date']['output']>;
  rejectedAt?: Maybe<Scalars['Date']['output']>;
  verifiedAt?: Maybe<Scalars['Date']['output']>;
  onboardedAt?: Maybe<Scalars['Date']['output']>;
  offboardedAt?: Maybe<Scalars['Date']['output']>;
  status?: Maybe<StrategyStatus>;
  isBacktesting: Scalars['Boolean']['output'];
  isSimulating: Scalars['Boolean']['output'];
  isTrading: Scalars['Boolean']['output'];
  isInWatchlist: Scalars['Boolean']['output'];
  inWatchlists?: Maybe<Array<Scalars['ID']['output']>>;
  isOwner: Scalars['Boolean']['output'];
  isCaStrategy: Scalars['Boolean']['output'];
  isPromoted: Scalars['Boolean']['output'];
  isWinner: Scalars['Boolean']['output'];
  competitionId?: Maybe<Scalars['ID']['output']>;
  type: BattlefieldStrategyType;
  chargingMethod: BattlefieldStrategyChargingMethod;
  duration: BattlefieldStrategyDuration;
  subscriptionPrice?: Maybe<Scalars['Float']['output']>;
  subscriptionPriceAfterDiscount?: Maybe<Scalars['Float']['output']>;
  /** get linked club strategy */
  clubStrategy?: Maybe<ClubStrategy>;
  battlefieldStrategyComment?: Maybe<BattlefieldStrategyCommentsConnection>;
  battlefieldStrategyCommentByCurrentUser?: Maybe<BattlefieldStrategyComment>;
  records?: Maybe<Array<BattlefieldStrategyRecord>>;
  /** Tasks of current user running with any version of strategies of the BattlefieldStrategy */
  backtestTasks: Array<BacktestTask>;
  simulationTasks: Array<SimulationTask>;
  traderTasks: Array<TraderTask>;
  /** Subscription plans for the battlefield strategy */
  subscriptionPlans?: Maybe<Array<BattlefieldStrategySubscriptionPlan>>;
};


export type BattlefieldStrategyRankingProfitVectorArgs = {
  pointsCount?: InputMaybe<Scalars['Int']['input']>;
  period?: InputMaybe<Scalars['Int']['input']>;
  from?: InputMaybe<Scalars['Date']['input']>;
  to?: InputMaybe<Scalars['Date']['input']>;
};


export type BattlefieldStrategyBattlefieldStrategyCommentArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  sortByTotalLikes?: InputMaybe<Scalars['Boolean']['input']>;
  sortByNewest?: InputMaybe<Scalars['Boolean']['input']>;
  excludeCurrentUser?: InputMaybe<Scalars['Boolean']['input']>;
};


export type BattlefieldStrategyBacktestTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  onlyEditor?: InputMaybe<Scalars['Boolean']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type BattlefieldStrategySimulationTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};


export type BattlefieldStrategyTraderTasksArgs = {
  page?: InputMaybe<Scalars['Int']['input']>;
  countsPerPage?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Array<BrokerStatus>>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
};

export type BattlefieldStrategyAllowedMarket = {
  __typename?: 'BattlefieldStrategyAllowedMarket';
  id: Scalars['ID']['output'];
  suggestedExchange: SupportedExchange;
  suggestedPair: Scalars['String']['output'];
  suggestedMinInvestmentAmount?: Maybe<Scalars['Float']['output']>;
  suggestedTotalInvestmentAmount?: Maybe<Scalars['Float']['output']>;
  suggestedTotalAdoptors?: Maybe<Scalars['Int']['output']>;
  suggestedLeverage?: Maybe<Scalars['Float']['output']>;
  suggestedLeverageUpperBound?: Maybe<Scalars['Int']['output']>;
  suggestedLeverageLowerBound?: Maybe<Scalars['Int']['output']>;
  suggestedStopPoints?: Maybe<Scalars['Float']['output']>;
  suggestedTakeProfit?: Maybe<Scalars['Float']['output']>;
  /** value 0.05 means 5% */
  profitSharingRatio: Scalars['Float']['output'];
  liveInvestmentAmount: Scalars['Float']['output'];
  liveAdoptorCount: Scalars['Int']['output'];
  totalLaunchedInvestmentAmount: Scalars['Float']['output'];
  totalLaunchedAdoptorCount: Scalars['Int']['output'];
};

export enum ClubMemberRole {
  /** Staff roles */
  Owner = 'OWNER',
  Admin = 'ADMIN',
  Partner = 'PARTNER',
  /** Non staff roles: member purchases membership plan */
  Member = 'MEMBER',
  Follower = 'FOLLOWER'
}

export type ClubLink = {
  __typename?: 'ClubLink';
  type: Scalars['String']['output'];
  url: Scalars['String']['output'];
};

export type Club = {
  __typename?: 'Club';
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  links: Array<ClubLink>;
  description: Scalars['String']['output'];
  avatarUrl: Scalars['String']['output'];
  heroImageUrl: Scalars['String']['output'];
  nationality?: Maybe<Scalars['String']['output']>;
  creatorId: Scalars['Int']['output'];
  creator: User;
  bestClubStrategyId?: Maybe<Scalars['Int']['output']>;
  bestStrategy?: Maybe<ClubStrategy>;
  bestClubStrategyRoi: Scalars['Float']['output'];
  totalLaunchedLiveTradeCount: Scalars['Int']['output'];
  totalAddedWatchlistCount: Scalars['Int']['output'];
  totalUnrealizedROI: Scalars['Float']['output'];
  totalRealizedROI: Scalars['Float']['output'];
  totalUnrealizedProfit: Scalars['Float']['output'];
  totalRealizedProfit: Scalars['Float']['output'];
  totalMemberCount: Scalars['Int']['output'];
  totalStrategyCount: Scalars['Int']['output'];
  strategies: Array<ClubStrategy>;
  myRole?: Maybe<ClubMemberRole>;
  myPlan?: Maybe<ClubMembershipPlan>;
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
};

export type ClubPerformance = {
  __typename?: 'ClubPerformance';
  realizedPnl: Scalars['Float']['output'];
  averageRoi: Scalars['Float']['output'];
};

export type ClubStrategy = {
  __typename?: 'ClubStrategy';
  id: Scalars['Int']['output'];
  battlefieldStrategyId: Scalars['Int']['output'];
  battleFieldStrategy?: Maybe<BattlefieldStrategy>;
  clubId: Scalars['Int']['output'];
  permissionScope?: Maybe<ClubStrategyPermissionScope>;
  accessibleClubMembershipPlans?: Maybe<Array<Maybe<ClubMembershipPlan>>>;
  addedToWatchlistCount: Scalars['Int']['output'];
  launchedLiveTradeCount: Scalars['Int']['output'];
  club?: Maybe<Club>;
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
};

export type ClubMembershipPlan = {
  __typename?: 'ClubMembershipPlan';
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  description: Scalars['String']['output'];
  originalPrice: Scalars['Int']['output'];
  discountedPrice?: Maybe<Scalars['Int']['output']>;
  tier?: Maybe<Scalars['Int']['output']>;
  clubId: Scalars['Int']['output'];
  club?: Maybe<Club>;
  members?: Maybe<Array<Maybe<UserClubMembership>>>;
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
};

export type UserClubMembership = {
  __typename?: 'UserClubMembership';
  clubId: Scalars['Int']['output'];
  clubMembershipPlanId?: Maybe<Scalars['Int']['output']>;
  userId: Scalars['Int']['output'];
  startedAt: Scalars['Date']['output'];
  expiredAt?: Maybe<Scalars['Date']['output']>;
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
  user?: Maybe<PublicUser>;
  clubMembershipPlans?: Maybe<ClubMembershipPlan>;
  club?: Maybe<Club>;
  role: ClubMemberRole;
};

export type ClubAdmin = {
  __typename?: 'ClubAdmin';
  clubId: Scalars['Int']['output'];
  userId: Scalars['Int']['output'];
  user?: Maybe<PublicUser>;
  readableClub?: Maybe<Club>;
  writableClub?: Maybe<Club>;
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
};

export type UserClubLastSeen = {
  __typename?: 'UserClubLastSeen';
  clubId: Scalars['Int']['output'];
  userId: Scalars['Int']['output'];
  lastSeen: Scalars['Date']['output'];
  user?: Maybe<PublicUser>;
  club?: Maybe<Club>;
};

export type UserWatchlist = {
  __typename?: 'UserWatchlist';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type CompetitionTeamMember = {
  __typename?: 'CompetitionTeamMember';
  email: Scalars['String']['output'];
  name: Scalars['String']['output'];
  role: CompetitionTeamMemberRole;
};

export type CompetitionTeamInfo = {
  __typename?: 'competitionTeamInfo';
  teamId: Scalars['Int']['output'];
  teamName: Scalars['String']['output'];
  avatarUrl?: Maybe<Scalars['String']['output']>;
  members: Array<CompetitionTeamMember>;
};

/** return null when fields unable to calculate */
export type TaskPerformance = {
  __typename?: 'TaskPerformance';
  startingCapital: Scalars['Float']['output'];
  finalCapital: Scalars['Float']['output'];
  tradeCount: Scalars['Int']['output'];
  roi?: Maybe<Scalars['Float']['output']>;
  roiRealized?: Maybe<Scalars['Float']['output']>;
  roiUnrealized?: Maybe<Scalars['Float']['output']>;
  winRate?: Maybe<Scalars['Float']['output']>;
  mdd?: Maybe<Scalars['Float']['output']>;
  mddPercentage?: Maybe<Scalars['Float']['output']>;
  profitFactor?: Maybe<Scalars['Float']['output']>;
  oddsRatio?: Maybe<Scalars['Float']['output']>;
  sharpeRatio?: Maybe<Scalars['Float']['output']>;
  returnDivDd?: Maybe<Scalars['Float']['output']>;
  /** in minutes */
  longestTimeToMakeNewHigh?: Maybe<Scalars['Float']['output']>;
  /** in minutes */
  timeToMake21Trades?: Maybe<Scalars['Float']['output']>;
  buyAndHoldEquity?: Maybe<Scalars['Float']['output']>;
  averageTradePerMonth?: Maybe<Scalars['Float']['output']>;
};

export type CompetitionCumulationData = {
  __typename?: 'CompetitionCumulationData';
  user: User;
  totalStrategiesCount: Scalars['Int']['output'];
  roi?: Maybe<Scalars['Float']['output']>;
  currentRoiRanking?: Maybe<Scalars['Int']['output']>;
  previousRoiRanking?: Maybe<Scalars['Int']['output']>;
  rankingChange?: Maybe<Scalars['Int']['output']>;
};

export type CompetitionTeamCumulationData = {
  __typename?: 'CompetitionTeamCumulationData';
  teamInfo: CompetitionTeamInfo;
  totalStrategiesCount: Scalars['Int']['output'];
  roi?: Maybe<Scalars['Float']['output']>;
  sharpeRatio?: Maybe<Scalars['Float']['output']>;
  currentRoiRanking?: Maybe<Scalars['Int']['output']>;
  previousRoiRanking?: Maybe<Scalars['Int']['output']>;
  rankingChange?: Maybe<Scalars['Int']['output']>;
  battlefieldStrategies?: Maybe<Array<Maybe<BattlefieldStrategy>>>;
};

export type Competition = {
  __typename?: 'Competition';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  startAt: Scalars['Date']['output'];
  startRegistryAt: Scalars['Date']['output'];
  endRegistryAt?: Maybe<Scalars['Date']['output']>;
  startRankingAt: Scalars['Date']['output'];
  endAt: Scalars['Date']['output'];
  status: CompetitionStatus;
  type: CompetitionType;
  guideline?: Maybe<Scalars['JSON']['output']>;
  suggestedExchange?: Maybe<Scalars['String']['output']>;
  suggestedPairs?: Maybe<Scalars['JSON']['output']>;
  options?: Maybe<Scalars['JSON']['output']>;
  initBaseAmount?: Maybe<Scalars['Float']['output']>;
  battlefieldStrategies: Array<BattlefieldStrategy>;
  battlefieldStrategiesByPage: Array<BattlefieldStrategy>;
  battlefieldStrategiesCount: Scalars['Int']['output'];
  cumulatedRanking: Array<CompetitionCumulationData>;
  cumulatedRankingByPage: Array<CompetitionCumulationData>;
  cumulatedRankingCount: Scalars['Int']['output'];
  teamCumulatedRanking: Array<CompetitionTeamCumulationData>;
  teamCumulatedRankingByPage: Array<CompetitionTeamCumulationData>;
  teamCumulatedRankingCount: Scalars['Int']['output'];
};


export type CompetitionBattlefieldStrategiesByPageArgs = {
  page: Scalars['Int']['input'];
  countsPerPage: Scalars['Int']['input'];
  pair?: InputMaybe<Scalars['String']['input']>;
};


export type CompetitionBattlefieldStrategiesCountArgs = {
  pair?: InputMaybe<Scalars['String']['input']>;
};


export type CompetitionCumulatedRankingByPageArgs = {
  page: Scalars['Int']['input'];
  countsPerPage: Scalars['Int']['input'];
};


export type CompetitionTeamCumulatedRankingByPageArgs = {
  page: Scalars['Int']['input'];
  countsPerPage: Scalars['Int']['input'];
};

export enum CouponType {
  Subscription = 'SUBSCRIPTION',
  LiveTrade = 'LIVE_TRADE',
  BattlefieldStrategySubscription = 'BATTLEFIELD_STRATEGY_SUBSCRIPTION'
}

export enum CouponDiscountType {
  Freetrial = 'FREETRIAL',
  Percentage = 'PERCENTAGE',
  FixedAmount = 'FIXED_AMOUNT'
}

export enum CouponTier {
  Gold = 'GOLD',
  Red = 'RED',
  Blue = 'BLUE',
  White = 'WHITE',
  Gray = 'GRAY'
}

export type Coupon = {
  __typename?: 'Coupon';
  id: Scalars['Int']['output'];
  title: Scalars['String']['output'];
  description: Scalars['String']['output'];
  type: CouponType;
  expiredAt?: Maybe<Scalars['Date']['output']>;
  ownerId?: Maybe<Scalars['Int']['output']>;
  /**
   * Type of discount to apply:
   * - FREETRIAL: Free trial for freeTrialPeriod (freeTrialPeriodUnit)
   * - PERCENTAGE: Price becomes price * (1 - discountPercentage)
   * - FIXED_AMOUNT: Price becomes price - discountAmount
   */
  discountType?: Maybe<CouponDiscountType>;
  /**
   * Discount percentage in decimal (e.g. 0.2 for 20% off)
   * Only used when discountType is PERCENTAGE
   */
  discountPercentage?: Maybe<Scalars['Float']['output']>;
  /**
   * Fixed amount to subtract from price
   * Only used when discountType is FIXED_AMOUNT
   */
  discountAmount?: Maybe<Scalars['Float']['output']>;
  /**
   * Duration of the discount period
   * Only used when discountType is PERCENTAGE or FIXED_AMOUNT
   */
  discountPeriod?: Maybe<Scalars['Int']['output']>;
  /**
   * Unit of the discount period (e.g. "month", "year")
   * Only used when discountType is PERCENTAGE or FIXED_AMOUNT
   */
  discountPeriodUnit?: Maybe<Scalars['String']['output']>;
  /**
   * Duration of the free trial period
   * Only used when discountType is FREETRIAL
   */
  freeTrialPeriod?: Maybe<Scalars['Int']['output']>;
  /**
   * Unit of the free trial period (e.g. "month", "year")
   * Only used when discountType is FREETRIAL
   */
  freeTrialPeriodUnit?: Maybe<Scalars['String']['output']>;
  /** deprecated */
  tier?: Maybe<CouponTier>;
  isClubOwned: Scalars['Boolean']['output'];
  subscriptionPlan?: Maybe<SubscriptionPlan>;
};

export type UserCoupon = {
  __typename?: 'UserCoupon';
  id: Scalars['Int']['output'];
  isUsed: Scalars['Boolean']['output'];
  couponId: Scalars['Int']['output'];
  title: Scalars['String']['output'];
  description: Scalars['String']['output'];
  type: CouponType;
  tier?: Maybe<CouponTier>;
  isClubOwned: Scalars['Boolean']['output'];
  expiredAt?: Maybe<Scalars['Date']['output']>;
  ownerId?: Maybe<Scalars['Int']['output']>;
  discountAmount?: Maybe<Scalars['Float']['output']>;
  discountPeriodUnit?: Maybe<Scalars['String']['output']>;
  freeTrialPeriod?: Maybe<Scalars['Int']['output']>;
  freeTrialPeriodUnit?: Maybe<Scalars['String']['output']>;
  subscriptionPlan?: Maybe<SubscriptionPlan>;
};

export type LoginOauthResult = {
  __typename?: 'LoginOauthResult';
  token: Scalars['String']['output'];
  user: PublicUser;
};

export enum PrismaOrderByDirection {
  Ascending = 'ASCENDING',
  Descending = 'DESCENDING'
}

export type PrismaClubOrderBy = {
  then?: InputMaybe<PrismaClubOrderBy>;
  /** Order by Popularity */
  totalMemberCount?: InputMaybe<PrismaOrderByDirection>;
  /** Order by ROI */
  bestClubStrategyRoi?: InputMaybe<PrismaOrderByDirection>;
  /** Order by Latest */
  createdAt?: InputMaybe<PrismaOrderByDirection>;
};

export type PrismaMemberClubOrderBy = {
  then?: InputMaybe<PrismaMemberClubOrderBy>;
  /** Order by Newest Join */
  createdAt?: InputMaybe<PrismaOrderByDirection>;
  /** Order by Latest Activity */
  updatedAt?: InputMaybe<PrismaOrderByDirection>;
  /** Order by Name */
  name?: InputMaybe<PrismaOrderByDirection>;
};

export type PrismaClubFilterBy = {
  permissionScope?: InputMaybe<Array<InputMaybe<ClubStrategyPermissionScope>>>;
  planIds?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  strategyCreatorIds?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  investmentType?: InputMaybe<StrategyInvestmentType>;
  investmentTrend?: InputMaybe<StrategyInvestmentTrend>;
  exchanges?: InputMaybe<Array<InputMaybe<SupportedExchange>>>;
  exchangePairs?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  roi?: InputMaybe<Scalars['Float']['input']>;
  commentRating?: InputMaybe<Scalars['Int']['input']>;
};

export type PrismaFilterByBoolean = {
  equals?: InputMaybe<Scalars['Boolean']['input']>;
  not?: InputMaybe<Scalars['Boolean']['input']>;
};

export type PrismaFilterById = {
  contains?: InputMaybe<Scalars['ID']['input']>;
  endsWith?: InputMaybe<Scalars['ID']['input']>;
  equals?: InputMaybe<Scalars['ID']['input']>;
  gt?: InputMaybe<Scalars['ID']['input']>;
  gte?: InputMaybe<Scalars['ID']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  lt?: InputMaybe<Scalars['ID']['input']>;
  lte?: InputMaybe<Scalars['ID']['input']>;
  mode?: InputMaybe<Scalars['ID']['input']>;
  not?: InputMaybe<Scalars['ID']['input']>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  startsWith?: InputMaybe<Scalars['ID']['input']>;
};

export type PrismaFilterByInt = {
  equals?: InputMaybe<Scalars['Int']['input']>;
  gt?: InputMaybe<Scalars['Int']['input']>;
  gte?: InputMaybe<Scalars['Int']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  lt?: InputMaybe<Scalars['Int']['input']>;
  lte?: InputMaybe<Scalars['Int']['input']>;
  not?: InputMaybe<Scalars['Int']['input']>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
};

export type PrismaFilterByString = {
  contains?: InputMaybe<Scalars['String']['input']>;
  endsWith?: InputMaybe<Scalars['String']['input']>;
  equals?: InputMaybe<Scalars['String']['input']>;
  gt?: InputMaybe<Scalars['String']['input']>;
  gte?: InputMaybe<Scalars['String']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  lt?: InputMaybe<Scalars['String']['input']>;
  lte?: InputMaybe<Scalars['String']['input']>;
  mode?: InputMaybe<Scalars['String']['input']>;
  not?: InputMaybe<Scalars['String']['input']>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  startsWith?: InputMaybe<Scalars['String']['input']>;
};

export type PrismaFilterByDate = {
  equals?: InputMaybe<Scalars['String']['input']>;
  gt?: InputMaybe<Scalars['String']['input']>;
  gte?: InputMaybe<Scalars['String']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  lt?: InputMaybe<Scalars['String']['input']>;
  lte?: InputMaybe<Scalars['String']['input']>;
  not?: InputMaybe<Scalars['String']['input']>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type PrismaFilterByBrokerStatus = {
  in?: InputMaybe<Array<InputMaybe<BrokerStatus>>>;
  eq?: InputMaybe<BrokerStatus>;
};

export type PrismaFilterByBrokerType = {
  in?: InputMaybe<Array<InputMaybe<BrokerStatus>>>;
  eq?: InputMaybe<BrokerType>;
};

export type PrismaFilterByInvestmentType = {
  in?: InputMaybe<Array<InputMaybe<StrategyInvestmentType>>>;
};

export type PrismaFilterByInvestmentTrend = {
  in?: InputMaybe<Array<InputMaybe<StrategyInvestmentTrend>>>;
};

export type PrismaFilterBySupportedExchange = {
  eq?: InputMaybe<SupportedExchange>;
  in?: InputMaybe<Array<InputMaybe<SupportedExchange>>>;
};

export type FilterByBoolean = {
  eq?: InputMaybe<Scalars['Boolean']['input']>;
  not?: InputMaybe<Scalars['Boolean']['input']>;
};

export type FilterById = {
  eq?: InputMaybe<Scalars['ID']['input']>;
  gt?: InputMaybe<Scalars['ID']['input']>;
  gte?: InputMaybe<Scalars['ID']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['ID']['input']>>>;
  lt?: InputMaybe<Scalars['ID']['input']>;
  lte?: InputMaybe<Scalars['ID']['input']>;
};

export type FilterByInt = {
  eq?: InputMaybe<Scalars['Int']['input']>;
  gt?: InputMaybe<Scalars['Int']['input']>;
  gte?: InputMaybe<Scalars['Int']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  lt?: InputMaybe<Scalars['Int']['input']>;
  lte?: InputMaybe<Scalars['Int']['input']>;
  not?: InputMaybe<Scalars['Int']['input']>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
};

export type FilterByString = {
  endsWith?: InputMaybe<Scalars['String']['input']>;
  eq?: InputMaybe<Scalars['String']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  startsWith?: InputMaybe<Scalars['String']['input']>;
};

export type FilterByDate = {
  eq?: InputMaybe<Scalars['String']['input']>;
  gt?: InputMaybe<Scalars['String']['input']>;
  gte?: InputMaybe<Scalars['String']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  lt?: InputMaybe<Scalars['String']['input']>;
  lte?: InputMaybe<Scalars['String']['input']>;
  not?: InputMaybe<Scalars['String']['input']>;
  notIn?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type FilterByBrokerStatus = {
  in?: InputMaybe<Array<InputMaybe<BrokerStatus>>>;
  eq?: InputMaybe<BrokerStatus>;
};

export type FilterByBrokerType = {
  in?: InputMaybe<Array<InputMaybe<BrokerStatus>>>;
  eq?: InputMaybe<BrokerType>;
};

export type FilterByInvestmentType = {
  in?: InputMaybe<Array<InputMaybe<StrategyInvestmentType>>>;
};

export type FilterByInvestmentTrend = {
  in?: InputMaybe<Array<InputMaybe<StrategyInvestmentTrend>>>;
};

export type FilterBySupportedExchange = {
  eq?: InputMaybe<SupportedExchange>;
  in?: InputMaybe<Array<InputMaybe<SupportedExchange>>>;
};

export type BrokerTaskFilterBy = {
  and?: InputMaybe<Array<BrokerTaskFilterBy>>;
  or?: InputMaybe<Array<BrokerTaskFilterBy>>;
  id?: InputMaybe<FilterById>;
  type?: InputMaybe<FilterByBrokerType>;
  status?: InputMaybe<FilterByBrokerStatus>;
  investmentType?: InputMaybe<FilterByInvestmentType>;
  investmentTrend?: InputMaybe<FilterByInvestmentTrend>;
  exchange?: InputMaybe<FilterBySupportedExchange>;
  exchangePair?: InputMaybe<FilterByString>;
  strategyId?: InputMaybe<FilterById>;
  roi?: InputMaybe<FilterByInt>;
};

export type BrokerTaskOrderBy = {
  then?: InputMaybe<BrokerTaskOrderBy>;
  roi?: InputMaybe<SortOrder>;
  createdAt?: InputMaybe<SortOrder>;
  bfsSubscriptionExpiredAt?: InputMaybe<SortOrder>;
};

export type BattlefieldStrategyCommentInput = {
  id?: InputMaybe<Scalars['ID']['input']>;
  comment?: InputMaybe<Scalars['String']['input']>;
  rating: Scalars['Float']['input'];
};

export type BattlefieldStrategyAllowedMarketInput = {
  suggestedExchange: SupportedExchange;
  suggestedPair: Scalars['String']['input'];
  suggestedMinInvestmentAmount?: InputMaybe<Scalars['Float']['input']>;
  suggestedTotalInvestmentAmount?: InputMaybe<Scalars['Float']['input']>;
  suggestedTotalAdoptors?: InputMaybe<Scalars['Int']['input']>;
  suggestedLeverage?: InputMaybe<Scalars['Float']['input']>;
  suggestedLeverageUpperBound?: InputMaybe<Scalars['Int']['input']>;
  suggestedLeverageLowerBound?: InputMaybe<Scalars['Int']['input']>;
  suggestedStopPoints?: InputMaybe<Scalars['Float']['input']>;
  suggestedTakeProfit?: InputMaybe<Scalars['Float']['input']>;
  /** value 0.05 means 5% */
  profitSharingRatio: Scalars['Float']['input'];
};

export type BattlefieldStrategyInput = {
  strategyDesc?: InputMaybe<Scalars['String']['input']>;
  battlefieldStrategyAllowedMarkets?: InputMaybe<Array<InputMaybe<BattlefieldStrategyAllowedMarketInput>>>;
  suggestedExchange?: InputMaybe<SupportedExchange>;
  suggestedPair?: InputMaybe<Scalars['String']['input']>;
  suggestedMinInvestmentAmount?: InputMaybe<Scalars['Float']['input']>;
  suggestedTotalInvestmentAmount?: InputMaybe<Scalars['Float']['input']>;
  suggestedTotalAdoptors?: InputMaybe<Scalars['Int']['input']>;
  suggestedStopPoints?: InputMaybe<Scalars['Float']['input']>;
  suggestedTakeProfit?: InputMaybe<Scalars['Float']['input']>;
  suggestedInvestmentType?: InputMaybe<StrategyInvestmentType>;
  suggestedInvestmentTrend?: InputMaybe<StrategyInvestmentTrend>;
  suggestedLeverage?: InputMaybe<Scalars['Float']['input']>;
  suggestedLeverageUpperBound?: InputMaybe<Scalars['Int']['input']>;
  suggestedLeverageLowerBound?: InputMaybe<Scalars['Int']['input']>;
  /** value 0.05 means 5% */
  profitSharingRatio?: InputMaybe<Scalars['Float']['input']>;
};

export type CompetitionTeamUserInput = {
  email: Scalars['String']['input'];
  name: Scalars['String']['input'];
};

export type CompetitionTeamInput = {
  competitionId: Scalars['Int']['input'];
  teamName: Scalars['String']['input'];
  users: Array<CompetitionTeamUserInput>;
};

export type UserInput = {
  email: Scalars['String']['input'];
  password: Scalars['String']['input'];
  username: Scalars['String']['input'];
};

export type UpdateUserInput = {
  username?: InputMaybe<Scalars['String']['input']>;
  firstName?: InputMaybe<Scalars['String']['input']>;
  middleName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
};

export type ChangePasswordInput = {
  oldPassword: Scalars['String']['input'];
  newPassword: Scalars['String']['input'];
  newPasswordCheck: Scalars['String']['input'];
};

export type StrategyInput = {
  name?: InputMaybe<Scalars['String']['input']>;
  desc: Scalars['String']['input'];
  code?: InputMaybe<Scalars['String']['input']>;
  investmentType?: InputMaybe<StrategyInvestmentType>;
  investmentTrend?: InputMaybe<StrategyInvestmentTrend>;
  leverage?: InputMaybe<Scalars['Int']['input']>;
  isParameterized?: InputMaybe<Scalars['Boolean']['input']>;
  isRemote: Scalars['Boolean']['input'];
  isBinary?: InputMaybe<Scalars['Boolean']['input']>;
  binaryFilename?: InputMaybe<Scalars['String']['input']>;
  language?: InputMaybe<SupportStrategyLanguage>;
  type: StrategyType;
  note?: InputMaybe<Scalars['String']['input']>;
  options: Array<StrategyOptionInput>;
  /** Must given if type of strategy is MULTI_PAIR */
  targetCurrency?: InputMaybe<Scalars['String']['input']>;
  baseCurrency?: InputMaybe<Scalars['String']['input']>;
  baseExchange?: InputMaybe<SupportedExchange>;
};

export type StrategyTemplateInput = {
  investmentType?: InputMaybe<StrategyInvestmentType>;
  targetCurrency?: InputMaybe<Scalars['String']['input']>;
  baseCurrency?: InputMaybe<Scalars['String']['input']>;
  baseExchange?: InputMaybe<SupportedExchange>;
  leverage?: InputMaybe<Scalars['Int']['input']>;
};

export type StrategyOptionInput = {
  name: Scalars['String']['input'];
  desc: Scalars['String']['input'];
  type: StrategyOptionType;
  defaultValue: Scalars['String']['input'];
  tips?: InputMaybe<Scalars['String']['input']>;
};

export type StrategyOptionValueInput = {
  name: Scalars['String']['input'];
  value: Scalars['String']['input'];
};

export type StrategyTaskConfigInput = {
  id?: InputMaybe<Scalars['ID']['input']>;
  label: Scalars['String']['input'];
  rangeStart: Scalars['Date']['input'];
  rangeEnd: Scalars['Date']['input'];
  spread: Scalars['Float']['input'];
  fee: Scalars['Float']['input'];
  initBaseAmount: Scalars['Float']['input'];
  leverage?: InputMaybe<Scalars['Int']['input']>;
};

export type LaunchTaskExchangePairInput = {
  exchange: SupportedExchange;
  pair: Scalars['String']['input'];
};

export type LaunchBacktestInput = {
  strategyId: Scalars['ID']['input'];
  launcher: BrokerLauncher;
  options: Array<StrategyOptionValueInput>;
  initAssetsWallet: Scalars['JSON']['input'];
  baseCurrency: Scalars['String']['input'];
  rangeStart: Scalars['Date']['input'];
  rangeEnd: Scalars['Date']['input'];
  targetExchangePair: Array<LaunchTaskExchangePairInput>;
  leverage?: InputMaybe<Scalars['Int']['input']>;
};

export type LaunchSimulationInput = {
  strategyId: Scalars['ID']['input'];
  options: Array<StrategyOptionValueInput>;
  initAssetsWallet: Scalars['JSON']['input'];
  baseCurrency: Scalars['String']['input'];
  targetExchangePair: Array<LaunchTaskExchangePairInput>;
  /** lockGainRatio/stopLossRatio unit in percentage, eg. 10.0 for 10.0% */
  lockGainRatio?: InputMaybe<Scalars['Float']['input']>;
  stopLossRatio?: InputMaybe<Scalars['Float']['input']>;
  leverage?: InputMaybe<Scalars['Int']['input']>;
};

export type LaunchTraderInput = {
  strategyId: Scalars['ID']['input'];
  options: Array<StrategyOptionValueInput>;
  initAssetsWallet: Scalars['JSON']['input'];
  baseCurrency: Scalars['String']['input'];
  targetExchangePair: Array<LaunchTaskExchangePairInput>;
  /** lockGainRatio/stopLossRatio unit in percentage, eg. 10.0 for 10.0% */
  lockGainRatio: Scalars['Float']['input'];
  stopLossRatio: Scalars['Float']['input'];
  apiKeyIds: Array<Scalars['ID']['input']>;
  lockCurrency?: InputMaybe<Scalars['String']['input']>;
  autoClosePosition?: InputMaybe<Scalars['Boolean']['input']>;
  leverage?: InputMaybe<Scalars['Int']['input']>;
  /** @deprecated Use couponCode instead. This field is only used for profit sharing mode. */
  userCouponId?: InputMaybe<Scalars['Int']['input']>;
  /**
   * Coupon code for subscription discount. Only applicable when chargingMethod is CA_SUBSCRIPTION.
   * The coupon can be:
   * - Free trial: subscription price will be 0
   * - Percentage discount: subscription price will be reduced by the percentage
   * - Fixed amount discount: subscription price will be reduced by the fixed amount
   */
  couponCode?: InputMaybe<Scalars['String']['input']>;
  chargingMethod?: InputMaybe<BrokerChargingMethod>;
  bfsSubscriptionPlanId?: InputMaybe<Scalars['ID']['input']>;
};

export type UpdateTraderUserSettingInput = {
  /** lockGainRatio/stopLossRatio unit in percentage, eg. 10.0 for 10.0% */
  lockGainRatio?: InputMaybe<Scalars['Float']['input']>;
  stopLossRatio?: InputMaybe<Scalars['Float']['input']>;
  autoClosePosition?: InputMaybe<Scalars['Boolean']['input']>;
};

export type TraderTaskFilterInput = {
  investmentTypes?: InputMaybe<Array<InputMaybe<StrategyInvestmentType>>>;
  investmentTrends?: InputMaybe<Array<InputMaybe<StrategyInvestmentTrend>>>;
  exchanges?: InputMaybe<Array<InputMaybe<SupportedExchange>>>;
  exchangePairs?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  /** in decimal, e.g. 10%: `minRoi: 0.1` */
  minRoi?: InputMaybe<Scalars['Float']['input']>;
  maxRoi?: InputMaybe<Scalars['Float']['input']>;
  roiOrder?: InputMaybe<SortOrder>;
  statuses?: InputMaybe<Array<BrokerStatus>>;
  results?: InputMaybe<Array<BrokerResult>>;
  strategyId?: InputMaybe<Scalars['ID']['input']>;
  /** in decimal, e.g. 10%: `minDd: 0.1` */
  minMdd?: InputMaybe<Scalars['Float']['input']>;
  maxMdd?: InputMaybe<Scalars['Float']['input']>;
  bfsSubscriptionExpiredAtOrder?: InputMaybe<SortOrder>;
};

export type BattlefieldStrategyFilterInput = {
  /**
   * return `ONBOARD` battlefield strategies when `status` is not given
   * `getBattlefieldStrategiesByFilter` and `getBattlefieldStrategiesCountByFilter` only return `ONBOARD` and `OFFBOARD` battlefield strategies
   * `watchlistBattlefieldStrategiesByFilter` and `watchlistBattlefieldStrategiesCountByFilter` only return `ONBOARD` battlefield strategies
   */
  status?: InputMaybe<StrategyStatus>;
  /** 30d, 3M, 1y */
  period?: InputMaybe<Scalars['String']['input']>;
  /** 30d, 3M, 6M, 1y */
  registrationTimeRange?: InputMaybe<Scalars['String']['input']>;
  investmentTypes?: InputMaybe<Array<InputMaybe<StrategyInvestmentType>>>;
  investmentTrends?: InputMaybe<Array<InputMaybe<StrategyInvestmentTrend>>>;
  exchanges?: InputMaybe<Array<InputMaybe<SupportedExchange>>>;
  exchangePairs?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  /** in decimal, e.g. 10%: `minRoi: 0.1` */
  minRoi?: InputMaybe<Scalars['Float']['input']>;
  maxRoi?: InputMaybe<Scalars['Float']['input']>;
  minSharpeRatio?: InputMaybe<Scalars['Float']['input']>;
  maxSharpeRatio?: InputMaybe<Scalars['Float']['input']>;
  minMdd?: InputMaybe<Scalars['Float']['input']>;
  maxMdd?: InputMaybe<Scalars['Float']['input']>;
  minAverageCommentRating?: InputMaybe<Scalars['Float']['input']>;
  maxAverageCommentRating?: InputMaybe<Scalars['Float']['input']>;
  roiOrder?: InputMaybe<SortOrder>;
  mddOrder?: InputMaybe<SortOrder>;
  sharpeRatioOrder?: InputMaybe<SortOrder>;
  winRateOrder?: InputMaybe<SortOrder>;
  averageCommentRatingOrder?: InputMaybe<SortOrder>;
  liveInvestmentAmountOrder?: InputMaybe<SortOrder>;
  liveAdoptorCountOrder?: InputMaybe<SortOrder>;
  liveProfitAndLossOrder?: InputMaybe<SortOrder>;
  liveAverageRoiOrder?: InputMaybe<SortOrder>;
  totalLaunchedInvestmentAmountOrder?: InputMaybe<SortOrder>;
  totalLaunchedAdoptorCountOrder?: InputMaybe<SortOrder>;
  totalProfitAndLossOrder?: InputMaybe<SortOrder>;
  totalAverageRoiOrder?: InputMaybe<SortOrder>;
  onboardedAtOrder?: InputMaybe<SortOrder>;
  minTotalLaunchedInvestmentAmount?: InputMaybe<Scalars['Float']['input']>;
  maxTotalLaunchedInvestmentAmount?: InputMaybe<Scalars['Float']['input']>;
  minTotalLaunchedAdoptorCount?: InputMaybe<Scalars['Int']['input']>;
  maxTotalLaunchedAdoptorCount?: InputMaybe<Scalars['Int']['input']>;
  /** `rangeStart` and `rangeEnd` are used only when status is OFFBOARD */
  rangeStart?: InputMaybe<Scalars['Date']['input']>;
  rangeEnd?: InputMaybe<Scalars['Date']['input']>;
  keywords?: InputMaybe<Scalars['String']['input']>;
  isCaStrategy?: InputMaybe<Scalars['Boolean']['input']>;
  isPromoted?: InputMaybe<Scalars['Boolean']['input']>;
  isBacktestable?: InputMaybe<Scalars['Boolean']['input']>;
  /** only return `ARENA` and `CHAINUP` battlefield strategies */
  type?: InputMaybe<BattlefieldStrategyType>;
  chargingMethods?: InputMaybe<Array<InputMaybe<BattlefieldStrategyChargingMethod>>>;
  durations?: InputMaybe<Array<InputMaybe<BattlefieldStrategyDuration>>>;
  /** workaround */
  tradingTeam?: InputMaybe<Scalars['String']['input']>;
};

export type SocialLink = {
  type: SocialLinkType;
  url: Scalars['String']['input'];
};

export type ClubInput = {
  name: Scalars['String']['input'];
  links: Array<InputMaybe<SocialLink>>;
  description: Scalars['String']['input'];
  nationality?: InputMaybe<Scalars['String']['input']>;
};

export type ClubStatsInput = {
  bestStrategyId?: InputMaybe<Scalars['Int']['input']>;
  totalLaunchedLiveTradeCount?: InputMaybe<Scalars['Int']['input']>;
  totalUnrealizedROI?: InputMaybe<Scalars['Float']['input']>;
  totalRealizedROI?: InputMaybe<Scalars['Float']['input']>;
  totalUnrealizedProfit?: InputMaybe<Scalars['Float']['input']>;
  totalRealizedProfit?: InputMaybe<Scalars['Float']['input']>;
  totalMemberCount?: InputMaybe<Scalars['Int']['input']>;
};

export type ClubMembershipPlanInput = {
  name: Scalars['String']['input'];
  description: Scalars['String']['input'];
  originalPrice: Scalars['Int']['input'];
  discountedPrice?: InputMaybe<Scalars['Int']['input']>;
  priceUnit?: InputMaybe<Scalars['String']['input']>;
  period?: InputMaybe<Scalars['Int']['input']>;
  periodUnit?: InputMaybe<Scalars['String']['input']>;
  tier?: InputMaybe<Scalars['Int']['input']>;
  clubId: Scalars['Int']['input'];
  livetradeLimits?: InputMaybe<Scalars['Int']['input']>;
};

export enum ClubStrategyPermissionScope {
  Public = 'PUBLIC',
  Private = 'PRIVATE',
  Member = 'MEMBER'
}

export type ClubStrategyInput = {
  strategyDesc?: InputMaybe<Scalars['String']['input']>;
  battlefieldStrategyAllowedMarkets?: InputMaybe<Array<InputMaybe<BattlefieldStrategyAllowedMarketInput>>>;
  suggestedExchange?: InputMaybe<SupportedExchange>;
  suggestedPair?: InputMaybe<Scalars['String']['input']>;
  suggestedMinInvestmentAmount?: InputMaybe<Scalars['Float']['input']>;
  suggestedTotalInvestmentAmount?: InputMaybe<Scalars['Float']['input']>;
  suggestedTotalAdoptors?: InputMaybe<Scalars['Int']['input']>;
  suggestedStopPoints?: InputMaybe<Scalars['Float']['input']>;
  suggestedTakeProfit?: InputMaybe<Scalars['Float']['input']>;
  suggestedInvestmentType?: InputMaybe<StrategyInvestmentType>;
  suggestedInvestmentTrend?: InputMaybe<StrategyInvestmentTrend>;
  suggestedLeverage?: InputMaybe<Scalars['Float']['input']>;
  suggestedLeverageUpperBound?: InputMaybe<Scalars['Int']['input']>;
  suggestedLeverageLowerBound?: InputMaybe<Scalars['Int']['input']>;
  /** value 0.05 means 5% */
  profitSharingRatio?: InputMaybe<Scalars['Float']['input']>;
  permissionScope?: InputMaybe<ClubStrategyPermissionScope>;
  /** Maximum duration of live trading */
  publicTimeConstraintPeriod?: InputMaybe<Scalars['Int']['input']>;
  publicTimeConstraintPeriodUnit?: InputMaybe<Scalars['String']['input']>;
  /** List of allowed membership plan ids */
  accessiblePlanIds?: InputMaybe<Array<Scalars['Int']['input']>>;
};

export enum BusinessSector {
  Cex = 'CEX',
  Dex = 'DEX',
  IntroducingBroker = 'INTRODUCING_BROKER',
  Kol = 'KOL',
  WhiteLabelExchange = 'WHITE_LABEL_EXCHANGE',
  Other = 'OTHER'
}

export type BusinessInquiryInput = {
  companyName: Scalars['String']['input'];
  jobTitle: Scalars['String']['input'];
  telegramId: Scalars['String']['input'];
  name: Scalars['String']['input'];
  email: Scalars['String']['input'];
  businessSector: BusinessSector;
};

export type BrokerTaskEdge = {
  __typename?: 'BrokerTaskEdge';
  cursor: Scalars['String']['output'];
  node: BrokerTask;
};

export type BacktestTaskEdge = {
  __typename?: 'BacktestTaskEdge';
  cursor: Scalars['String']['output'];
  node: BacktestTask;
};

export type SimulationTaskEdge = {
  __typename?: 'SimulationTaskEdge';
  cursor: Scalars['String']['output'];
  node: SimulationTask;
};

export type TraderTaskEdge = {
  __typename?: 'TraderTaskEdge';
  cursor: Scalars['String']['output'];
  node: TraderTask;
};

export type BrokerTaskConnection = {
  __typename?: 'BrokerTaskConnection';
  pageInfo: PageInfo;
  edges: Array<BrokerTaskEdge>;
};

export type BacktestTaskConnection = {
  __typename?: 'BacktestTaskConnection';
  pageInfo: PageInfo;
  edges: Array<BacktestTaskEdge>;
};

export type SimulationTaskConnection = {
  __typename?: 'SimulationTaskConnection';
  pageInfo: PageInfo;
  edges: Array<SimulationTaskEdge>;
};

export type TraderTaskConnection = {
  __typename?: 'TraderTaskConnection';
  pageInfo: PageInfo;
  edges: Array<TraderTaskEdge>;
};

export type SiweVerifyResult = {
  __typename?: 'SiweVerifyResult';
  isOk: Scalars['Boolean']['output'];
  token?: Maybe<Scalars['String']['output']>;
};

export type Payment = {
  __typename?: 'Payment';
  id: Scalars['ID']['output'];
  status: PaymentStatus;
  network: PaymentNetwork;
  address: Scalars['String']['output'];
  currency: Scalars['String']['output'];
  amountDue: Scalars['String']['output'];
  discrepancy: Scalars['String']['output'];
  txHash?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['Date']['output'];
  updatedAt: Scalars['Date']['output'];
  expiredAt?: Maybe<Scalars['Date']['output']>;
};

export enum SubscriptionPaymentStatus {
  Pending = 'PENDING',
  Completed = 'COMPLETED',
  Expired = 'EXPIRED',
  HandledByThirdParty = 'HANDLED_BY_THIRD_PARTY',
  Failed = 'FAILED'
}

export type BattlefieldStrategySubscription = {
  __typename?: 'BattlefieldStrategySubscription';
  id: Scalars['ID']['output'];
  price: Scalars['String']['output'];
  priceUnit: Scalars['String']['output'];
  startedAt: Scalars['Date']['output'];
  expiredAt: Scalars['Date']['output'];
  battlefieldStrategy?: Maybe<BattlefieldStrategy>;
  exchange?: Maybe<SupportedExchange>;
  payments: Array<Payment>;
  paymentStatus: SubscriptionPaymentStatus;
};

export type VerifySiweMessageMutationVariables = Exact<{
  message: Scalars['String']['input'];
  signature: Scalars['String']['input'];
}>;


export type VerifySiweMessageMutation = { __typename?: 'Mutation', verifySiweMessage: { __typename?: 'SiweVerifyResult', isOk: boolean, token?: string | null } };

export type LoginWithThirdPartyTokenMutationVariables = Exact<{
  provider: UserThirdPartyProvider;
  token: Scalars['String']['input'];
  exchange?: InputMaybe<Scalars['String']['input']>;
}>;


export type LoginWithThirdPartyTokenMutation = { __typename?: 'Mutation', loginWithThirdPartyToken?: string | null };

export type SiweNonceMutationVariables = Exact<{
  address: Scalars['String']['input'];
}>;


export type SiweNonceMutation = { __typename?: 'Mutation', getSiweNonce: string };

export type MyIdQueryVariables = Exact<{ [key: string]: never; }>;


export type MyIdQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string } | { __typename?: 'PrivateUser', id: string } };

export type RefreshTokenMutationVariables = Exact<{ [key: string]: never; }>;


export type RefreshTokenMutation = { __typename?: 'Mutation', refreshToken?: string | null };

export type ArenaFilteredStrategiesQueryVariables = Exact<{
  filter: BattlefieldStrategyFilterInput;
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
}>;


export type ArenaFilteredStrategiesQuery = { __typename?: 'Query', getBattlefieldStrategiesByFilter: Array<{ __typename?: 'BattlefieldStrategy', id: string, strategyName?: string | null, suggestedPair?: string | null, suggestedInvestmentType?: StrategyInvestmentType | null, suggestedInvestmentTrend?: StrategyInvestmentTrend | null, rankingRoi?: number | null, rankingSharpeRatio?: number | null, rankingMdd?: number | null, rankingWinRate?: number | null, liveAdoptorCount: number, liveInvestmentAmount: number, liveAverageRoi: number, chargingMethod: BattlefieldStrategyChargingMethod, onboardedAt?: any | null, isWinner: boolean, duration: BattlefieldStrategyDuration, strategy: { __typename?: 'Strategy', id: string, isLivetradableForCurrentUser: { __typename?: 'IsLivetradableForCurrentUserResult', isLivetradable: boolean, reasonType: IsLivetradableReasonType, reason: string } }, battlefieldStrategyAllowedMarkets?: Array<{ __typename?: 'BattlefieldStrategyAllowedMarket', suggestedExchange: SupportedExchange, suggestedPair: string } | null> | null, owner: { __typename?: 'PublicUser', avatarUrl: string } | { __typename?: 'PrivateUser', avatarUrl: string } }> };

export type RefetchThirdPartyApiKeyMutationVariables = Exact<{
  exchange?: InputMaybe<Scalars['String']['input']>;
}>;


export type RefetchThirdPartyApiKeyMutation = { __typename?: 'Mutation', refetchThirdPartyApiKey?: { __typename?: 'ExchangeApiKey', id: string, exchange: SupportedExchange } | null };

export type GetMyTraderTasksQueryVariables = Exact<{
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<TraderTaskFilterInput>;
  reverseOrder?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type GetMyTraderTasksQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, traderTasksCursor: { __typename?: 'TraderTaskConnection', edges: Array<{ __typename?: 'TraderTaskEdge', cursor: string, node: { __typename?: 'TraderTask', id: string, createdAt: any, updatedAt: any, rangeEnd?: any | null, strategyInvestmentType?: string | null, strategyInvestmentTrend?: string | null, roi?: number | null, status: BrokerStatus, strategyId: string, targetExchangePair: any, lockGainRatio?: number | null, stopLossRatio?: number | null, baseExchange: SupportedExchange, autoClosePosition?: boolean | null, chargingMethod?: BrokerChargingMethod | null, strategy?: { __typename?: 'Strategy', id: string, name?: string | null, investmentType?: StrategyInvestmentType | null, investmentTrend?: StrategyInvestmentTrend | null, templateName?: string | null, templateSerialNumber?: number | null, owner: { __typename?: 'PublicUser', id: string, avatarUrl: string } | { __typename?: 'PrivateUser', id: string, avatarUrl: string } } | null, exchangeApiKeys?: Array<{ __typename?: 'ExchangeApiKeyInfo', comment?: string | null, apiKey: string }> | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null } } } | { __typename?: 'PrivateUser', id: string, traderTasksCursor: { __typename?: 'TraderTaskConnection', edges: Array<{ __typename?: 'TraderTaskEdge', cursor: string, node: { __typename?: 'TraderTask', id: string, createdAt: any, updatedAt: any, rangeEnd?: any | null, strategyInvestmentType?: string | null, strategyInvestmentTrend?: string | null, roi?: number | null, status: BrokerStatus, strategyId: string, targetExchangePair: any, lockGainRatio?: number | null, stopLossRatio?: number | null, baseExchange: SupportedExchange, autoClosePosition?: boolean | null, chargingMethod?: BrokerChargingMethod | null, strategy?: { __typename?: 'Strategy', id: string, name?: string | null, investmentType?: StrategyInvestmentType | null, investmentTrend?: StrategyInvestmentTrend | null, templateName?: string | null, templateSerialNumber?: number | null, owner: { __typename?: 'PublicUser', id: string, avatarUrl: string } | { __typename?: 'PrivateUser', id: string, avatarUrl: string } } | null, exchangeApiKeys?: Array<{ __typename?: 'ExchangeApiKeyInfo', comment?: string | null, apiKey: string }> | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null } } } };

export type GetCopyingTradeBrokerTaskQueryVariables = Exact<{
  taskId: Scalars['ID']['input'];
}>;


export type GetCopyingTradeBrokerTaskQuery = { __typename?: 'Query', getBroker: { __typename?: 'BacktestTask', id: string, initBaseAmount: number, leverage: number, stopLossRatio?: number | null, lockGainRatio?: number | null, autoClosePosition?: boolean | null, chargingMethod?: BrokerChargingMethod | null, exchangeApiKeys?: Array<{ __typename?: 'ExchangeApiKeyInfo', id: string }> | null, bfsSubscription?: { __typename?: 'BattlefieldStrategySubscription', id: string, price: string, priceUnit: string, startedAt: any, expiredAt: any, paymentStatus: SubscriptionPaymentStatus } | null } | { __typename?: 'SimulationTask', id: string, initBaseAmount: number, leverage: number, stopLossRatio?: number | null, lockGainRatio?: number | null, autoClosePosition?: boolean | null, chargingMethod?: BrokerChargingMethod | null, exchangeApiKeys?: Array<{ __typename?: 'ExchangeApiKeyInfo', id: string }> | null, bfsSubscription?: { __typename?: 'BattlefieldStrategySubscription', id: string, price: string, priceUnit: string, startedAt: any, expiredAt: any, paymentStatus: SubscriptionPaymentStatus } | null } | { __typename?: 'TraderTask', id: string, initBaseAmount: number, leverage: number, stopLossRatio?: number | null, lockGainRatio?: number | null, autoClosePosition?: boolean | null, chargingMethod?: BrokerChargingMethod | null, exchangeApiKeys?: Array<{ __typename?: 'ExchangeApiKeyInfo', id: string }> | null, bfsSubscription?: { __typename?: 'BattlefieldStrategySubscription', id: string, price: string, priceUnit: string, startedAt: any, expiredAt: any, paymentStatus: SubscriptionPaymentStatus } | null } };

export type LiveTradeBrokerTaskQueryVariables = Exact<{
  taskId: Scalars['ID']['input'];
}>;


export type LiveTradeBrokerTaskQuery = { __typename?: 'Query', getBroker: { __typename?: 'BacktestTask', id: string, baseExchange: SupportedExchange, createdAt: any, rangeEnd?: any | null, stopLossRatio?: number | null, lockGainRatio?: number | null, leverage: number, strategyInvestmentType?: string | null, autoClosePosition?: boolean | null, baseCurrency: string, profitSharingRatio?: number | null, initAssetsValues?: Array<{ __typename?: 'AssetsValue', currency: string, amount: number, equivalentQuoteAmount: number }> | null, performance: { __typename?: 'TaskPerformance', startingCapital: number, finalCapital: number, tradeCount: number, roi?: number | null, roiRealized?: number | null, roiUnrealized?: number | null, winRate?: number | null, mddPercentage?: number | null, profitFactor?: number | null, oddsRatio?: number | null, sharpeRatio?: number | null, returnDivDd?: number | null, longestTimeToMakeNewHigh?: number | null, buyAndHoldEquity?: number | null }, strategy?: { __typename?: 'Strategy', targetCurrency?: string | null, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', suggestedStopPoints?: number | null, suggestedTakeProfit?: number | null } | null } | null } | { __typename?: 'SimulationTask', id: string, baseExchange: SupportedExchange, createdAt: any, rangeEnd?: any | null, stopLossRatio?: number | null, lockGainRatio?: number | null, leverage: number, strategyInvestmentType?: string | null, autoClosePosition?: boolean | null, baseCurrency: string, profitSharingRatio?: number | null, initAssetsValues?: Array<{ __typename?: 'AssetsValue', currency: string, amount: number, equivalentQuoteAmount: number }> | null, performance: { __typename?: 'TaskPerformance', startingCapital: number, finalCapital: number, tradeCount: number, roi?: number | null, roiRealized?: number | null, roiUnrealized?: number | null, winRate?: number | null, mddPercentage?: number | null, profitFactor?: number | null, oddsRatio?: number | null, sharpeRatio?: number | null, returnDivDd?: number | null, longestTimeToMakeNewHigh?: number | null, buyAndHoldEquity?: number | null }, strategy?: { __typename?: 'Strategy', targetCurrency?: string | null, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', suggestedStopPoints?: number | null, suggestedTakeProfit?: number | null } | null } | null } | { __typename?: 'TraderTask', id: string, baseExchange: SupportedExchange, createdAt: any, rangeEnd?: any | null, stopLossRatio?: number | null, lockGainRatio?: number | null, leverage: number, strategyInvestmentType?: string | null, autoClosePosition?: boolean | null, baseCurrency: string, profitSharingRatio?: number | null, initAssetsValues?: Array<{ __typename?: 'AssetsValue', currency: string, amount: number, equivalentQuoteAmount: number }> | null, performance: { __typename?: 'TaskPerformance', startingCapital: number, finalCapital: number, tradeCount: number, roi?: number | null, roiRealized?: number | null, roiUnrealized?: number | null, winRate?: number | null, mddPercentage?: number | null, profitFactor?: number | null, oddsRatio?: number | null, sharpeRatio?: number | null, returnDivDd?: number | null, longestTimeToMakeNewHigh?: number | null, buyAndHoldEquity?: number | null }, strategy?: { __typename?: 'Strategy', targetCurrency?: string | null, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', suggestedStopPoints?: number | null, suggestedTakeProfit?: number | null } | null } | null } };

export type GetTaskActivitiesQueryVariables = Exact<{
  taskId: Scalars['ID']['input'];
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  activityTypes?: InputMaybe<Array<InputMaybe<BrokerActivityType>> | InputMaybe<BrokerActivityType>>;
}>;


export type GetTaskActivitiesQuery = { __typename?: 'Query', getBroker: { __typename?: 'BacktestTask', id: string, activities: { __typename?: 'ActivitiesConnection', totalCount: number, edges: Array<{ __typename?: 'ActivitiesEdge', cursor: string, node: { __typename?: 'BrokerActivity', time: any, type: BrokerActivityType, price?: number | null, amount?: number | null, orderStatus?: OrderStatus | null, message?: string | null, exchange?: SupportedExchange | null, pair?: string | null } }>, pageInfo: { __typename?: 'PageInfo', endCursor?: string | null, hasNextPage: boolean } } } | { __typename?: 'SimulationTask', id: string, activities: { __typename?: 'ActivitiesConnection', totalCount: number, edges: Array<{ __typename?: 'ActivitiesEdge', cursor: string, node: { __typename?: 'BrokerActivity', time: any, type: BrokerActivityType, price?: number | null, amount?: number | null, orderStatus?: OrderStatus | null, message?: string | null, exchange?: SupportedExchange | null, pair?: string | null } }>, pageInfo: { __typename?: 'PageInfo', endCursor?: string | null, hasNextPage: boolean } } } | { __typename?: 'TraderTask', id: string, activities: { __typename?: 'ActivitiesConnection', totalCount: number, edges: Array<{ __typename?: 'ActivitiesEdge', cursor: string, node: { __typename?: 'BrokerActivity', time: any, type: BrokerActivityType, price?: number | null, amount?: number | null, orderStatus?: OrderStatus | null, message?: string | null, exchange?: SupportedExchange | null, pair?: string | null } }>, pageInfo: { __typename?: 'PageInfo', endCursor?: string | null, hasNextPage: boolean } } } };

export type StopBrokerTaskMutationVariables = Exact<{
  taskId: Scalars['ID']['input'];
}>;


export type StopBrokerTaskMutation = { __typename?: 'Mutation', stopTask: { __typename?: 'BacktestTask', strategy?: { __typename?: 'Strategy', id: string, name?: string | null } | null } | { __typename?: 'SimulationTask', strategy?: { __typename?: 'Strategy', id: string, name?: string | null } | null } | { __typename?: 'TraderTask', strategy?: { __typename?: 'Strategy', id: string, name?: string | null } | null } };

export type UpdateLiveTradeSettingsMutationVariables = Exact<{
  taskId: Scalars['ID']['input'];
  userSetting: UpdateTraderUserSettingInput;
}>;


export type UpdateLiveTradeSettingsMutation = { __typename?: 'Mutation', updateTraderUserSetting: boolean };

export type ProfileUserInformationQueryVariables = Exact<{ [key: string]: never; }>;


export type ProfileUserInformationQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, username: string, firstName?: string | null, avatarUrl: string, email?: string | null, apiKeyUsage: number, userEvmAddresses: Array<{ __typename?: 'UserEvmAddress', address: string, isPrimary: boolean }>, battlefieldStrategySubscriptions: Array<{ __typename?: 'BattlefieldStrategySubscription', battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', strategyName?: string | null } | null }>, payments: Array<{ __typename?: 'Payment', address: string }> } | { __typename?: 'PrivateUser', id: string, username: string, firstName?: string | null, avatarUrl: string, email?: string | null, apiKeyUsage: number, userEvmAddresses: Array<{ __typename?: 'UserEvmAddress', address: string, isPrimary: boolean }>, battlefieldStrategySubscriptions: Array<{ __typename?: 'BattlefieldStrategySubscription', battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', strategyName?: string | null } | null }>, payments: Array<{ __typename?: 'Payment', address: string }> } };

export type DeleteBrokerTaskMutationVariables = Exact<{
  taskId: Scalars['ID']['input'];
}>;


export type DeleteBrokerTaskMutation = { __typename?: 'Mutation', deleteBrokerTask: string };

export type ArenaMarketplaceStrategyQueryVariables = Exact<{
  battlefieldStrategyId: Scalars['ID']['input'];
}>;


export type ArenaMarketplaceStrategyQuery = { __typename?: 'Query', getBattlefieldStrategy: { __typename?: 'BattlefieldStrategy', chargingMethod: BattlefieldStrategyChargingMethod, duration: BattlefieldStrategyDuration, isWinner: boolean, liveAdoptorCount: number, liveInvestmentAmount: number, onboardedAt?: any | null, profitSharingRatio: number, rankingMdd?: number | null, rankingRoi?: number | null, rankingSharpeRatio?: number | null, rankingTaskId?: string | null, rankingWinRate?: number | null, strategyDesc?: string | null, strategyName?: string | null, suggestedInvestmentTrend?: StrategyInvestmentTrend | null, suggestedInvestmentType?: StrategyInvestmentType | null, suggestedLeverage: number, suggestedMinInvestmentAmount?: number | null, suggestedPair?: string | null, averageHoldingDays: number, battlefieldStrategyAllowedMarkets?: Array<{ __typename?: 'BattlefieldStrategyAllowedMarket', suggestedExchange: SupportedExchange, suggestedPair: string } | null> | null, owner: { __typename?: 'PublicUser', avatarUrl: string, createdAt: any, description: string, totalFollowers: number, username: string } | { __typename?: 'PrivateUser', avatarUrl: string, createdAt: any, description: string, totalFollowers: number, username: string }, rankingTaskPerformance?: { __typename?: 'TaskPerformance', longestTimeToMakeNewHigh?: number | null } | null, strategy: { __typename?: 'Strategy', id: string, isLivetradableForCurrentUser: { __typename?: 'IsLivetradableForCurrentUserResult', isLivetradable: boolean, reason: string, reasonType: IsLivetradableReasonType } } } };

export type BrokerChartTaskQueryVariables = Exact<{
  id: Scalars['ID']['input'];
  from?: InputMaybe<Scalars['Date']['input']>;
  to?: InputMaybe<Scalars['Date']['input']>;
  pointsCount?: InputMaybe<Scalars['Int']['input']>;
}>;


export type BrokerChartTaskQuery = { __typename?: 'Query', getBroker: { __typename?: 'BacktestTask', id: string, lockGainRatio?: number | null, stopLossRatio?: number | null, initBaseAmount: number, profitVector: { __typename?: 'ProfitVector', points: Array<{ __typename?: 'ProfitVectorPoint', time: any, profit: number, marketPrices: Array<{ __typename?: 'MarketPricePoint', currency: string, price: number }> }> }, ops: Array<{ __typename?: 'BrokerTaskOps', id: string, time: any, price: number, amount: number }> } | { __typename?: 'SimulationTask', id: string, lockGainRatio?: number | null, stopLossRatio?: number | null, initBaseAmount: number, profitVector: { __typename?: 'ProfitVector', points: Array<{ __typename?: 'ProfitVectorPoint', time: any, profit: number, marketPrices: Array<{ __typename?: 'MarketPricePoint', currency: string, price: number }> }> }, ops: Array<{ __typename?: 'BrokerTaskOps', id: string, time: any, price: number, amount: number }> } | { __typename?: 'TraderTask', id: string, lockGainRatio?: number | null, stopLossRatio?: number | null, initBaseAmount: number, profitVector: { __typename?: 'ProfitVector', points: Array<{ __typename?: 'ProfitVectorPoint', time: any, profit: number, marketPrices: Array<{ __typename?: 'MarketPricePoint', currency: string, price: number }> }> }, ops: Array<{ __typename?: 'BrokerTaskOps', id: string, time: any, price: number, amount: number }> } };

export type MarketplaceMoreStrategiesQueryVariables = Exact<{
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  filter: BattlefieldStrategyFilterInput;
}>;


export type MarketplaceMoreStrategiesQuery = { __typename?: 'Query', getBattlefieldStrategiesByFilter: Array<{ __typename?: 'BattlefieldStrategy', id: string, rankingRoi?: number | null, strategyName?: string | null, owner: { __typename?: 'PublicUser', username: string, avatarUrl: string } | { __typename?: 'PrivateUser', username: string, avatarUrl: string } }> };

export type ArenaMarketplaceBrokerQueryVariables = Exact<{
  brokerId: Scalars['ID']['input'];
}>;


export type ArenaMarketplaceBrokerQuery = { __typename?: 'Query', getBroker: { __typename?: 'BacktestTask', strategyInvestmentType?: string | null, roi?: number | null, targetExchangePair: any, baseExchange: SupportedExchange, initBaseAmount: number, performance: { __typename?: 'TaskPerformance', profitFactor?: number | null, averageTradePerMonth?: number | null } } | { __typename?: 'SimulationTask', strategyInvestmentType?: string | null, roi?: number | null, targetExchangePair: any, baseExchange: SupportedExchange, initBaseAmount: number, performance: { __typename?: 'TaskPerformance', profitFactor?: number | null, averageTradePerMonth?: number | null } } | { __typename?: 'TraderTask', strategyInvestmentType?: string | null, roi?: number | null, targetExchangePair: any, baseExchange: SupportedExchange, initBaseAmount: number, performance: { __typename?: 'TaskPerformance', profitFactor?: number | null, averageTradePerMonth?: number | null } } };

export type GetLivetradingPairsQueryVariables = Exact<{
  investmentType?: InputMaybe<StrategyInvestmentType>;
  exchange: SupportedExchange;
}>;


export type GetLivetradingPairsQuery = { __typename?: 'Query', getLivetradingPairs: Array<{ __typename?: 'PairsMarket', symbol: string, settle?: string | null, base: string, quote: string }> };

export type GetUserAdvancedSettingsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetUserAdvancedSettingsQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, passwordStrength: string, isEnable2Fa: boolean } | { __typename?: 'PrivateUser', id: string, passwordStrength: string, isEnable2Fa: boolean } };

export type ArenaStrategyAllowedMarketsQueryVariables = Exact<{
  battlefieldStrategyId: Scalars['ID']['input'];
}>;


export type ArenaStrategyAllowedMarketsQuery = { __typename?: 'Query', getBattlefieldStrategy: { __typename?: 'BattlefieldStrategy', battlefieldStrategyAllowedMarkets?: Array<{ __typename?: 'BattlefieldStrategyAllowedMarket', id: string, suggestedExchange: SupportedExchange, suggestedPair: string } | null> | null } };

export type GetExchangeApiKeyQueryVariables = Exact<{
  apiKeyId?: InputMaybe<Scalars['Int']['input']>;
  investmentType?: InputMaybe<StrategyInvestmentType>;
  pair: Scalars['String']['input'];
}>;


export type GetExchangeApiKeyQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, exchangeApiKeys: Array<{ __typename?: 'ExchangeApiKey', id: string, comment?: string | null, exchange: SupportedExchange, apiKey: string, createdAt: any, updatedAt: any, status: ExchangeApiKeyStatus, restrictedLeverage?: number | null, exchangeAssets: Array<{ __typename?: 'ExchangeAsset', currency: string, amount: number, lockedAmount: number }> }> } | { __typename?: 'PrivateUser', id: string, exchangeApiKeys: Array<{ __typename?: 'ExchangeApiKey', id: string, comment?: string | null, exchange: SupportedExchange, apiKey: string, createdAt: any, updatedAt: any, status: ExchangeApiKeyStatus, restrictedLeverage?: number | null, exchangeAssets: Array<{ __typename?: 'ExchangeAsset', currency: string, amount: number, lockedAmount: number }> }> } };

export type GetTraderTasksCountQueryVariables = Exact<{
  filter: TraderTaskFilterInput;
}>;


export type GetTraderTasksCountQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, traderTasksCountByFilter: number } | { __typename?: 'PrivateUser', id: string, traderTasksCountByFilter: number } };

export type GetUserWalletInfoQueryVariables = Exact<{ [key: string]: never; }>;


export type GetUserWalletInfoQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, userWallets?: Array<{ __typename?: 'UserWallet', currencyName: string, exchangeRate: number, balance: string, availableBalance: string, withdrawalAddresses?: Array<{ __typename?: 'WithdrawalAddresses', address: string }> | null }> | null } | { __typename?: 'PrivateUser', id: string, userWallets?: Array<{ __typename?: 'UserWallet', currencyName: string, exchangeRate: number, balance: string, availableBalance: string, withdrawalAddresses?: Array<{ __typename?: 'WithdrawalAddresses', address: string }> | null }> | null } };

export type UpdateUserFirstNameMutationVariables = Exact<{
  name: Scalars['String']['input'];
}>;


export type UpdateUserFirstNameMutation = { __typename?: 'Mutation', updateUserFirstName: { __typename?: 'UpdateUserFirstNameResult', isOk: boolean } };

export type GetUserApiKeysQueryVariables = Exact<{ [key: string]: never; }>;


export type GetUserApiKeysQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', exchangeApiKeys: Array<{ __typename?: 'ExchangeApiKey', id: string, exchange: SupportedExchange, comment?: string | null, apiKey: string, status: ExchangeApiKeyStatus }> } | { __typename?: 'PrivateUser', exchangeApiKeys: Array<{ __typename?: 'ExchangeApiKey', id: string, exchange: SupportedExchange, comment?: string | null, apiKey: string, status: ExchangeApiKeyStatus }> } };

export type UserArenaSubscriptionsQueryVariables = Exact<{ [key: string]: never; }>;


export type UserArenaSubscriptionsQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', battlefieldStrategySubscriptions: Array<{ __typename?: 'BattlefieldStrategySubscription', price: string, priceUnit: string, startedAt: any, expiredAt: any, paymentStatus: SubscriptionPaymentStatus, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', strategyName?: string | null, strategy: { __typename?: 'Strategy', id: string } } | null }> } | { __typename?: 'PrivateUser', battlefieldStrategySubscriptions: Array<{ __typename?: 'BattlefieldStrategySubscription', price: string, priceUnit: string, startedAt: any, expiredAt: any, paymentStatus: SubscriptionPaymentStatus, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', strategyName?: string | null, strategy: { __typename?: 'Strategy', id: string } } | null }> } };

export type UserArenaPaymentsQueryVariables = Exact<{ [key: string]: never; }>;


export type UserArenaPaymentsQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', payments: Array<{ __typename?: 'Payment', address: string, status: PaymentStatus, currency: string, amountDue: string, discrepancy: string, txHash?: string | null, createdAt: any, updatedAt: any }> } | { __typename?: 'PrivateUser', payments: Array<{ __typename?: 'Payment', address: string, status: PaymentStatus, currency: string, amountDue: string, discrepancy: string, txHash?: string | null, createdAt: any, updatedAt: any }> } };

export type GetBacktestConfigTaskQueryVariables = Exact<{
  strategyId: Scalars['ID']['input'];
}>;


export type GetBacktestConfigTaskQuery = { __typename?: 'Query', getStrategy: { __typename?: 'Strategy', id: string, name?: string | null, investmentType?: StrategyInvestmentType | null, baseExchange?: SupportedExchange | null, baseCurrency?: string | null, targetCurrency?: string | null, leverage: number, type: StrategyType, isParameterized: boolean, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', suggestedLeverage: number, suggestedLeverageLowerBound: number, suggestedLeverageUpperBound: number, suggestedMinInvestmentAmount?: number | null, suggestedStopPoints?: number | null, suggestedTakeProfit?: number | null, chargingMethod: BattlefieldStrategyChargingMethod, profitSharingRatio: number, profitSharingRatioAfterDiscount: number, subscriptionPrice?: number | null } | null, strategyTaskConfigs?: Array<{ __typename?: 'StrategyTaskConfig', id: string, label: string, rangeStart?: any | null, rangeEnd?: any | null, spread?: number | null, fee?: number | null, initBaseAmount?: number | null }> | null } };

export type ChangeStrategyTemplateMutationVariables = Exact<{
  templateId: Scalars['ID']['input'];
  strategyId: Scalars['ID']['input'];
}>;


export type ChangeStrategyTemplateMutation = { __typename?: 'Mutation', changeStrategyTemplate?: { __typename?: 'Strategy', id: string } | null };

export type GetStrategyQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type GetStrategyQuery = { __typename?: 'Query', getStrategy: { __typename?: 'Strategy', id: string, name?: string | null, templateName?: string | null, templateSerialNumber?: number | null, desc: string, note?: string | null, code?: string | null, binaryFilename?: string | null, language?: SupportStrategyLanguage | null, remoteToken?: string | null, type: StrategyType, isBacktesting: boolean, isSimulating: boolean, isTrading: boolean, isRemote: boolean, isBinary: boolean, isConnected?: boolean | null, isBacktestable: boolean, updatedAt: any, createdAt: any, version: number, investmentType?: StrategyInvestmentType | null, investmentTrend?: StrategyInvestmentTrend | null, baseExchange?: SupportedExchange | null, targetCurrency?: string | null, baseCurrency?: string | null, status: StrategyStatus, leverage: number, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', id: string, competitionId?: string | null, suggestedLeverageUpperBound: number, suggestedLeverageLowerBound: number } | null, strategyTaskConfigs?: Array<{ __typename?: 'StrategyTaskConfig', id: string, label: string, rangeStart?: any | null, rangeEnd?: any | null, spread?: number | null, fee?: number | null, initBaseAmount?: number | null }> | null, options?: Array<{ __typename?: 'StrategyOption', id: string, name: string, type: StrategyOptionType, defaultValue: string, desc: string, tips?: string | null }> | null } };

export type CreateStrategyByTemplateMutationVariables = Exact<{
  templateId: Scalars['ID']['input'];
  strategy: StrategyTemplateInput;
  recipe?: InputMaybe<Scalars['JSON']['input']>;
}>;


export type CreateStrategyByTemplateMutation = { __typename?: 'Mutation', createStrategyByTemplate?: { __typename?: 'Strategy', id: string, name?: string | null, desc: string, note?: string | null, binaryFilename?: string | null, language?: SupportStrategyLanguage | null, remoteToken?: string | null, type: StrategyType, isRemote: boolean, updatedAt: any, createdAt: any, version: number, options?: Array<{ __typename?: 'StrategyOption', id: string, name: string, type: StrategyOptionType, defaultValue: string, desc: string, tips?: string | null }> | null } | null };

export type CreateSubscriptionPaymentMutationVariables = Exact<{
  bfsSubscriptionId: Scalars['ID']['input'];
  network?: InputMaybe<PaymentNetwork>;
}>;


export type CreateSubscriptionPaymentMutation = { __typename?: 'Mutation', createBattlefieldStrategySubscriptionPayment: { __typename?: 'Payment', id: string, network: PaymentNetwork, address: string, currency: string, amountDue: string, expiredAt?: any | null } };

export type UpdateSubscriptionPaymentMutationVariables = Exact<{
  paymentId: Scalars['ID']['input'];
}>;


export type UpdateSubscriptionPaymentMutation = { __typename?: 'Mutation', updateAndGetPayment: { __typename?: 'Payment', id: string, status: PaymentStatus, network: PaymentNetwork, address: string, currency: string, amountDue: string, txHash?: string | null } };

export type GetApiVersionQueryVariables = Exact<{ [key: string]: never; }>;


export type GetApiVersionQuery = { __typename?: 'Query', apiVersion: { __typename?: 'VersionInfo', lastCommitTime: any, lastCommitHash: string } };

export type ArenaLiveTradeStrategyQueryVariables = Exact<{
  strategyId: Scalars['ID']['input'];
}>;


export type ArenaLiveTradeStrategyQuery = { __typename?: 'Query', getStrategy: { __typename?: 'Strategy', battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', id: string, strategyName?: string | null, chargingMethod: BattlefieldStrategyChargingMethod, suggestedInvestmentType?: StrategyInvestmentType | null, suggestedExchange?: SupportedExchange | null, suggestedPair?: string | null, suggestedLeverage: number, suggestedLeverageUpperBound: number, suggestedLeverageLowerBound: number, suggestedMinInvestmentAmount?: number | null, suggestedStopPoints?: number | null, suggestedTakeProfit?: number | null, subscriptionPlans?: Array<{ __typename?: 'BattlefieldStrategySubscriptionPlan', id: number, period: number, periodUnit: string, originalPrice: number, discountedPrice?: number | null, priceUnit: string, exchange?: SupportedExchange | null }> | null } | null } };

export type GetMyPortfolioDataQueryVariables = Exact<{
  filter: TraderTaskFilterInput;
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetMyPortfolioDataQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, traderTasksCountByFilter: number, traderTasksByFilter: Array<{ __typename?: 'TraderTask', id: string, createdAt: any, updatedAt: any, rangeEnd?: any | null, strategyInvestmentType?: string | null, strategyInvestmentTrend?: string | null, roi?: number | null, status: BrokerStatus, strategyId: string, targetExchangePair: any, lockGainRatio?: number | null, stopLossRatio?: number | null, baseExchange: SupportedExchange, autoClosePosition?: boolean | null, chargingMethod?: BrokerChargingMethod | null, strategy?: { __typename?: 'Strategy', id: string, name?: string | null, investmentType?: StrategyInvestmentType | null, investmentTrend?: StrategyInvestmentTrend | null, templateName?: string | null, templateSerialNumber?: number | null, owner: { __typename?: 'PublicUser', id: string, avatarUrl: string } | { __typename?: 'PrivateUser', id: string, avatarUrl: string } } | null, exchangeApiKeys?: Array<{ __typename?: 'ExchangeApiKeyInfo', comment?: string | null, apiKey: string }> | null }> } | { __typename?: 'PrivateUser', id: string, traderTasksCountByFilter: number, traderTasksByFilter: Array<{ __typename?: 'TraderTask', id: string, createdAt: any, updatedAt: any, rangeEnd?: any | null, strategyInvestmentType?: string | null, strategyInvestmentTrend?: string | null, roi?: number | null, status: BrokerStatus, strategyId: string, targetExchangePair: any, lockGainRatio?: number | null, stopLossRatio?: number | null, baseExchange: SupportedExchange, autoClosePosition?: boolean | null, chargingMethod?: BrokerChargingMethod | null, strategy?: { __typename?: 'Strategy', id: string, name?: string | null, investmentType?: StrategyInvestmentType | null, investmentTrend?: StrategyInvestmentTrend | null, templateName?: string | null, templateSerialNumber?: number | null, owner: { __typename?: 'PublicUser', id: string, avatarUrl: string } | { __typename?: 'PrivateUser', id: string, avatarUrl: string } } | null, exchangeApiKeys?: Array<{ __typename?: 'ExchangeApiKeyInfo', comment?: string | null, apiKey: string }> | null }> } };

export type MeWithUserReferrerQueryVariables = Exact<{ [key: string]: never; }>;


export type MeWithUserReferrerQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', username: string, referrer?: { __typename?: 'PublicUser', id: string, username: string, email?: string | null } | { __typename?: 'PrivateUser', id: string, username: string, email?: string | null } | null, userWallets?: Array<{ __typename?: 'UserWallet', currencyName: string, balance: string, withdrawalAddresses?: Array<{ __typename?: 'WithdrawalAddresses', tokenSymbol?: string | null }> | null }> | null } | { __typename?: 'PrivateUser', username: string, referrer?: { __typename?: 'PublicUser', id: string, username: string, email?: string | null } | { __typename?: 'PrivateUser', id: string, username: string, email?: string | null } | null, userWallets?: Array<{ __typename?: 'UserWallet', currencyName: string, balance: string, withdrawalAddresses?: Array<{ __typename?: 'WithdrawalAddresses', tokenSymbol?: string | null }> | null }> | null } };

export type AddStrategyConnectorMutationVariables = Exact<{
  strategyId: Scalars['ID']['input'];
  type: ConnectorTypeEnum;
  name: Scalars['String']['input'];
  recipe: Scalars['JSON']['input'];
}>;


export type AddStrategyConnectorMutation = { __typename?: 'Mutation', addStrategyConnector: { __typename?: 'Connector', id: string, name: string, recipe: any } };

export type StrategyConnectorsQueryVariables = Exact<{
  strategyId: Scalars['ID']['input'];
}>;


export type StrategyConnectorsQuery = { __typename?: 'Query', getStrategyConnectors: Array<{ __typename?: 'Connector', id: string, name: string, recipe: any, type: { __typename?: 'ConnectorType', id: string, name: ConnectorTypeEnum, recipe: any, bound: ConnectorBoundType } } | null> };

export type UpdateConnectorRecipeMutationVariables = Exact<{
  strategyId: Scalars['ID']['input'];
  connectorId: Scalars['ID']['input'];
  recipe: Scalars['JSON']['input'];
  name: Scalars['String']['input'];
}>;


export type UpdateConnectorRecipeMutation = { __typename?: 'Mutation', updateConnectorRecipe: { __typename?: 'Connector', id: string, name: string, recipe: any } };

export type GetExchangeApiKeysWithAssetsByIdQueryVariables = Exact<{
  investmentType?: InputMaybe<StrategyInvestmentType>;
  exchangeApiKeyId?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetExchangeApiKeysWithAssetsByIdQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, exchangeApiKeys: Array<{ __typename?: 'ExchangeApiKey', id: string, exchange: SupportedExchange, comment?: string | null, apiKey: string, status: ExchangeApiKeyStatus, createdAt: any, updatedAt: any, exchangeAssets: Array<{ __typename?: 'ExchangeAsset', currency: string, amount: number, lockedAmount: number }> }> } | { __typename?: 'PrivateUser', id: string, exchangeApiKeys: Array<{ __typename?: 'ExchangeApiKey', id: string, exchange: SupportedExchange, comment?: string | null, apiKey: string, status: ExchangeApiKeyStatus, createdAt: any, updatedAt: any, exchangeAssets: Array<{ __typename?: 'ExchangeAsset', currency: string, amount: number, lockedAmount: number }> }> } };

export type CreateExchangeApiKeyMutationVariables = Exact<{
  apiKey: Scalars['String']['input'];
  apiSecret: Scalars['String']['input'];
  apiPassword: Scalars['String']['input'];
  comment: Scalars['String']['input'];
  exchange: SupportedExchange;
  investmentType?: InputMaybe<StrategyInvestmentType>;
}>;


export type CreateExchangeApiKeyMutation = { __typename?: 'Mutation', createExchangeApiKey?: { __typename?: 'ExchangeApiKey', id: string, exchange: SupportedExchange, comment?: string | null, apiKey: string, status: ExchangeApiKeyStatus, createdAt: any, updatedAt: any, exchangeAssets: Array<{ __typename?: 'ExchangeAsset', currency: string, amount: number, lockedAmount: number }> } | null };

export type GetExchangeApiKeysWithoutAssetsWithLeverageQueryVariables = Exact<{
  investmentType?: InputMaybe<StrategyInvestmentType>;
  pair: Scalars['String']['input'];
}>;


export type GetExchangeApiKeysWithoutAssetsWithLeverageQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, exchangeApiKeys: Array<{ __typename?: 'ExchangeApiKey', id: string, exchange: SupportedExchange, comment?: string | null, apiKey: string, status: ExchangeApiKeyStatus, createdAt: any, updatedAt: any, restrictedLeverage?: number | null }> } | { __typename?: 'PrivateUser', id: string, exchangeApiKeys: Array<{ __typename?: 'ExchangeApiKey', id: string, exchange: SupportedExchange, comment?: string | null, apiKey: string, status: ExchangeApiKeyStatus, createdAt: any, updatedAt: any, restrictedLeverage?: number | null }> } };

export type GetExchangeApiKeysWithoutAssetsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetExchangeApiKeysWithoutAssetsQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, exchangeApiKeys: Array<{ __typename?: 'ExchangeApiKey', id: string, exchange: SupportedExchange, comment?: string | null, apiKey: string, status: ExchangeApiKeyStatus, createdAt: any, updatedAt: any }> } | { __typename?: 'PrivateUser', id: string, exchangeApiKeys: Array<{ __typename?: 'ExchangeApiKey', id: string, exchange: SupportedExchange, comment?: string | null, apiKey: string, status: ExchangeApiKeyStatus, createdAt: any, updatedAt: any }> } };

export type GetUserPlanUsageQueryVariables = Exact<{ [key: string]: never; }>;


export type GetUserPlanUsageQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, apiKeyUsage: number, backtestUsage: number, simulationUsage: number, livetradeUsage: number, subscription: { __typename?: 'UserSubscription', expiredDate?: any | null, plan: { __typename?: 'SubscriptionPlan', name: string, price?: number | null, apiKeyLimits: number, backtestLimits: number, simulationLimits: number, livetradeLimits: number } } } | { __typename?: 'PrivateUser', id: string, apiKeyUsage: number, backtestUsage: number, simulationUsage: number, livetradeUsage: number, subscription: { __typename?: 'UserSubscription', expiredDate?: any | null, plan: { __typename?: 'SubscriptionPlan', name: string, price?: number | null, apiKeyLimits: number, backtestLimits: number, simulationLimits: number, livetradeLimits: number } } } };

export type DefaultFeeQueryVariables = Exact<{
  investmentType: StrategyInvestmentType;
}>;


export type DefaultFeeQuery = { __typename?: 'Query', getSupportedExchanges: Array<{ __typename?: 'ExchangePairsMarket', exchange: SupportedExchange, defaultFee: number }> };

export type StrategyTemplatesQueryVariables = Exact<{
  locale?: InputMaybe<Scalars['String']['input']>;
}>;


export type StrategyTemplatesQuery = { __typename?: 'Query', getStrategyTemplates?: Array<{ __typename?: 'StrategyTemplate', id: string, name?: string | null, desc?: string | null, note?: string | null, isParameterized: boolean, templateType?: StrategyTemplateType | null }> | null };

export type RegisterToMarketplaceStrategyQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type RegisterToMarketplaceStrategyQuery = { __typename?: 'Query', getStrategy: { __typename?: 'Strategy', investmentType?: StrategyInvestmentType | null, investmentTrend?: StrategyInvestmentTrend | null, targetCurrency?: string | null } };

export type LaunchLiveTradeMutationVariables = Exact<{
  input: LaunchTraderInput;
}>;


export type LaunchLiveTradeMutation = { __typename?: 'Mutation', launchTrader: { __typename?: 'TraderTask', id: string, bfsSubscription?: { __typename?: 'BattlefieldStrategySubscription', id: string, paymentStatus: SubscriptionPaymentStatus } | null } };

export type LaunchSimulationMutationVariables = Exact<{
  input: LaunchSimulationInput;
}>;


export type LaunchSimulationMutation = { __typename?: 'Mutation', launchSimulation: { __typename?: 'SimulationTask', id: string } };

export type LaunchBacktestMutationVariables = Exact<{
  input: LaunchBacktestInput;
}>;


export type LaunchBacktestMutation = { __typename?: 'Mutation', launchBacktest: { __typename?: 'BacktestTask', id: string } };

export type GetActableStrategyQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type GetActableStrategyQuery = { __typename?: 'Query', getStrategy: { __typename?: 'Strategy', id: string, name?: string | null, type: StrategyType, desc: string, isRemote: boolean, templateName?: string | null, templateSerialNumber?: number | null, investmentType?: StrategyInvestmentType | null, baseExchange?: SupportedExchange | null, baseCurrency?: string | null, targetCurrency?: string | null, leverage: number, isBacktestable: boolean, options?: Array<{ __typename?: 'StrategyOption', id: string, name: string, type: StrategyOptionType, defaultValue: string, desc: string, tips?: string | null }> | null, strategyTaskConfigs?: Array<{ __typename?: 'StrategyTaskConfig', id: string, label: string, rangeStart?: any | null, rangeEnd?: any | null, spread?: number | null, fee?: number | null, initBaseAmount?: number | null }> | null, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', suggestedLeverage: number, suggestedLeverageUpperBound: number, suggestedLeverageLowerBound: number, battlefieldStrategyAllowedMarkets?: Array<{ __typename?: 'BattlefieldStrategyAllowedMarket', suggestedExchange: SupportedExchange } | null> | null } | null } };

export type RequestUploadOctetStreamMutationVariables = Exact<{
  fileType: OctetStreamType;
}>;


export type RequestUploadOctetStreamMutation = { __typename?: 'Mutation', requestUploadOctetStream: { __typename?: 'requestUploadFileResult', isOk: boolean, filename: string, url: string, fields: any } };

export type GetExchangeApiKeysWithExchangeAssetsQueryVariables = Exact<{
  investmentType?: InputMaybe<StrategyInvestmentType>;
  pair: Scalars['String']['input'];
}>;


export type GetExchangeApiKeysWithExchangeAssetsQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, exchangeApiKeys: Array<{ __typename?: 'ExchangeApiKey', id: string, exchange: SupportedExchange, comment?: string | null, apiKey: string, status: ExchangeApiKeyStatus, createdAt: any, updatedAt: any, restrictedLeverage?: number | null, exchangeAssets: Array<{ __typename?: 'ExchangeAsset', currency: string, amount: number, lockedAmount: number }> }> } | { __typename?: 'PrivateUser', id: string, exchangeApiKeys: Array<{ __typename?: 'ExchangeApiKey', id: string, exchange: SupportedExchange, comment?: string | null, apiKey: string, status: ExchangeApiKeyStatus, createdAt: any, updatedAt: any, restrictedLeverage?: number | null, exchangeAssets: Array<{ __typename?: 'ExchangeAsset', currency: string, amount: number, lockedAmount: number }> }> } };

export type StrategyForNewTaskQueryVariables = Exact<{
  strategyId: Scalars['ID']['input'];
}>;


export type StrategyForNewTaskQuery = { __typename?: 'Query', getStrategy: { __typename?: 'Strategy', id: string, name?: string | null, investmentType?: StrategyInvestmentType | null, baseExchange?: SupportedExchange | null, baseCurrency?: string | null, targetCurrency?: string | null, leverage: number, type: StrategyType, isParameterized: boolean, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', suggestedLeverage: number, suggestedLeverageLowerBound: number, suggestedLeverageUpperBound: number, suggestedMinInvestmentAmount?: number | null, suggestedStopPoints?: number | null, suggestedTakeProfit?: number | null, chargingMethod: BattlefieldStrategyChargingMethod, profitSharingRatio: number, profitSharingRatioAfterDiscount: number, subscriptionPrice?: number | null, subscriptionPriceAfterDiscount?: number | null } | null } };

export type TaskProfitQueryVariables = Exact<{
  id: Scalars['ID']['input'];
  period: Scalars['Int']['input'];
  from?: InputMaybe<Scalars['Date']['input']>;
  to?: InputMaybe<Scalars['Date']['input']>;
}>;


export type TaskProfitQuery = { __typename?: 'Query', getBroker: { __typename?: 'BacktestTask', id: string, profitVector: { __typename?: 'ProfitVector', points: Array<{ __typename?: 'ProfitVectorPoint', time: any, profit: number }> } } | { __typename?: 'SimulationTask', id: string, profitVector: { __typename?: 'ProfitVector', points: Array<{ __typename?: 'ProfitVectorPoint', time: any, profit: number }> } } | { __typename?: 'TraderTask', id: string, profitVector: { __typename?: 'ProfitVector', points: Array<{ __typename?: 'ProfitVectorPoint', time: any, profit: number }> } } };

export type PairForExchangesQueryVariables = Exact<{
  investmentType: StrategyInvestmentType;
}>;


export type PairForExchangesQuery = { __typename?: 'Query', getSupportedExchanges: Array<{ __typename?: 'ExchangePairsMarket', exchange: SupportedExchange, markets?: Array<{ __typename?: 'PairsMarket', base: string, quote: string }> | null }> };

export type NotificationsQueryVariables = Exact<{
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
}>;


export type NotificationsQuery = { __typename?: 'Query', getNotifications: { __typename?: 'NotificationsConnection', totalCount: number, edges: Array<{ __typename?: 'NotificationsEdge', cursor: string, node: { __typename?: 'NotificationEntry', time: any, type: NotificationType, message: string, unseen: boolean, task?: { __typename?: 'BacktestTask', id: string, type: BrokerType, strategy?: { __typename?: 'Strategy', id: string, name?: string | null, owner: { __typename?: 'PublicUser', id: string } | { __typename?: 'PrivateUser', id: string }, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', id: string } | null } | null } | { __typename?: 'SimulationTask', id: string, type: BrokerType, strategy?: { __typename?: 'Strategy', id: string, name?: string | null, owner: { __typename?: 'PublicUser', id: string } | { __typename?: 'PrivateUser', id: string }, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', id: string } | null } | null } | { __typename?: 'TraderTask', id: string, type: BrokerType, strategy?: { __typename?: 'Strategy', id: string, name?: string | null, owner: { __typename?: 'PublicUser', id: string } | { __typename?: 'PrivateUser', id: string }, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', id: string } | null } | null } | null } }>, pageInfo: { __typename?: 'PageInfo', endCursor?: string | null, hasNextPage: boolean } } };

export type SubmitBusinessInquiryMutationVariables = Exact<{
  bii: BusinessInquiryInput;
}>;


export type SubmitBusinessInquiryMutation = { __typename?: 'Mutation', submitBusinessInquiry: string };

export type SetUserNotificationPreferenceMutationVariables = Exact<{
  notificationPreference: Scalars['JSON']['input'];
}>;


export type SetUserNotificationPreferenceMutation = { __typename?: 'Mutation', setUserNotificationPreference?: any | null };

export type UserNotificationQueryVariables = Exact<{ [key: string]: never; }>;


export type UserNotificationQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, email?: string | null, username: string, avatarUrl: string, createdAt: any, updatedAt: any, locale: SupportedLocale, kycStatus?: string | null, role: UserRole, telegramBotChatId?: number | null, telegramBotActivateToken?: string | null, notificationPreference: any } | { __typename?: 'PrivateUser', id: string, email?: string | null, username: string, avatarUrl: string, createdAt: any, updatedAt: any, locale: SupportedLocale, kycStatus?: string | null, role: UserRole, telegramBotChatId?: number | null, telegramBotActivateToken?: string | null, notificationPreference: any } };

export type ArenaBacktestingRecordQueryVariables = Exact<{
  strategyId: Scalars['ID']['input'];
}>;


export type ArenaBacktestingRecordQuery = { __typename?: 'Query', getBattlefieldStrategy: { __typename?: 'BattlefieldStrategy', strategyBacktestSummary?: any | null } };

export type PortfolioFilterPairsQueryVariables = Exact<{ [key: string]: never; }>;


export type PortfolioFilterPairsQuery = { __typename?: 'Query', getSupportedExchanges: Array<{ __typename?: 'ExchangePairsMarket', pairs: Array<string> }> };

export type ChangeDefaultAvatarMutationVariables = Exact<{
  defaultAvatar: DefaultAvatar;
}>;


export type ChangeDefaultAvatarMutation = { __typename?: 'Mutation', changeDefaultAvatar: boolean };

export type GetUserReferralCodeQueryVariables = Exact<{ [key: string]: never; }>;


export type GetUserReferralCodeQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, referralCode: string } | { __typename?: 'PrivateUser', id: string, referralCode: string } };

export type RequestUploadImageMutationVariables = Exact<{
  imageType: ImageType;
}>;


export type RequestUploadImageMutation = { __typename?: 'Mutation', requestUploadImage: { __typename?: 'requestUploadImageResult', isOk: boolean, filename: string, fields: any, url: string } };

export type ConfirmImageUploadedMutationVariables = Exact<{
  filename: Scalars['String']['input'];
  imageType: ImageType;
}>;


export type ConfirmImageUploadedMutation = { __typename?: 'Mutation', confirmImageUploaded: { __typename?: 'ConfirmImageUploadedResult', isOk: boolean } };

export type RequestBindEmailMutationVariables = Exact<{
  email: Scalars['String']['input'];
}>;


export type RequestBindEmailMutation = { __typename?: 'Mutation', requestBindEmailOtp: boolean };

export type BindEmailMutationVariables = Exact<{
  email: Scalars['String']['input'];
  verificationCode: Scalars['String']['input'];
}>;


export type BindEmailMutation = { __typename?: 'Mutation', bindEmail: boolean };

export type DeleteExchangeApiKeyMutationVariables = Exact<{
  exchangeApiKeyId: Scalars['ID']['input'];
}>;


export type DeleteExchangeApiKeyMutation = { __typename?: 'Mutation', deleteExchangeApiKey: string };

export type GetSupportedExchangesQueryVariables = Exact<{
  investmentType?: InputMaybe<StrategyInvestmentType>;
}>;


export type GetSupportedExchangesQuery = { __typename?: 'Query', getSupportedExchanges: Array<{ __typename?: 'ExchangePairsMarket', exchange: SupportedExchange, type: StrategyInvestmentType, defaultFee: number, pairs: Array<string> }> };

export type DeleteStrategyMutationVariables = Exact<{
  strategyId: Scalars['ID']['input'];
}>;


export type DeleteStrategyMutation = { __typename?: 'Mutation', deleteStrategy: string };

export type DuplicateStrategyMutationVariables = Exact<{
  strategyId: Scalars['ID']['input'];
}>;


export type DuplicateStrategyMutation = { __typename?: 'Mutation', duplicateStrategy?: { __typename?: 'Strategy', id: string, name?: string | null } | null };

export type GetPlanDetailsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetPlanDetailsQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, avatarUrl: string, passwordStrength: string, apiKeyUsage: number, backtestUsage: number, simulationUsage: number, livetradeUsage: number, subscription: { __typename?: 'UserSubscription', expiredDate?: any | null, plan: { __typename?: 'SubscriptionPlan', name: string, price?: number | null, apiKeyLimits: number, backtestLimits: number, simulationLimits: number, livetradeLimits: number } } } | { __typename?: 'PrivateUser', id: string, avatarUrl: string, passwordStrength: string, apiKeyUsage: number, backtestUsage: number, simulationUsage: number, livetradeUsage: number, subscription: { __typename?: 'UserSubscription', expiredDate?: any | null, plan: { __typename?: 'SubscriptionPlan', name: string, price?: number | null, apiKeyLimits: number, backtestLimits: number, simulationLimits: number, livetradeLimits: number } } } };

export type GetStrategiesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetStrategiesQuery = { __typename?: 'Query', me: { __typename?: 'PublicUser', id: string, strategies: Array<{ __typename?: 'Strategy', id: string, name?: string | null, desc: string, type: StrategyType, isBacktesting: boolean, isBacktestable: boolean, isSimulating: boolean, isTrading: boolean, isRemote: boolean, isParameterized: boolean, leverage: number, version: number, baseExchange?: SupportedExchange | null, baseCurrency?: string | null, targetCurrency?: string | null, status: StrategyStatus, investmentType?: StrategyInvestmentType | null, investmentTrend?: StrategyInvestmentTrend | null, templateName?: string | null, templateSerialNumber?: number | null, verifiedStatus?: VerifiedStatusLevel | null, options?: Array<{ __typename?: 'StrategyOption', id: string, name: string, type: StrategyOptionType, defaultValue: string, desc: string, tips?: string | null }> | null, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', liveInvestmentAmount: number, totalTradersRoiRealized: number, totalLaunchedInvestmentAmount: number, onboardedAt?: any | null, rankingTaskPerformance?: { __typename?: 'TaskPerformance', roi?: number | null } | null } | null, backtestTasks: Array<{ __typename?: 'BacktestTask', roi?: number | null, profitVector: { __typename?: 'ProfitVector', points: Array<{ __typename?: 'ProfitVectorPoint', time: any, profit: number }> } }>, simulationTasks: Array<{ __typename?: 'SimulationTask', roi?: number | null, profitVector: { __typename?: 'ProfitVector', points: Array<{ __typename?: 'ProfitVectorPoint', time: any, profit: number }> } }>, traderTasks: Array<{ __typename?: 'TraderTask', roi?: number | null, profitVector: { __typename?: 'ProfitVector', points: Array<{ __typename?: 'ProfitVectorPoint', time: any, profit: number }> } }> }> } | { __typename?: 'PrivateUser', id: string, strategies: Array<{ __typename?: 'Strategy', id: string, name?: string | null, desc: string, type: StrategyType, isBacktesting: boolean, isBacktestable: boolean, isSimulating: boolean, isTrading: boolean, isRemote: boolean, isParameterized: boolean, leverage: number, version: number, baseExchange?: SupportedExchange | null, baseCurrency?: string | null, targetCurrency?: string | null, status: StrategyStatus, investmentType?: StrategyInvestmentType | null, investmentTrend?: StrategyInvestmentTrend | null, templateName?: string | null, templateSerialNumber?: number | null, verifiedStatus?: VerifiedStatusLevel | null, options?: Array<{ __typename?: 'StrategyOption', id: string, name: string, type: StrategyOptionType, defaultValue: string, desc: string, tips?: string | null }> | null, battlefieldStrategy?: { __typename?: 'BattlefieldStrategy', liveInvestmentAmount: number, totalTradersRoiRealized: number, totalLaunchedInvestmentAmount: number, onboardedAt?: any | null, rankingTaskPerformance?: { __typename?: 'TaskPerformance', roi?: number | null } | null } | null, backtestTasks: Array<{ __typename?: 'BacktestTask', roi?: number | null, profitVector: { __typename?: 'ProfitVector', points: Array<{ __typename?: 'ProfitVectorPoint', time: any, profit: number }> } }>, simulationTasks: Array<{ __typename?: 'SimulationTask', roi?: number | null, profitVector: { __typename?: 'ProfitVector', points: Array<{ __typename?: 'ProfitVectorPoint', time: any, profit: number }> } }>, traderTasks: Array<{ __typename?: 'TraderTask', roi?: number | null, profitVector: { __typename?: 'ProfitVector', points: Array<{ __typename?: 'ProfitVectorPoint', time: any, profit: number }> } }> }> } };

export type StrategyFilterPairsQueryVariables = Exact<{ [key: string]: never; }>;


export type StrategyFilterPairsQuery = { __typename?: 'Query', getSupportedExchanges: Array<{ __typename?: 'ExchangePairsMarket', pairs: Array<string> }> };

export type ChainUpMarketplaceBrokerQueryVariables = Exact<{
  brokerId: Scalars['ID']['input'];
}>;


export type ChainUpMarketplaceBrokerQuery = { __typename?: 'Query', getBroker: { __typename?: 'BacktestTask', strategyInvestmentType?: string | null, roi?: number | null, targetExchangePair: any, baseExchange: SupportedExchange, initBaseAmount: number, performance: { __typename?: 'TaskPerformance', profitFactor?: number | null, averageTradePerMonth?: number | null } } | { __typename?: 'SimulationTask', strategyInvestmentType?: string | null, roi?: number | null, targetExchangePair: any, baseExchange: SupportedExchange, initBaseAmount: number, performance: { __typename?: 'TaskPerformance', profitFactor?: number | null, averageTradePerMonth?: number | null } } | { __typename?: 'TraderTask', strategyInvestmentType?: string | null, roi?: number | null, targetExchangePair: any, baseExchange: SupportedExchange, initBaseAmount: number, performance: { __typename?: 'TaskPerformance', profitFactor?: number | null, averageTradePerMonth?: number | null } } };

export type ChainUpMarketplaceStrategyQueryVariables = Exact<{
  battlefieldStrategyId: Scalars['ID']['input'];
}>;


export type ChainUpMarketplaceStrategyQuery = { __typename?: 'Query', getBattlefieldStrategy: { __typename?: 'BattlefieldStrategy', chargingMethod: BattlefieldStrategyChargingMethod, duration: BattlefieldStrategyDuration, isWinner: boolean, liveAdoptorCount: number, liveInvestmentAmount: number, onboardedAt?: any | null, profitSharingRatio: number, rankingMdd?: number | null, rankingRoi?: number | null, rankingSharpeRatio?: number | null, rankingTaskId?: string | null, rankingWinRate?: number | null, strategyDesc?: string | null, strategyName?: string | null, suggestedInvestmentTrend?: StrategyInvestmentTrend | null, suggestedInvestmentType?: StrategyInvestmentType | null, suggestedLeverage: number, suggestedMinInvestmentAmount?: number | null, suggestedPair?: string | null, averageHoldingDays: number, owner: { __typename?: 'PublicUser', avatarUrl: string, createdAt: any, description: string, totalFollowers: number, username: string } | { __typename?: 'PrivateUser', avatarUrl: string, createdAt: any, description: string, totalFollowers: number, username: string }, rankingTaskPerformance?: { __typename?: 'TaskPerformance', longestTimeToMakeNewHigh?: number | null } | null, strategy: { __typename?: 'Strategy', id: string, isLivetradableForCurrentUser: { __typename?: 'IsLivetradableForCurrentUserResult', isLivetradable: boolean, reason: string, reasonType: IsLivetradableReasonType } } } };

export type ExternalMarketplaceFilterPairsQueryVariables = Exact<{ [key: string]: never; }>;


export type ExternalMarketplaceFilterPairsQuery = { __typename?: 'Query', getSupportedExchanges: Array<{ __typename?: 'ExchangePairsMarket', exchange: SupportedExchange, pairs: Array<string> }> };

export type GetStrategyNamesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetStrategyNamesQuery = { __typename?: 'Query', getStrategies: Array<{ __typename?: 'Strategy', id: string, name?: string | null }> };

export type RemoveStrategyConnectorMutationVariables = Exact<{
  strategyId: Scalars['ID']['input'];
  connectorId: Scalars['ID']['input'];
}>;


export type RemoveStrategyConnectorMutation = { __typename?: 'Mutation', removeStrategyConnector: boolean };

export type TaskOrdersQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type TaskOrdersQuery = { __typename?: 'Query', getBroker: { __typename?: 'BacktestTask', id: string, ops: Array<{ __typename?: 'BrokerTaskOps', id: string, time: any, price: number, amount: number }> } | { __typename?: 'SimulationTask', id: string, ops: Array<{ __typename?: 'BrokerTaskOps', id: string, time: any, price: number, amount: number }> } | { __typename?: 'TraderTask', id: string, ops: Array<{ __typename?: 'BrokerTaskOps', id: string, time: any, price: number, amount: number }> } };

export type CouponCodeQueryVariables = Exact<{
  code: Scalars['String']['input'];
  battlefieldStrategyId?: InputMaybe<Scalars['Int']['input']>;
}>;


export type CouponCodeQuery = { __typename?: 'Query', getCouponByCode?: { __typename?: 'Coupon', id: number, discountType?: CouponDiscountType | null, discountPercentage?: number | null, discountAmount?: number | null, freeTrialPeriod?: number | null, freeTrialPeriodUnit?: string | null } | null };

export type MetricsSectionBrokerTaskQueryVariables = Exact<{
  taskId: Scalars['ID']['input'];
}>;


export type MetricsSectionBrokerTaskQuery = { __typename?: 'Query', getBroker: { __typename?: 'BacktestTask', id: string, stopLossRatio?: number | null, lockGainRatio?: number | null, leverage: number, strategyInvestmentTrend?: string | null, autoClosePosition?: boolean | null, performance: { __typename?: 'TaskPerformance', startingCapital: number, finalCapital: number, tradeCount: number, roi?: number | null, roiRealized?: number | null, roiUnrealized?: number | null, winRate?: number | null, mddPercentage?: number | null, profitFactor?: number | null, oddsRatio?: number | null, sharpeRatio?: number | null, returnDivDd?: number | null, longestTimeToMakeNewHigh?: number | null, buyAndHoldEquity?: number | null } } | { __typename?: 'SimulationTask', id: string, stopLossRatio?: number | null, lockGainRatio?: number | null, leverage: number, strategyInvestmentTrend?: string | null, autoClosePosition?: boolean | null, performance: { __typename?: 'TaskPerformance', startingCapital: number, finalCapital: number, tradeCount: number, roi?: number | null, roiRealized?: number | null, roiUnrealized?: number | null, winRate?: number | null, mddPercentage?: number | null, profitFactor?: number | null, oddsRatio?: number | null, sharpeRatio?: number | null, returnDivDd?: number | null, longestTimeToMakeNewHigh?: number | null, buyAndHoldEquity?: number | null } } | { __typename?: 'TraderTask', id: string, stopLossRatio?: number | null, lockGainRatio?: number | null, leverage: number, strategyInvestmentTrend?: string | null, autoClosePosition?: boolean | null, performance: { __typename?: 'TaskPerformance', startingCapital: number, finalCapital: number, tradeCount: number, roi?: number | null, roiRealized?: number | null, roiUnrealized?: number | null, winRate?: number | null, mddPercentage?: number | null, profitFactor?: number | null, oddsRatio?: number | null, sharpeRatio?: number | null, returnDivDd?: number | null, longestTimeToMakeNewHigh?: number | null, buyAndHoldEquity?: number | null } } };

export type GetBacktestQueryVariables = Exact<{
  brokerId: Scalars['ID']['input'];
}>;


export type GetBacktestQuery = { __typename?: 'Query', getBroker: { __typename?: 'BacktestTask', progress?: number | null, strategyInvestmentType?: string | null, roi?: number | null, targetExchangePair: any, baseExchange: SupportedExchange, initBaseAmount: number, rangeEnd?: any | null, createdAt: any, ops: Array<{ __typename?: 'BrokerTaskOps', time: any, amount: number, price: number }>, performance: { __typename?: 'TaskPerformance', startingCapital: number, finalCapital: number, tradeCount: number, longestTimeToMakeNewHigh?: number | null, timeToMake21Trades?: number | null, roi?: number | null, winRate?: number | null, mdd?: number | null, mddPercentage?: number | null, oddsRatio?: number | null, profitFactor?: number | null, sharpeRatio?: number | null, returnDivDd?: number | null } } | { __typename?: 'SimulationTask', strategyInvestmentType?: string | null, roi?: number | null, targetExchangePair: any, baseExchange: SupportedExchange, initBaseAmount: number, rangeEnd?: any | null, createdAt: any, ops: Array<{ __typename?: 'BrokerTaskOps', time: any, amount: number, price: number }>, performance: { __typename?: 'TaskPerformance', startingCapital: number, finalCapital: number, tradeCount: number, longestTimeToMakeNewHigh?: number | null, timeToMake21Trades?: number | null, roi?: number | null, winRate?: number | null, mdd?: number | null, mddPercentage?: number | null, oddsRatio?: number | null, profitFactor?: number | null, sharpeRatio?: number | null, returnDivDd?: number | null } } | { __typename?: 'TraderTask', strategyInvestmentType?: string | null, roi?: number | null, targetExchangePair: any, baseExchange: SupportedExchange, initBaseAmount: number, rangeEnd?: any | null, createdAt: any, ops: Array<{ __typename?: 'BrokerTaskOps', time: any, amount: number, price: number }>, performance: { __typename?: 'TaskPerformance', startingCapital: number, finalCapital: number, tradeCount: number, longestTimeToMakeNewHigh?: number | null, timeToMake21Trades?: number | null, roi?: number | null, winRate?: number | null, mdd?: number | null, mddPercentage?: number | null, oddsRatio?: number | null, profitFactor?: number | null, sharpeRatio?: number | null, returnDivDd?: number | null } } };

export type ChainUpBacktestingRecordQueryVariables = Exact<{
  strategyId: Scalars['ID']['input'];
}>;


export type ChainUpBacktestingRecordQuery = { __typename?: 'Query', getBattlefieldStrategy: { __typename?: 'BattlefieldStrategy', strategyBacktestSummary?: any | null } };

export type ChainUpFilteredStrategiesQueryVariables = Exact<{
  filter: BattlefieldStrategyFilterInput;
  offset?: InputMaybe<Scalars['Int']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
}>;


export type ChainUpFilteredStrategiesQuery = { __typename?: 'Query', getBattlefieldStrategiesByFilter: Array<{ __typename?: 'BattlefieldStrategy', id: string, strategyName?: string | null, suggestedPair?: string | null, suggestedInvestmentType?: StrategyInvestmentType | null, suggestedInvestmentTrend?: StrategyInvestmentTrend | null, rankingRoi?: number | null, rankingSharpeRatio?: number | null, rankingMdd?: number | null, rankingWinRate?: number | null, liveAdoptorCount: number, liveInvestmentAmount: number, liveAverageRoi: number, chargingMethod: BattlefieldStrategyChargingMethod, onboardedAt?: any | null, isWinner: boolean, duration: BattlefieldStrategyDuration, strategy: { __typename?: 'Strategy', id: string, isLivetradableForCurrentUser: { __typename?: 'IsLivetradableForCurrentUserResult', isLivetradable: boolean, reasonType: IsLivetradableReasonType, reason: string } }, owner: { __typename?: 'PublicUser', avatarUrl: string } | { __typename?: 'PrivateUser', avatarUrl: string } }> };


export const VerifySiweMessageDocument = gql`
    mutation VerifySiweMessage($message: String!, $signature: String!) {
  verifySiweMessage(message: $message, signature: $signature) {
    isOk
    token
  }
}
    `;

export function useVerifySiweMessageMutation() {
  return Urql.useMutation<VerifySiweMessageMutation, VerifySiweMessageMutationVariables>(VerifySiweMessageDocument);
};
export const LoginWithThirdPartyTokenDocument = gql`
    mutation LoginWithThirdPartyToken($provider: UserThirdPartyProvider!, $token: String!, $exchange: String) {
  loginWithThirdPartyToken(
    provider: $provider
    token: $token
    exchange: $exchange
  )
}
    `;

export function useLoginWithThirdPartyTokenMutation() {
  return Urql.useMutation<LoginWithThirdPartyTokenMutation, LoginWithThirdPartyTokenMutationVariables>(LoginWithThirdPartyTokenDocument);
};
export const SiweNonceDocument = gql`
    mutation SiweNonce($address: String!) {
  getSiweNonce(address: $address)
}
    `;

export function useSiweNonceMutation() {
  return Urql.useMutation<SiweNonceMutation, SiweNonceMutationVariables>(SiweNonceDocument);
};
export const MyIdDocument = gql`
    query MyId {
  me {
    id
  }
}
    `;

export function useMyIdQuery(options?: Omit<Urql.UseQueryArgs<MyIdQueryVariables>, 'query'>) {
  return Urql.useQuery<MyIdQuery, MyIdQueryVariables>({ query: MyIdDocument, ...options });
};
export const RefreshTokenDocument = gql`
    mutation RefreshToken {
  refreshToken
}
    `;

export function useRefreshTokenMutation() {
  return Urql.useMutation<RefreshTokenMutation, RefreshTokenMutationVariables>(RefreshTokenDocument);
};
export const ArenaFilteredStrategiesDocument = gql`
    query ArenaFilteredStrategies($filter: BattlefieldStrategyFilterInput!, $offset: Int, $limit: Int) {
  getBattlefieldStrategiesByFilter(
    filter: $filter
    offset: $offset
    limit: $limit
  ) {
    id
    strategy {
      id
      isLivetradableForCurrentUser {
        isLivetradable
        reasonType
        reason
      }
    }
    strategyName
    suggestedPair
    suggestedInvestmentType
    suggestedInvestmentTrend
    battlefieldStrategyAllowedMarkets {
      suggestedExchange
      suggestedPair
    }
    owner {
      avatarUrl
    }
    rankingRoi
    rankingSharpeRatio
    rankingMdd
    rankingWinRate
    liveAdoptorCount
    liveInvestmentAmount
    liveAverageRoi
    chargingMethod
    onboardedAt
    isWinner
    duration
  }
}
    `;

export function useArenaFilteredStrategiesQuery(options: Omit<Urql.UseQueryArgs<ArenaFilteredStrategiesQueryVariables>, 'query'>) {
  return Urql.useQuery<ArenaFilteredStrategiesQuery, ArenaFilteredStrategiesQueryVariables>({ query: ArenaFilteredStrategiesDocument, ...options });
};
export const RefetchThirdPartyApiKeyDocument = gql`
    mutation RefetchThirdPartyApiKey($exchange: String) {
  refetchThirdPartyApiKey(exchange: $exchange) {
    id
    exchange
  }
}
    `;

export function useRefetchThirdPartyApiKeyMutation() {
  return Urql.useMutation<RefetchThirdPartyApiKeyMutation, RefetchThirdPartyApiKeyMutationVariables>(RefetchThirdPartyApiKeyDocument);
};
export const GetMyTraderTasksDocument = gql`
    query GetMyTraderTasks($first: Int = 15, $after: String = "", $filter: TraderTaskFilterInput = {}, $reverseOrder: Boolean) {
  me {
    id
    traderTasksCursor(
      first: $first
      after: $after
      filter: $filter
      reverseOrder: $reverseOrder
    ) {
      edges {
        node {
          id
          createdAt
          updatedAt
          rangeEnd
          strategyInvestmentType
          strategyInvestmentTrend
          roi
          status
          strategyId
          strategy {
            id
            owner {
              id
              avatarUrl
            }
            name
            investmentType
            investmentTrend
            templateName
            templateSerialNumber
          }
          targetExchangePair
          lockGainRatio
          stopLossRatio
          baseExchange
          autoClosePosition
          exchangeApiKeys {
            comment
            apiKey
          }
          chargingMethod
        }
        cursor
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
}
    `;

export function useGetMyTraderTasksQuery(options?: Omit<Urql.UseQueryArgs<GetMyTraderTasksQueryVariables>, 'query'>) {
  return Urql.useQuery<GetMyTraderTasksQuery, GetMyTraderTasksQueryVariables>({ query: GetMyTraderTasksDocument, ...options });
};
export const GetCopyingTradeBrokerTaskDocument = gql`
    query GetCopyingTradeBrokerTask($taskId: ID!) {
  getBroker(id: $taskId) {
    id
    initBaseAmount
    leverage
    stopLossRatio
    lockGainRatio
    autoClosePosition
    chargingMethod
    exchangeApiKeys {
      id
    }
    bfsSubscription {
      id
      price
      priceUnit
      startedAt
      expiredAt
      paymentStatus
    }
  }
}
    `;

export function useGetCopyingTradeBrokerTaskQuery(options: Omit<Urql.UseQueryArgs<GetCopyingTradeBrokerTaskQueryVariables>, 'query'>) {
  return Urql.useQuery<GetCopyingTradeBrokerTaskQuery, GetCopyingTradeBrokerTaskQueryVariables>({ query: GetCopyingTradeBrokerTaskDocument, ...options });
};
export const LiveTradeBrokerTaskDocument = gql`
    query LiveTradeBrokerTask($taskId: ID!) {
  getBroker(id: $taskId) {
    id
    baseExchange
    createdAt
    rangeEnd
    stopLossRatio
    lockGainRatio
    leverage
    strategyInvestmentType
    autoClosePosition
    baseCurrency
    initAssetsValues {
      currency
      amount
      equivalentQuoteAmount
    }
    profitSharingRatio
    performance {
      startingCapital
      finalCapital
      tradeCount
      roi
      roiRealized
      roiUnrealized
      winRate
      mddPercentage
      profitFactor
      oddsRatio
      sharpeRatio
      returnDivDd
      longestTimeToMakeNewHigh
      buyAndHoldEquity
    }
    strategy {
      targetCurrency
      battlefieldStrategy {
        suggestedStopPoints
        suggestedTakeProfit
      }
    }
  }
}
    `;

export function useLiveTradeBrokerTaskQuery(options: Omit<Urql.UseQueryArgs<LiveTradeBrokerTaskQueryVariables>, 'query'>) {
  return Urql.useQuery<LiveTradeBrokerTaskQuery, LiveTradeBrokerTaskQueryVariables>({ query: LiveTradeBrokerTaskDocument, ...options });
};
export const GetTaskActivitiesDocument = gql`
    query GetTaskActivities($taskId: ID!, $first: Int = 15, $after: String = "", $activityTypes: [BrokerActivityType]) {
  getBroker(id: $taskId) {
    id
    activities(first: $first, after: $after, activityTypes: $activityTypes) {
      totalCount
      edges {
        cursor
        node {
          time
          type
          price
          amount
          orderStatus
          message
          exchange
          pair
        }
      }
      pageInfo {
        endCursor
        hasNextPage
      }
    }
  }
}
    `;

export function useGetTaskActivitiesQuery(options: Omit<Urql.UseQueryArgs<GetTaskActivitiesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetTaskActivitiesQuery, GetTaskActivitiesQueryVariables>({ query: GetTaskActivitiesDocument, ...options });
};
export const StopBrokerTaskDocument = gql`
    mutation StopBrokerTask($taskId: ID!) {
  stopTask(taskId: $taskId) {
    strategy {
      id
      name
    }
  }
}
    `;

export function useStopBrokerTaskMutation() {
  return Urql.useMutation<StopBrokerTaskMutation, StopBrokerTaskMutationVariables>(StopBrokerTaskDocument);
};
export const UpdateLiveTradeSettingsDocument = gql`
    mutation UpdateLiveTradeSettings($taskId: ID!, $userSetting: UpdateTraderUserSettingInput!) {
  updateTraderUserSetting(taskId: $taskId, userSetting: $userSetting)
}
    `;

export function useUpdateLiveTradeSettingsMutation() {
  return Urql.useMutation<UpdateLiveTradeSettingsMutation, UpdateLiveTradeSettingsMutationVariables>(UpdateLiveTradeSettingsDocument);
};
export const ProfileUserInformationDocument = gql`
    query ProfileUserInformation {
  me {
    id
    username
    firstName
    avatarUrl
    email
    userEvmAddresses {
      address
      isPrimary
    }
    apiKeyUsage
    battlefieldStrategySubscriptions {
      battlefieldStrategy {
        strategyName
      }
    }
    payments {
      address
    }
  }
}
    `;

export function useProfileUserInformationQuery(options?: Omit<Urql.UseQueryArgs<ProfileUserInformationQueryVariables>, 'query'>) {
  return Urql.useQuery<ProfileUserInformationQuery, ProfileUserInformationQueryVariables>({ query: ProfileUserInformationDocument, ...options });
};
export const DeleteBrokerTaskDocument = gql`
    mutation DeleteBrokerTask($taskId: ID!) {
  deleteBrokerTask(taskId: $taskId)
}
    `;

export function useDeleteBrokerTaskMutation() {
  return Urql.useMutation<DeleteBrokerTaskMutation, DeleteBrokerTaskMutationVariables>(DeleteBrokerTaskDocument);
};
export const ArenaMarketplaceStrategyDocument = gql`
    query ArenaMarketplaceStrategy($battlefieldStrategyId: ID!) {
  getBattlefieldStrategy(id: $battlefieldStrategyId) {
    chargingMethod
    duration
    isWinner
    liveAdoptorCount
    liveInvestmentAmount
    onboardedAt
    battlefieldStrategyAllowedMarkets {
      suggestedExchange
      suggestedPair
    }
    owner {
      avatarUrl
      createdAt
      description
      totalFollowers
      username
    }
    profitSharingRatio
    rankingMdd
    rankingRoi
    rankingSharpeRatio
    rankingTaskId
    rankingTaskPerformance {
      longestTimeToMakeNewHigh
    }
    rankingWinRate
    strategy {
      id
      isLivetradableForCurrentUser {
        isLivetradable
        reason
        reasonType
      }
    }
    strategyDesc
    strategyName
    suggestedInvestmentTrend
    suggestedInvestmentType
    suggestedLeverage
    suggestedMinInvestmentAmount
    suggestedPair
    averageHoldingDays
  }
}
    `;

export function useArenaMarketplaceStrategyQuery(options: Omit<Urql.UseQueryArgs<ArenaMarketplaceStrategyQueryVariables>, 'query'>) {
  return Urql.useQuery<ArenaMarketplaceStrategyQuery, ArenaMarketplaceStrategyQueryVariables>({ query: ArenaMarketplaceStrategyDocument, ...options });
};
export const BrokerChartTaskDocument = gql`
    query BrokerChartTask($id: ID!, $from: Date, $to: Date, $pointsCount: Int) {
  getBroker(id: $id) {
    id
    lockGainRatio
    stopLossRatio
    initBaseAmount
    profitVector(pointsCount: $pointsCount, from: $from, to: $to) {
      points {
        time
        profit
        marketPrices {
          currency
          price
        }
      }
    }
    ops {
      id
      time
      price
      amount
    }
  }
}
    `;

export function useBrokerChartTaskQuery(options: Omit<Urql.UseQueryArgs<BrokerChartTaskQueryVariables>, 'query'>) {
  return Urql.useQuery<BrokerChartTaskQuery, BrokerChartTaskQueryVariables>({ query: BrokerChartTaskDocument, ...options });
};
export const MarketplaceMoreStrategiesDocument = gql`
    query MarketplaceMoreStrategies($offset: Int, $limit: Int, $filter: BattlefieldStrategyFilterInput!) {
  getBattlefieldStrategiesByFilter(
    offset: $offset
    limit: $limit
    filter: $filter
  ) {
    id
    owner {
      username
      avatarUrl
    }
    rankingRoi
    strategyName
  }
}
    `;

export function useMarketplaceMoreStrategiesQuery(options: Omit<Urql.UseQueryArgs<MarketplaceMoreStrategiesQueryVariables>, 'query'>) {
  return Urql.useQuery<MarketplaceMoreStrategiesQuery, MarketplaceMoreStrategiesQueryVariables>({ query: MarketplaceMoreStrategiesDocument, ...options });
};
export const ArenaMarketplaceBrokerDocument = gql`
    query ArenaMarketplaceBroker($brokerId: ID!) {
  getBroker(id: $brokerId) {
    strategyInvestmentType
    roi
    targetExchangePair
    baseExchange
    performance {
      profitFactor
      averageTradePerMonth
    }
    initBaseAmount
  }
}
    `;

export function useArenaMarketplaceBrokerQuery(options: Omit<Urql.UseQueryArgs<ArenaMarketplaceBrokerQueryVariables>, 'query'>) {
  return Urql.useQuery<ArenaMarketplaceBrokerQuery, ArenaMarketplaceBrokerQueryVariables>({ query: ArenaMarketplaceBrokerDocument, ...options });
};
export const GetLivetradingPairsDocument = gql`
    query GetLivetradingPairs($investmentType: StrategyInvestmentType = USD_M_FUTURES, $exchange: SupportedExchange!) {
  getLivetradingPairs(investmentType: $investmentType, exchange: $exchange) {
    symbol
    settle
    base
    quote
  }
}
    `;

export function useGetLivetradingPairsQuery(options: Omit<Urql.UseQueryArgs<GetLivetradingPairsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetLivetradingPairsQuery, GetLivetradingPairsQueryVariables>({ query: GetLivetradingPairsDocument, ...options });
};
export const GetUserAdvancedSettingsDocument = gql`
    query getUserAdvancedSettings {
  me {
    id
    passwordStrength
    isEnable2Fa
  }
}
    `;

export function useGetUserAdvancedSettingsQuery(options?: Omit<Urql.UseQueryArgs<GetUserAdvancedSettingsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetUserAdvancedSettingsQuery, GetUserAdvancedSettingsQueryVariables>({ query: GetUserAdvancedSettingsDocument, ...options });
};
export const ArenaStrategyAllowedMarketsDocument = gql`
    query ArenaStrategyAllowedMarkets($battlefieldStrategyId: ID!) {
  getBattlefieldStrategy(id: $battlefieldStrategyId) {
    battlefieldStrategyAllowedMarkets {
      id
      suggestedExchange
      suggestedPair
    }
  }
}
    `;

export function useArenaStrategyAllowedMarketsQuery(options: Omit<Urql.UseQueryArgs<ArenaStrategyAllowedMarketsQueryVariables>, 'query'>) {
  return Urql.useQuery<ArenaStrategyAllowedMarketsQuery, ArenaStrategyAllowedMarketsQueryVariables>({ query: ArenaStrategyAllowedMarketsDocument, ...options });
};
export const GetExchangeApiKeyDocument = gql`
    query GetExchangeApiKey($apiKeyId: Int, $investmentType: StrategyInvestmentType = SPOT, $pair: String!) {
  me {
    id
    exchangeApiKeys(exchangeApiKeyId: $apiKeyId) {
      id
      comment
      exchange
      apiKey
      createdAt
      updatedAt
      status
      exchangeAssets(investmentType: $investmentType) {
        currency
        amount
        lockedAmount
      }
      restrictedLeverage(pair: $pair, investmentType: $investmentType)
    }
  }
}
    `;

export function useGetExchangeApiKeyQuery(options: Omit<Urql.UseQueryArgs<GetExchangeApiKeyQueryVariables>, 'query'>) {
  return Urql.useQuery<GetExchangeApiKeyQuery, GetExchangeApiKeyQueryVariables>({ query: GetExchangeApiKeyDocument, ...options });
};
export const GetTraderTasksCountDocument = gql`
    query getTraderTasksCount($filter: TraderTaskFilterInput!) {
  me {
    id
    traderTasksCountByFilter(filter: $filter)
  }
}
    `;

export function useGetTraderTasksCountQuery(options: Omit<Urql.UseQueryArgs<GetTraderTasksCountQueryVariables>, 'query'>) {
  return Urql.useQuery<GetTraderTasksCountQuery, GetTraderTasksCountQueryVariables>({ query: GetTraderTasksCountDocument, ...options });
};
export const GetUserWalletInfoDocument = gql`
    query getUserWalletInfo {
  me {
    id
    userWallets {
      currencyName
      exchangeRate(target: TWD)
      balance
      availableBalance
      withdrawalAddresses {
        address
      }
    }
  }
}
    `;

export function useGetUserWalletInfoQuery(options?: Omit<Urql.UseQueryArgs<GetUserWalletInfoQueryVariables>, 'query'>) {
  return Urql.useQuery<GetUserWalletInfoQuery, GetUserWalletInfoQueryVariables>({ query: GetUserWalletInfoDocument, ...options });
};
export const UpdateUserFirstNameDocument = gql`
    mutation UpdateUserFirstName($name: String!) {
  updateUserFirstName(name: $name) {
    isOk
  }
}
    `;

export function useUpdateUserFirstNameMutation() {
  return Urql.useMutation<UpdateUserFirstNameMutation, UpdateUserFirstNameMutationVariables>(UpdateUserFirstNameDocument);
};
export const GetUserApiKeysDocument = gql`
    query getUserApiKeys {
  me {
    exchangeApiKeys {
      id
      exchange
      comment
      apiKey
      status
    }
  }
}
    `;

export function useGetUserApiKeysQuery(options?: Omit<Urql.UseQueryArgs<GetUserApiKeysQueryVariables>, 'query'>) {
  return Urql.useQuery<GetUserApiKeysQuery, GetUserApiKeysQueryVariables>({ query: GetUserApiKeysDocument, ...options });
};
export const UserArenaSubscriptionsDocument = gql`
    query UserArenaSubscriptions {
  me {
    battlefieldStrategySubscriptions {
      price
      priceUnit
      startedAt
      expiredAt
      paymentStatus
      battlefieldStrategy {
        strategyName
        strategy {
          id
        }
      }
    }
  }
}
    `;

export function useUserArenaSubscriptionsQuery(options?: Omit<Urql.UseQueryArgs<UserArenaSubscriptionsQueryVariables>, 'query'>) {
  return Urql.useQuery<UserArenaSubscriptionsQuery, UserArenaSubscriptionsQueryVariables>({ query: UserArenaSubscriptionsDocument, ...options });
};
export const UserArenaPaymentsDocument = gql`
    query UserArenaPayments {
  me {
    payments {
      address
      status
      currency
      amountDue
      discrepancy
      txHash
      createdAt
      updatedAt
    }
  }
}
    `;

export function useUserArenaPaymentsQuery(options?: Omit<Urql.UseQueryArgs<UserArenaPaymentsQueryVariables>, 'query'>) {
  return Urql.useQuery<UserArenaPaymentsQuery, UserArenaPaymentsQueryVariables>({ query: UserArenaPaymentsDocument, ...options });
};
export const GetBacktestConfigTaskDocument = gql`
    query getBacktestConfigTask($strategyId: ID!) {
  getStrategy(id: $strategyId) {
    id
    name
    investmentType
    baseExchange
    baseCurrency
    targetCurrency
    leverage
    type
    isParameterized
    battlefieldStrategy {
      suggestedLeverage
      suggestedLeverageLowerBound
      suggestedLeverageUpperBound
      suggestedMinInvestmentAmount
      suggestedStopPoints
      suggestedTakeProfit
      chargingMethod
      profitSharingRatio
      profitSharingRatioAfterDiscount
      subscriptionPrice
    }
    strategyTaskConfigs {
      id
      label
      rangeStart
      rangeEnd
      spread
      fee
      initBaseAmount
    }
  }
}
    `;

export function useGetBacktestConfigTaskQuery(options: Omit<Urql.UseQueryArgs<GetBacktestConfigTaskQueryVariables>, 'query'>) {
  return Urql.useQuery<GetBacktestConfigTaskQuery, GetBacktestConfigTaskQueryVariables>({ query: GetBacktestConfigTaskDocument, ...options });
};
export const ChangeStrategyTemplateDocument = gql`
    mutation ChangeStrategyTemplate($templateId: ID!, $strategyId: ID!) {
  changeStrategyTemplate(templateId: $templateId, strategyId: $strategyId) {
    id
  }
}
    `;

export function useChangeStrategyTemplateMutation() {
  return Urql.useMutation<ChangeStrategyTemplateMutation, ChangeStrategyTemplateMutationVariables>(ChangeStrategyTemplateDocument);
};
export const GetStrategyDocument = gql`
    query GetStrategy($id: ID!) {
  getStrategy(id: $id) {
    id
    name
    templateName
    templateSerialNumber
    desc
    note
    code
    binaryFilename
    language
    remoteToken
    type
    isBacktesting
    isSimulating
    isTrading
    isRemote
    isBinary
    isConnected
    isBacktestable
    updatedAt
    createdAt
    version
    investmentType
    investmentTrend
    baseExchange
    targetCurrency
    baseCurrency
    status
    leverage
    battlefieldStrategy {
      id
      competitionId
      suggestedLeverageUpperBound
      suggestedLeverageLowerBound
    }
    strategyTaskConfigs {
      id
      label
      rangeStart
      rangeEnd
      spread
      fee
      initBaseAmount
    }
    options {
      id
      name
      type
      defaultValue
      desc
      tips
    }
  }
}
    `;

export function useGetStrategyQuery(options: Omit<Urql.UseQueryArgs<GetStrategyQueryVariables>, 'query'>) {
  return Urql.useQuery<GetStrategyQuery, GetStrategyQueryVariables>({ query: GetStrategyDocument, ...options });
};
export const CreateStrategyByTemplateDocument = gql`
    mutation CreateStrategyByTemplate($templateId: ID!, $strategy: StrategyTemplateInput!, $recipe: JSON) {
  createStrategyByTemplate(
    templateId: $templateId
    strategy: $strategy
    recipe: $recipe
  ) {
    id
    name
    desc
    note
    binaryFilename
    language
    remoteToken
    type
    isRemote
    updatedAt
    createdAt
    version
    options {
      id
      name
      type
      defaultValue
      desc
      tips
    }
  }
}
    `;

export function useCreateStrategyByTemplateMutation() {
  return Urql.useMutation<CreateStrategyByTemplateMutation, CreateStrategyByTemplateMutationVariables>(CreateStrategyByTemplateDocument);
};
export const CreateSubscriptionPaymentDocument = gql`
    mutation CreateSubscriptionPayment($bfsSubscriptionId: ID!, $network: PaymentNetwork = ETHEREUM) {
  createBattlefieldStrategySubscriptionPayment(
    battlefieldStrategySubscriptionId: $bfsSubscriptionId
    network: $network
  ) {
    id
    network
    address
    currency
    amountDue
    expiredAt
  }
}
    `;

export function useCreateSubscriptionPaymentMutation() {
  return Urql.useMutation<CreateSubscriptionPaymentMutation, CreateSubscriptionPaymentMutationVariables>(CreateSubscriptionPaymentDocument);
};
export const UpdateSubscriptionPaymentDocument = gql`
    mutation UpdateSubscriptionPayment($paymentId: ID!) {
  updateAndGetPayment(paymentId: $paymentId) {
    id
    status
    network
    address
    currency
    amountDue
    txHash
  }
}
    `;

export function useUpdateSubscriptionPaymentMutation() {
  return Urql.useMutation<UpdateSubscriptionPaymentMutation, UpdateSubscriptionPaymentMutationVariables>(UpdateSubscriptionPaymentDocument);
};
export const GetApiVersionDocument = gql`
    query GetAPIVersion {
  apiVersion {
    lastCommitTime
    lastCommitHash
  }
}
    `;

export function useGetApiVersionQuery(options?: Omit<Urql.UseQueryArgs<GetApiVersionQueryVariables>, 'query'>) {
  return Urql.useQuery<GetApiVersionQuery, GetApiVersionQueryVariables>({ query: GetApiVersionDocument, ...options });
};
export const ArenaLiveTradeStrategyDocument = gql`
    query ArenaLiveTradeStrategy($strategyId: ID!) {
  getStrategy(id: $strategyId) {
    battlefieldStrategy {
      id
      strategyName
      chargingMethod
      suggestedInvestmentType
      suggestedExchange
      suggestedPair
      suggestedLeverage
      suggestedLeverageUpperBound
      suggestedLeverageLowerBound
      suggestedMinInvestmentAmount
      suggestedStopPoints
      suggestedTakeProfit
      subscriptionPlans {
        id
        period
        periodUnit
        originalPrice
        discountedPrice
        priceUnit
        exchange
      }
    }
  }
}
    `;

export function useArenaLiveTradeStrategyQuery(options: Omit<Urql.UseQueryArgs<ArenaLiveTradeStrategyQueryVariables>, 'query'>) {
  return Urql.useQuery<ArenaLiveTradeStrategyQuery, ArenaLiveTradeStrategyQueryVariables>({ query: ArenaLiveTradeStrategyDocument, ...options });
};
export const GetMyPortfolioDataDocument = gql`
    query getMyPortfolioData($filter: TraderTaskFilterInput!, $offset: Int, $limit: Int) {
  me {
    id
    traderTasksCountByFilter(filter: $filter)
    traderTasksByFilter(
      filter: $filter
      offset: $offset
      limit: $limit
      reverseOrder: true
    ) {
      id
      createdAt
      updatedAt
      rangeEnd
      strategyInvestmentType
      strategyInvestmentTrend
      roi
      status
      strategyId
      strategy {
        id
        owner {
          id
          avatarUrl
        }
        name
        investmentType
        investmentTrend
        templateName
        templateSerialNumber
      }
      targetExchangePair
      lockGainRatio
      stopLossRatio
      baseExchange
      autoClosePosition
      exchangeApiKeys {
        comment
        apiKey
      }
      chargingMethod
    }
  }
}
    `;

export function useGetMyPortfolioDataQuery(options: Omit<Urql.UseQueryArgs<GetMyPortfolioDataQueryVariables>, 'query'>) {
  return Urql.useQuery<GetMyPortfolioDataQuery, GetMyPortfolioDataQueryVariables>({ query: GetMyPortfolioDataDocument, ...options });
};
export const MeWithUserReferrerDocument = gql`
    query MeWithUserReferrer {
  me {
    username
    referrer {
      id
      username
      email
    }
    userWallets {
      currencyName
      balance
      withdrawalAddresses {
        tokenSymbol
      }
    }
  }
}
    `;

export function useMeWithUserReferrerQuery(options?: Omit<Urql.UseQueryArgs<MeWithUserReferrerQueryVariables>, 'query'>) {
  return Urql.useQuery<MeWithUserReferrerQuery, MeWithUserReferrerQueryVariables>({ query: MeWithUserReferrerDocument, ...options });
};
export const AddStrategyConnectorDocument = gql`
    mutation AddStrategyConnector($strategyId: ID!, $type: ConnectorTypeEnum!, $name: String!, $recipe: JSON!) {
  addStrategyConnector(
    strategyId: $strategyId
    name: $name
    type: $type
    recipe: $recipe
  ) {
    id
    name
    recipe
  }
}
    `;

export function useAddStrategyConnectorMutation() {
  return Urql.useMutation<AddStrategyConnectorMutation, AddStrategyConnectorMutationVariables>(AddStrategyConnectorDocument);
};
export const StrategyConnectorsDocument = gql`
    query StrategyConnectors($strategyId: ID!) {
  getStrategyConnectors(strategyId: $strategyId) {
    id
    name
    recipe
    type {
      id
      name
      recipe
      bound
    }
  }
}
    `;

export function useStrategyConnectorsQuery(options: Omit<Urql.UseQueryArgs<StrategyConnectorsQueryVariables>, 'query'>) {
  return Urql.useQuery<StrategyConnectorsQuery, StrategyConnectorsQueryVariables>({ query: StrategyConnectorsDocument, ...options });
};
export const UpdateConnectorRecipeDocument = gql`
    mutation UpdateConnectorRecipe($strategyId: ID!, $connectorId: ID!, $recipe: JSON!, $name: String!) {
  updateConnectorRecipe(
    strategyId: $strategyId
    connectorId: $connectorId
    recipe: $recipe
    name: $name
  ) {
    id
    name
    recipe
  }
}
    `;

export function useUpdateConnectorRecipeMutation() {
  return Urql.useMutation<UpdateConnectorRecipeMutation, UpdateConnectorRecipeMutationVariables>(UpdateConnectorRecipeDocument);
};
export const GetExchangeApiKeysWithAssetsByIdDocument = gql`
    query GetExchangeApiKeysWithAssetsById($investmentType: StrategyInvestmentType = SPOT, $exchangeApiKeyId: Int) {
  me {
    id
    exchangeApiKeys(exchangeApiKeyId: $exchangeApiKeyId) {
      id
      exchange
      comment
      apiKey
      status
      createdAt
      updatedAt
      exchangeAssets(investmentType: $investmentType) {
        currency
        amount
        lockedAmount
      }
    }
  }
}
    `;

export function useGetExchangeApiKeysWithAssetsByIdQuery(options?: Omit<Urql.UseQueryArgs<GetExchangeApiKeysWithAssetsByIdQueryVariables>, 'query'>) {
  return Urql.useQuery<GetExchangeApiKeysWithAssetsByIdQuery, GetExchangeApiKeysWithAssetsByIdQueryVariables>({ query: GetExchangeApiKeysWithAssetsByIdDocument, ...options });
};
export const CreateExchangeApiKeyDocument = gql`
    mutation CreateExchangeApiKey($apiKey: String!, $apiSecret: String!, $apiPassword: String!, $comment: String!, $exchange: SupportedExchange!, $investmentType: StrategyInvestmentType = SPOT) {
  createExchangeApiKey(
    apiKey: $apiKey
    apiSecret: $apiSecret
    apiPassword: $apiPassword
    comment: $comment
    exchange: $exchange
  ) {
    id
    exchange
    comment
    apiKey
    status
    createdAt
    updatedAt
    exchangeAssets(investmentType: $investmentType) {
      currency
      amount
      lockedAmount
    }
  }
}
    `;

export function useCreateExchangeApiKeyMutation() {
  return Urql.useMutation<CreateExchangeApiKeyMutation, CreateExchangeApiKeyMutationVariables>(CreateExchangeApiKeyDocument);
};
export const GetExchangeApiKeysWithoutAssetsWithLeverageDocument = gql`
    query GetExchangeApiKeysWithoutAssetsWithLeverage($investmentType: StrategyInvestmentType = SPOT, $pair: String!) {
  me {
    id
    exchangeApiKeys {
      id
      exchange
      comment
      apiKey
      status
      createdAt
      updatedAt
      restrictedLeverage(investmentType: $investmentType, pair: $pair)
    }
  }
}
    `;

export function useGetExchangeApiKeysWithoutAssetsWithLeverageQuery(options: Omit<Urql.UseQueryArgs<GetExchangeApiKeysWithoutAssetsWithLeverageQueryVariables>, 'query'>) {
  return Urql.useQuery<GetExchangeApiKeysWithoutAssetsWithLeverageQuery, GetExchangeApiKeysWithoutAssetsWithLeverageQueryVariables>({ query: GetExchangeApiKeysWithoutAssetsWithLeverageDocument, ...options });
};
export const GetExchangeApiKeysWithoutAssetsDocument = gql`
    query GetExchangeApiKeysWithoutAssets {
  me {
    id
    exchangeApiKeys {
      id
      exchange
      comment
      apiKey
      status
      createdAt
      updatedAt
    }
  }
}
    `;

export function useGetExchangeApiKeysWithoutAssetsQuery(options?: Omit<Urql.UseQueryArgs<GetExchangeApiKeysWithoutAssetsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetExchangeApiKeysWithoutAssetsQuery, GetExchangeApiKeysWithoutAssetsQueryVariables>({ query: GetExchangeApiKeysWithoutAssetsDocument, ...options });
};
export const GetUserPlanUsageDocument = gql`
    query getUserPlanUsage {
  me {
    id
    apiKeyUsage
    backtestUsage
    simulationUsage
    livetradeUsage
    subscription {
      plan {
        name
        price
        apiKeyLimits
        backtestLimits
        simulationLimits
        livetradeLimits
      }
      expiredDate
    }
  }
}
    `;

export function useGetUserPlanUsageQuery(options?: Omit<Urql.UseQueryArgs<GetUserPlanUsageQueryVariables>, 'query'>) {
  return Urql.useQuery<GetUserPlanUsageQuery, GetUserPlanUsageQueryVariables>({ query: GetUserPlanUsageDocument, ...options });
};
export const DefaultFeeDocument = gql`
    query DefaultFee($investmentType: StrategyInvestmentType!) {
  getSupportedExchanges(investmentType: $investmentType) {
    exchange
    defaultFee
  }
}
    `;

export function useDefaultFeeQuery(options: Omit<Urql.UseQueryArgs<DefaultFeeQueryVariables>, 'query'>) {
  return Urql.useQuery<DefaultFeeQuery, DefaultFeeQueryVariables>({ query: DefaultFeeDocument, ...options });
};
export const StrategyTemplatesDocument = gql`
    query StrategyTemplates($locale: String) {
  getStrategyTemplates(locale: $locale) {
    id
    name
    desc
    note
    isParameterized
    templateType
  }
}
    `;

export function useStrategyTemplatesQuery(options?: Omit<Urql.UseQueryArgs<StrategyTemplatesQueryVariables>, 'query'>) {
  return Urql.useQuery<StrategyTemplatesQuery, StrategyTemplatesQueryVariables>({ query: StrategyTemplatesDocument, ...options });
};
export const RegisterToMarketplaceStrategyDocument = gql`
    query RegisterToMarketplaceStrategy($id: ID!) {
  getStrategy(id: $id) {
    investmentType
    investmentTrend
    targetCurrency
  }
}
    `;

export function useRegisterToMarketplaceStrategyQuery(options: Omit<Urql.UseQueryArgs<RegisterToMarketplaceStrategyQueryVariables>, 'query'>) {
  return Urql.useQuery<RegisterToMarketplaceStrategyQuery, RegisterToMarketplaceStrategyQueryVariables>({ query: RegisterToMarketplaceStrategyDocument, ...options });
};
export const LaunchLiveTradeDocument = gql`
    mutation LaunchLiveTrade($input: LaunchTraderInput!) {
  launchTrader(traderInput: $input) {
    id
    bfsSubscription {
      id
      paymentStatus
    }
  }
}
    `;

export function useLaunchLiveTradeMutation() {
  return Urql.useMutation<LaunchLiveTradeMutation, LaunchLiveTradeMutationVariables>(LaunchLiveTradeDocument);
};
export const LaunchSimulationDocument = gql`
    mutation LaunchSimulation($input: LaunchSimulationInput!) {
  launchSimulation(simulationInput: $input) {
    id
  }
}
    `;

export function useLaunchSimulationMutation() {
  return Urql.useMutation<LaunchSimulationMutation, LaunchSimulationMutationVariables>(LaunchSimulationDocument);
};
export const LaunchBacktestDocument = gql`
    mutation LaunchBacktest($input: LaunchBacktestInput!) {
  launchBacktest(backtestInput: $input) {
    id
  }
}
    `;

export function useLaunchBacktestMutation() {
  return Urql.useMutation<LaunchBacktestMutation, LaunchBacktestMutationVariables>(LaunchBacktestDocument);
};
export const GetActableStrategyDocument = gql`
    query GetActableStrategy($id: ID!) {
  getStrategy(id: $id) {
    id
    name
    type
    desc
    isRemote
    templateName
    templateSerialNumber
    investmentType
    baseExchange
    baseCurrency
    targetCurrency
    leverage
    isBacktestable
    options {
      id
      name
      type
      defaultValue
      desc
      tips
    }
    strategyTaskConfigs {
      id
      label
      rangeStart
      rangeEnd
      spread
      fee
      initBaseAmount
    }
    battlefieldStrategy {
      suggestedLeverage
      suggestedLeverageUpperBound
      suggestedLeverageLowerBound
      battlefieldStrategyAllowedMarkets {
        suggestedExchange
      }
    }
  }
}
    `;

export function useGetActableStrategyQuery(options: Omit<Urql.UseQueryArgs<GetActableStrategyQueryVariables>, 'query'>) {
  return Urql.useQuery<GetActableStrategyQuery, GetActableStrategyQueryVariables>({ query: GetActableStrategyDocument, ...options });
};
export const RequestUploadOctetStreamDocument = gql`
    mutation RequestUploadOctetStream($fileType: OctetStreamType!) {
  requestUploadOctetStream(fileType: $fileType) {
    isOk
    filename
    url
    fields
  }
}
    `;

export function useRequestUploadOctetStreamMutation() {
  return Urql.useMutation<RequestUploadOctetStreamMutation, RequestUploadOctetStreamMutationVariables>(RequestUploadOctetStreamDocument);
};
export const GetExchangeApiKeysWithExchangeAssetsDocument = gql`
    query GetExchangeApiKeysWithExchangeAssets($investmentType: StrategyInvestmentType = SPOT, $pair: String!) {
  me {
    id
    exchangeApiKeys {
      id
      exchange
      comment
      apiKey
      status
      createdAt
      updatedAt
      exchangeAssets(investmentType: $investmentType) {
        currency
        amount
        lockedAmount
      }
      restrictedLeverage(investmentType: $investmentType, pair: $pair)
    }
  }
}
    `;

export function useGetExchangeApiKeysWithExchangeAssetsQuery(options: Omit<Urql.UseQueryArgs<GetExchangeApiKeysWithExchangeAssetsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetExchangeApiKeysWithExchangeAssetsQuery, GetExchangeApiKeysWithExchangeAssetsQueryVariables>({ query: GetExchangeApiKeysWithExchangeAssetsDocument, ...options });
};
export const StrategyForNewTaskDocument = gql`
    query StrategyForNewTask($strategyId: ID!) {
  getStrategy(id: $strategyId) {
    id
    name
    investmentType
    baseExchange
    baseCurrency
    targetCurrency
    leverage
    type
    isParameterized
    battlefieldStrategy {
      suggestedLeverage
      suggestedLeverageLowerBound
      suggestedLeverageUpperBound
      suggestedMinInvestmentAmount
      suggestedStopPoints
      suggestedTakeProfit
      chargingMethod
      profitSharingRatio
      profitSharingRatioAfterDiscount
      subscriptionPrice
      subscriptionPriceAfterDiscount
    }
  }
}
    `;

export function useStrategyForNewTaskQuery(options: Omit<Urql.UseQueryArgs<StrategyForNewTaskQueryVariables>, 'query'>) {
  return Urql.useQuery<StrategyForNewTaskQuery, StrategyForNewTaskQueryVariables>({ query: StrategyForNewTaskDocument, ...options });
};
export const TaskProfitDocument = gql`
    query TaskProfit($id: ID!, $period: Int!, $from: Date, $to: Date) {
  getBroker(id: $id) {
    id
    profitVector(period: $period, from: $from, to: $to) {
      points {
        time
        profit
      }
    }
  }
}
    `;

export function useTaskProfitQuery(options: Omit<Urql.UseQueryArgs<TaskProfitQueryVariables>, 'query'>) {
  return Urql.useQuery<TaskProfitQuery, TaskProfitQueryVariables>({ query: TaskProfitDocument, ...options });
};
export const PairForExchangesDocument = gql`
    query PairForExchanges($investmentType: StrategyInvestmentType!) {
  getSupportedExchanges(investmentType: $investmentType) {
    exchange
    markets {
      base
      quote
    }
  }
}
    `;

export function usePairForExchangesQuery(options: Omit<Urql.UseQueryArgs<PairForExchangesQueryVariables>, 'query'>) {
  return Urql.useQuery<PairForExchangesQuery, PairForExchangesQueryVariables>({ query: PairForExchangesDocument, ...options });
};
export const NotificationsDocument = gql`
    query Notifications($first: Int = 10, $after: String = "") {
  getNotifications(first: $first, after: $after) {
    totalCount
    edges {
      cursor
      node {
        time
        type
        message
        unseen
        task {
          id
          type
          strategy {
            id
            name
            owner {
              id
            }
            battlefieldStrategy {
              id
            }
          }
        }
      }
    }
    pageInfo {
      endCursor
      hasNextPage
    }
  }
}
    `;

export function useNotificationsQuery(options?: Omit<Urql.UseQueryArgs<NotificationsQueryVariables>, 'query'>) {
  return Urql.useQuery<NotificationsQuery, NotificationsQueryVariables>({ query: NotificationsDocument, ...options });
};
export const SubmitBusinessInquiryDocument = gql`
    mutation SubmitBusinessInquiry($bii: BusinessInquiryInput!) {
  submitBusinessInquiry(businessInquiryInput: $bii)
}
    `;

export function useSubmitBusinessInquiryMutation() {
  return Urql.useMutation<SubmitBusinessInquiryMutation, SubmitBusinessInquiryMutationVariables>(SubmitBusinessInquiryDocument);
};
export const SetUserNotificationPreferenceDocument = gql`
    mutation SetUserNotificationPreference($notificationPreference: JSON!) {
  setUserNotificationPreference(notificationPreference: $notificationPreference)
}
    `;

export function useSetUserNotificationPreferenceMutation() {
  return Urql.useMutation<SetUserNotificationPreferenceMutation, SetUserNotificationPreferenceMutationVariables>(SetUserNotificationPreferenceDocument);
};
export const UserNotificationDocument = gql`
    query UserNotification {
  me {
    id
    email
    username
    avatarUrl
    createdAt
    updatedAt
    locale
    kycStatus
    role
    telegramBotChatId
    telegramBotActivateToken
    notificationPreference
  }
}
    `;

export function useUserNotificationQuery(options?: Omit<Urql.UseQueryArgs<UserNotificationQueryVariables>, 'query'>) {
  return Urql.useQuery<UserNotificationQuery, UserNotificationQueryVariables>({ query: UserNotificationDocument, ...options });
};
export const ArenaBacktestingRecordDocument = gql`
    query ArenaBacktestingRecord($strategyId: ID!) {
  getBattlefieldStrategy(id: $strategyId) {
    strategyBacktestSummary
  }
}
    `;

export function useArenaBacktestingRecordQuery(options: Omit<Urql.UseQueryArgs<ArenaBacktestingRecordQueryVariables>, 'query'>) {
  return Urql.useQuery<ArenaBacktestingRecordQuery, ArenaBacktestingRecordQueryVariables>({ query: ArenaBacktestingRecordDocument, ...options });
};
export const PortfolioFilterPairsDocument = gql`
    query PortfolioFilterPairs {
  getSupportedExchanges(investmentType: SPOT) {
    pairs
  }
}
    `;

export function usePortfolioFilterPairsQuery(options?: Omit<Urql.UseQueryArgs<PortfolioFilterPairsQueryVariables>, 'query'>) {
  return Urql.useQuery<PortfolioFilterPairsQuery, PortfolioFilterPairsQueryVariables>({ query: PortfolioFilterPairsDocument, ...options });
};
export const ChangeDefaultAvatarDocument = gql`
    mutation ChangeDefaultAvatar($defaultAvatar: DefaultAvatar!) {
  changeDefaultAvatar(defaultAvatar: $defaultAvatar)
}
    `;

export function useChangeDefaultAvatarMutation() {
  return Urql.useMutation<ChangeDefaultAvatarMutation, ChangeDefaultAvatarMutationVariables>(ChangeDefaultAvatarDocument);
};
export const GetUserReferralCodeDocument = gql`
    query getUserReferralCode {
  me {
    id
    referralCode
  }
}
    `;

export function useGetUserReferralCodeQuery(options?: Omit<Urql.UseQueryArgs<GetUserReferralCodeQueryVariables>, 'query'>) {
  return Urql.useQuery<GetUserReferralCodeQuery, GetUserReferralCodeQueryVariables>({ query: GetUserReferralCodeDocument, ...options });
};
export const RequestUploadImageDocument = gql`
    mutation RequestUploadImage($imageType: ImageType!) {
  requestUploadImage(imageType: $imageType) {
    isOk
    filename
    fields
    url
  }
}
    `;

export function useRequestUploadImageMutation() {
  return Urql.useMutation<RequestUploadImageMutation, RequestUploadImageMutationVariables>(RequestUploadImageDocument);
};
export const ConfirmImageUploadedDocument = gql`
    mutation ConfirmImageUploaded($filename: String!, $imageType: ImageType!) {
  confirmImageUploaded(filename: $filename, imageType: $imageType) {
    isOk
  }
}
    `;

export function useConfirmImageUploadedMutation() {
  return Urql.useMutation<ConfirmImageUploadedMutation, ConfirmImageUploadedMutationVariables>(ConfirmImageUploadedDocument);
};
export const RequestBindEmailDocument = gql`
    mutation RequestBindEmail($email: String!) {
  requestBindEmailOtp(email: $email)
}
    `;

export function useRequestBindEmailMutation() {
  return Urql.useMutation<RequestBindEmailMutation, RequestBindEmailMutationVariables>(RequestBindEmailDocument);
};
export const BindEmailDocument = gql`
    mutation BindEmail($email: String!, $verificationCode: String!) {
  bindEmail(email: $email, otp: $verificationCode)
}
    `;

export function useBindEmailMutation() {
  return Urql.useMutation<BindEmailMutation, BindEmailMutationVariables>(BindEmailDocument);
};
export const DeleteExchangeApiKeyDocument = gql`
    mutation DeleteExchangeApiKey($exchangeApiKeyId: ID!) {
  deleteExchangeApiKey(exchangeApiKeyId: $exchangeApiKeyId)
}
    `;

export function useDeleteExchangeApiKeyMutation() {
  return Urql.useMutation<DeleteExchangeApiKeyMutation, DeleteExchangeApiKeyMutationVariables>(DeleteExchangeApiKeyDocument);
};
export const GetSupportedExchangesDocument = gql`
    query GetSupportedExchanges($investmentType: StrategyInvestmentType) {
  getSupportedExchanges(investmentType: $investmentType) {
    exchange
    type
    defaultFee
    pairs
  }
}
    `;

export function useGetSupportedExchangesQuery(options?: Omit<Urql.UseQueryArgs<GetSupportedExchangesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetSupportedExchangesQuery, GetSupportedExchangesQueryVariables>({ query: GetSupportedExchangesDocument, ...options });
};
export const DeleteStrategyDocument = gql`
    mutation deleteStrategy($strategyId: ID!) {
  deleteStrategy(strategyId: $strategyId)
}
    `;

export function useDeleteStrategyMutation() {
  return Urql.useMutation<DeleteStrategyMutation, DeleteStrategyMutationVariables>(DeleteStrategyDocument);
};
export const DuplicateStrategyDocument = gql`
    mutation duplicateStrategy($strategyId: ID!) {
  duplicateStrategy(strategyId: $strategyId) {
    id
    name
  }
}
    `;

export function useDuplicateStrategyMutation() {
  return Urql.useMutation<DuplicateStrategyMutation, DuplicateStrategyMutationVariables>(DuplicateStrategyDocument);
};
export const GetPlanDetailsDocument = gql`
    query getPlanDetails {
  me {
    id
    avatarUrl
    passwordStrength
    apiKeyUsage
    backtestUsage
    simulationUsage
    livetradeUsage
    subscription {
      plan {
        name
        price
        apiKeyLimits
        backtestLimits
        simulationLimits
        livetradeLimits
      }
      expiredDate
    }
  }
}
    `;

export function useGetPlanDetailsQuery(options?: Omit<Urql.UseQueryArgs<GetPlanDetailsQueryVariables>, 'query'>) {
  return Urql.useQuery<GetPlanDetailsQuery, GetPlanDetailsQueryVariables>({ query: GetPlanDetailsDocument, ...options });
};
export const GetStrategiesDocument = gql`
    query getStrategies {
  me {
    id
    strategies {
      id
      name
      desc
      type
      isBacktesting
      isBacktestable
      isSimulating
      isTrading
      isRemote
      isParameterized
      leverage
      version
      baseExchange
      baseCurrency
      targetCurrency
      status
      investmentType
      investmentTrend
      templateName
      templateSerialNumber
      options {
        id
        name
        type
        defaultValue
        desc
        tips
      }
      verifiedStatus
      battlefieldStrategy {
        liveInvestmentAmount
        totalTradersRoiRealized
        totalLaunchedInvestmentAmount
        rankingTaskPerformance {
          roi
        }
        onboardedAt
      }
      backtestTasks {
        roi
        profitVector {
          points {
            time
            profit
          }
        }
      }
      simulationTasks {
        roi
        profitVector {
          points {
            time
            profit
          }
        }
      }
      traderTasks {
        roi
        profitVector {
          points {
            time
            profit
          }
        }
      }
    }
  }
}
    `;

export function useGetStrategiesQuery(options?: Omit<Urql.UseQueryArgs<GetStrategiesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetStrategiesQuery, GetStrategiesQueryVariables>({ query: GetStrategiesDocument, ...options });
};
export const StrategyFilterPairsDocument = gql`
    query StrategyFilterPairs {
  getSupportedExchanges(investmentType: SPOT) {
    pairs
  }
}
    `;

export function useStrategyFilterPairsQuery(options?: Omit<Urql.UseQueryArgs<StrategyFilterPairsQueryVariables>, 'query'>) {
  return Urql.useQuery<StrategyFilterPairsQuery, StrategyFilterPairsQueryVariables>({ query: StrategyFilterPairsDocument, ...options });
};
export const ChainUpMarketplaceBrokerDocument = gql`
    query ChainUpMarketplaceBroker($brokerId: ID!) {
  getBroker(id: $brokerId) {
    strategyInvestmentType
    roi
    targetExchangePair
    baseExchange
    performance {
      profitFactor
      averageTradePerMonth
    }
    initBaseAmount
  }
}
    `;

export function useChainUpMarketplaceBrokerQuery(options: Omit<Urql.UseQueryArgs<ChainUpMarketplaceBrokerQueryVariables>, 'query'>) {
  return Urql.useQuery<ChainUpMarketplaceBrokerQuery, ChainUpMarketplaceBrokerQueryVariables>({ query: ChainUpMarketplaceBrokerDocument, ...options });
};
export const ChainUpMarketplaceStrategyDocument = gql`
    query ChainUpMarketplaceStrategy($battlefieldStrategyId: ID!) {
  getBattlefieldStrategy(id: $battlefieldStrategyId) {
    chargingMethod
    duration
    isWinner
    liveAdoptorCount
    liveInvestmentAmount
    onboardedAt
    owner {
      avatarUrl
      createdAt
      description
      totalFollowers
      username
    }
    profitSharingRatio
    rankingMdd
    rankingRoi
    rankingSharpeRatio
    rankingTaskId
    rankingTaskPerformance {
      longestTimeToMakeNewHigh
    }
    rankingWinRate
    strategy {
      id
      isLivetradableForCurrentUser {
        isLivetradable
        reason
        reasonType
      }
    }
    strategyDesc
    strategyName
    suggestedInvestmentTrend
    suggestedInvestmentType
    suggestedLeverage
    suggestedMinInvestmentAmount
    suggestedPair
    averageHoldingDays
  }
}
    `;

export function useChainUpMarketplaceStrategyQuery(options: Omit<Urql.UseQueryArgs<ChainUpMarketplaceStrategyQueryVariables>, 'query'>) {
  return Urql.useQuery<ChainUpMarketplaceStrategyQuery, ChainUpMarketplaceStrategyQueryVariables>({ query: ChainUpMarketplaceStrategyDocument, ...options });
};
export const ExternalMarketplaceFilterPairsDocument = gql`
    query ExternalMarketplaceFilterPairs {
  getSupportedExchanges(investmentType: SPOT) {
    exchange
    pairs
  }
}
    `;

export function useExternalMarketplaceFilterPairsQuery(options?: Omit<Urql.UseQueryArgs<ExternalMarketplaceFilterPairsQueryVariables>, 'query'>) {
  return Urql.useQuery<ExternalMarketplaceFilterPairsQuery, ExternalMarketplaceFilterPairsQueryVariables>({ query: ExternalMarketplaceFilterPairsDocument, ...options });
};
export const GetStrategyNamesDocument = gql`
    query GetStrategyNames {
  getStrategies {
    id
    name
  }
}
    `;

export function useGetStrategyNamesQuery(options?: Omit<Urql.UseQueryArgs<GetStrategyNamesQueryVariables>, 'query'>) {
  return Urql.useQuery<GetStrategyNamesQuery, GetStrategyNamesQueryVariables>({ query: GetStrategyNamesDocument, ...options });
};
export const RemoveStrategyConnectorDocument = gql`
    mutation RemoveStrategyConnector($strategyId: ID!, $connectorId: ID!) {
  removeStrategyConnector(strategyId: $strategyId, connectorId: $connectorId)
}
    `;

export function useRemoveStrategyConnectorMutation() {
  return Urql.useMutation<RemoveStrategyConnectorMutation, RemoveStrategyConnectorMutationVariables>(RemoveStrategyConnectorDocument);
};
export const TaskOrdersDocument = gql`
    query TaskOrders($id: ID!) {
  getBroker(id: $id) {
    id
    ops {
      id
      time
      price
      amount
    }
  }
}
    `;

export function useTaskOrdersQuery(options: Omit<Urql.UseQueryArgs<TaskOrdersQueryVariables>, 'query'>) {
  return Urql.useQuery<TaskOrdersQuery, TaskOrdersQueryVariables>({ query: TaskOrdersDocument, ...options });
};
export const CouponCodeDocument = gql`
    query CouponCode($code: String!, $battlefieldStrategyId: Int) {
  getCouponByCode(code: $code, battlefieldStrategyId: $battlefieldStrategyId) {
    id
    discountType
    discountPercentage
    discountAmount
    freeTrialPeriod
    freeTrialPeriodUnit
  }
}
    `;

export function useCouponCodeQuery(options: Omit<Urql.UseQueryArgs<CouponCodeQueryVariables>, 'query'>) {
  return Urql.useQuery<CouponCodeQuery, CouponCodeQueryVariables>({ query: CouponCodeDocument, ...options });
};
export const MetricsSectionBrokerTaskDocument = gql`
    query MetricsSectionBrokerTask($taskId: ID!) {
  getBroker(id: $taskId) {
    id
    stopLossRatio
    lockGainRatio
    leverage
    strategyInvestmentTrend
    autoClosePosition
    performance {
      startingCapital
      finalCapital
      tradeCount
      roi
      roiRealized
      roiUnrealized
      winRate
      mddPercentage
      profitFactor
      oddsRatio
      sharpeRatio
      returnDivDd
      longestTimeToMakeNewHigh
      buyAndHoldEquity
    }
  }
}
    `;

export function useMetricsSectionBrokerTaskQuery(options: Omit<Urql.UseQueryArgs<MetricsSectionBrokerTaskQueryVariables>, 'query'>) {
  return Urql.useQuery<MetricsSectionBrokerTaskQuery, MetricsSectionBrokerTaskQueryVariables>({ query: MetricsSectionBrokerTaskDocument, ...options });
};
export const GetBacktestDocument = gql`
    query GetBacktest($brokerId: ID!) {
  getBroker(id: $brokerId) {
    strategyInvestmentType
    roi
    targetExchangePair
    baseExchange
    initBaseAmount
    rangeEnd
    createdAt
    ops {
      time
      amount
      price
    }
    ... on BacktestTask {
      progress
    }
    performance {
      startingCapital
      finalCapital
      tradeCount
      longestTimeToMakeNewHigh
      timeToMake21Trades
      roi
      winRate
      mdd
      mddPercentage
      oddsRatio
      profitFactor
      sharpeRatio
      returnDivDd
    }
  }
}
    `;

export function useGetBacktestQuery(options: Omit<Urql.UseQueryArgs<GetBacktestQueryVariables>, 'query'>) {
  return Urql.useQuery<GetBacktestQuery, GetBacktestQueryVariables>({ query: GetBacktestDocument, ...options });
};
export const ChainUpBacktestingRecordDocument = gql`
    query ChainUpBacktestingRecord($strategyId: ID!) {
  getBattlefieldStrategy(id: $strategyId) {
    strategyBacktestSummary
  }
}
    `;

export function useChainUpBacktestingRecordQuery(options: Omit<Urql.UseQueryArgs<ChainUpBacktestingRecordQueryVariables>, 'query'>) {
  return Urql.useQuery<ChainUpBacktestingRecordQuery, ChainUpBacktestingRecordQueryVariables>({ query: ChainUpBacktestingRecordDocument, ...options });
};
export const ChainUpFilteredStrategiesDocument = gql`
    query ChainUpFilteredStrategies($filter: BattlefieldStrategyFilterInput!, $offset: Int, $limit: Int) {
  getBattlefieldStrategiesByFilter(
    filter: $filter
    offset: $offset
    limit: $limit
  ) {
    id
    strategy {
      id
      isLivetradableForCurrentUser {
        isLivetradable
        reasonType
        reason
      }
    }
    strategyName
    suggestedPair
    suggestedInvestmentType
    suggestedInvestmentTrend
    owner {
      avatarUrl
    }
    rankingRoi
    rankingSharpeRatio
    rankingMdd
    rankingWinRate
    liveAdoptorCount
    liveInvestmentAmount
    liveAverageRoi
    chargingMethod
    onboardedAt
    isWinner
    duration
  }
}
    `;

export function useChainUpFilteredStrategiesQuery(options: Omit<Urql.UseQueryArgs<ChainUpFilteredStrategiesQueryVariables>, 'query'>) {
  return Urql.useQuery<ChainUpFilteredStrategiesQuery, ChainUpFilteredStrategiesQueryVariables>({ query: ChainUpFilteredStrategiesDocument, ...options });
};
