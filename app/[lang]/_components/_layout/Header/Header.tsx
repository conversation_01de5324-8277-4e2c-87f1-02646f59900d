'use client';

import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Fragment, useState } from 'react';

import { ConnectWallet } from '@/_components/ConnectWallet/ConnectWallet';
import MenuFold from '@/_components/Icon/assets/MenuFold.svg';
import NotificationIcon from '@/_components/Icon/assets/NotificationNormal.svg';
import { tw } from '@/_services/utils/tailwind';
import clsx from 'clsx';
import { LangPopup } from './Menu/LangPopup';
import { NavigationPopup } from './Menu/NavigationPopup';
import { MobileMenu } from './MobileMenu';
import { NotificationPopup } from './Notification/NotificationPopup';
import { getSubmenuItems, menuItems } from './data/menuItems';

type HeaderProp = {
  isTenant?: boolean;
  tenantName?: string;
  tenantLogoUrl?: string;
  showLangSwitcher?: boolean; // optional prop to show/hide the language switcher
};

export function Header({
  isTenant = false,
  tenantName,
  tenantLogoUrl,
  showLangSwitcher = false,
}: HeaderProp) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  const normalizedPathname = pathname?.replace(/^\/[a-z]{2}\//, '/') || '/';

  const isCurrentPageCheck = (item: (typeof menuItems)[0]) => {
    if (item.href) {
      if (item.href === '/') {
        return normalizedPathname === '/';
      }
      return normalizedPathname === item.href || normalizedPathname.startsWith(item.href + '/');
    }

    if (item.hasSubmenu && item.submenuId) {
      const submenuItems = getSubmenuItems(item.submenuId);
      return submenuItems.some(
        (subItem) =>
          normalizedPathname === subItem.href || normalizedPathname.startsWith(subItem.href + '/'),
      );
    }

    return false;
  };

  return (
    <>
      <div
        className="flex w-full items-center 
                  justify-start
                  bg-darkBlue-8
                  bg-gradient-to-t 
                  from-[rgba(0,26,55,0.40)] 
                  via-[rgba(26,36,120,0.40)] 
                  to-[rgba(46,116,181,0.40)]
                  px-6
                  py-3
                  text-[17px]
                  shadow-md
                  2xl:px-4
                  md:px-4"
      >
        {/* Logo Section */}
        <div className="flex items-center gap-4 2xl:pl-5 md:pl-5">
          <Image
            src={tenantLogoUrl || '/static/newLogo.svg'}
            width={193}
            height={32}
            className={clsx(
              tenantLogoUrl ? 'h-full' : 'size-full',
              isTenant && '2xl:pe-[1.125rem] 2xl:ps-1 md:ps-1',
            )}
            alt={`${tenantName || 'Crypto-Arsenal'} Logo`}
          />

          <button
            className="block text-text-3 hover:text-text-1 2xl:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {!isMobileMenuOpen && <MenuFold width={24} height={24} />}
          </button>
        </div>

        {/* Navigation Links - center area */}
        <div className="relative hidden items-center p-6 2xl:flex">
          <div className="flex space-x-[30px] text-[17px] font-semibold leading-normal">
            {menuItems.map((item) => {
              const isCurrentPage = isCurrentPageCheck(item);

              return isTenant && item.hiddenForTenant ? (
                <Fragment key={item.label} />
              ) : (
                <div key={item.label} className="relative">
                  {item.hasSubmenu && item.submenuId ? (
                    <NavigationPopup
                      submenuId={item.submenuId}
                      label={item.label}
                      showHotLabel={item.label === 'White Label Solution'}
                      href={item.href || '#'}
                      isCurrentPage={isCurrentPage}
                      isTenant={isTenant}
                    />
                  ) : (
                    <Link
                      href={item.href || '#'}
                      onClick={!item.href ? (e) => e.preventDefault() : undefined}
                      className={`flex cursor-pointer items-center space-x-2 ${
                        isCurrentPage ? 'text-text-1' : 'text-text-3 hover:text-text-1'
                      }`}
                    >
                      <span>{item.label}</span>
                    </Link>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Language Switcher & Notification and Connect Container */}
        <div className="ml-auto hidden items-center gap-6 2xl:flex 2xl:pr-6 md:flex md:pr-6">
          {/* Connect Button - Hide when mobile menu is open in lg-2xl breakpoint */}
          <div className={tw`${isMobileMenuOpen ? 'xl:invisible' : 'xl:flex'}`}>
            <ConnectWallet />
          </div>

          {/* Notification Icon */}
          <NotificationPopup>
            <div className="group cursor-pointer">
              <NotificationIcon className="size-5 group-hover:opacity-70" />
            </div>
          </NotificationPopup>

          {/* Language Switcher */}
          {showLangSwitcher && <LangPopup />}
        </div>
      </div>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        isTenant={isTenant}
      />
    </>
  );
}
