// Toggle appearance types for better semantic clarity
export type ToggleVariant = 'master' | 'child';

// Helper function to determine toggle CSS classes based on semantic context
const getToggleClasses = (variant: ToggleVariant, disabled: boolean, isOn: boolean) => {
  // For master toggles: always white (unless disabled by Telegram connection)
  // For child toggles: white if not disabled, gray if disabled
  const isWhiteVariant = variant === 'master' || !disabled;

  return {
    button: `relative inline-block h-5 w-10 rounded-full border transition-all ${
      isWhiteVariant ? 'border-text-0' : 'border-[#95ABD3]'
    }`,

    circle: `absolute top-0.5 size-3.5 rounded-full border transition-all ${
      isOn ? 'right-0.5' : 'left-0.5'
    } ${
      isOn
        ? isWhiteVariant
          ? 'bg-text-0 border-transparent' // Solid white fill
          : 'bg-[#95ABD3] border-transparent' // Solid gray fill
        : isWhiteVariant
          ? 'bg-transparent border-text-0' // Transparent white border
          : 'bg-transparent border-[#95ABD3]' // Transparent gray border
    }`,
  };
};

// Toggle component for notification settings
export const Toggle: React.FC<{
  isOn: boolean;
  disabled?: boolean;
  variant: ToggleVariant;
  onChange: (isOn: boolean) => void;
}> = ({ isOn, disabled = false, variant, onChange }) => {
  const classes = getToggleClasses(variant, disabled, isOn);

  return (
    <button
      className={`${classes.button} ${
        disabled ? 'cursor-not-allowed opacity-40' : 'cursor-pointer'
      }`}
      onClick={() => !disabled && onChange(!isOn)}
      disabled={disabled}
    >
      <div className={classes.circle} />
    </button>
  );
};
