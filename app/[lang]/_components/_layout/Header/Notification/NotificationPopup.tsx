'use client';

import ArrowDownGray from '@/_components/Icon/assets/ArrowDownGray.svg';
import ArrowUpG<PERSON> from '@/_components/Icon/assets/ArrowUpGray.svg';
import CAIcon from '@/_components/Icon/assets/CAGrayIcon.svg';
import EmailIcon from '@/_components/Icon/assets/EmailGrayIcon.svg';
import NotificationManIcon from '@/_components/Icon/assets/Notification-man.svg';
import TelegramIcon from '@/_components/Icon/assets/TelegramGrayIcon.svg';
import TelegramLogoHover from '@/_components/Icon/assets/TelegramLogoHover.svg';
import { Popup } from '@/_components/Popup/Popup';
import { Popover, PopoverTrigger } from '@/_components/Popover';
import { useTranslation } from '@/_services/i18n/client';
import { Button as AriaButton, Dialog } from 'react-aria-components';
// eslint-disable-next-line prettier/prettier
import {
  NotificationType,
  NotificationsQuery,
  useNotificationsQuery,
  useSetUserNotificationPreferenceMutation,
  useUserNotificationQuery,
} from '__generated__/types.app';
import Link from 'next/link';
import React, { ReactElement, useCallback, useEffect, useMemo, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroller';
import { Toggle, ToggleVariant } from './_components/Toggle';
import {
  BrokerTaskNameMap,
  DEFAULT_NOTIFICATION_SETTING,
  NotificationChannelType,
  NotificationTypeKey,
} from './consts';
import { formatNotificationTimestamp } from './notificationTimeUtils';

// Type definition for notification settings
type NotificationSettings = Record<NotificationTypeKey, NotificationChannelType[]>;

// Mock data removed - now using real GraphQL data

type NotificationPopupProp = {
  children: ReactElement;
};

// Helper function to get notification type display text
const getNotificationTypeDisplay = (
  data: NotificationsQuery['getNotifications']['edges'][number]['node'],
): string => {
  if (data.task && data.task.strategy) {
    return `Strategy - ${BrokerTaskNameMap[data.task.type]}`;
  }

  // Handle other notification types based on available types
  if (data.type === NotificationType.TaskOp) {
    return 'Strategy';
  }

  return 'Subscription';
};

// NotificationPopupItem component
const NotificationPopupItem: React.FC<{
  data: NotificationsQuery['getNotifications']['edges'][number]['node'];
}> = ({ data }) => {
  const timestamp = formatNotificationTimestamp(data.time);
  const typeDisplay = getNotificationTypeDisplay(data);

  if (data.task && data.task.strategy) {
    const url = `/strategy/${data.task.strategy.id}`;

    return (
      <Link href={url} className="block">
        <div className="w-full cursor-pointer bg-background-5 p-4 transition-colors hover:bg-background-4">
          <div className="mb-2 flex justify-between">
            <div className="text-xs font-normal text-text-3">{typeDisplay}</div>
            <div className="flex gap-6 text-xs font-normal text-text-3">
              <span>{timestamp.date}</span>
              <span>{timestamp.time}</span>
            </div>
          </div>
          <div className="text-sm font-medium">
            <span className="text-primary-1">{data.task.strategy.name}</span>
            <span className="text-text-0"> {data.message}</span>
          </div>
        </div>
      </Link>
    );
  } else {
    return (
      <div className="w-full bg-background-5 p-4">
        <div className="mb-2 flex justify-between">
          <div className="text-xs font-normal text-text-3">{typeDisplay}</div>
          <div className="flex gap-6 text-xs font-normal text-text-3">
            <span>{timestamp.date}</span>
            <span>{timestamp.time}</span>
          </div>
        </div>
        <div className="text-sm font-medium text-text-0">{data.message}</div>
      </div>
    );
  }
};

// EmptyNotificationState component
const EmptyNotificationState: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center gap-y-4 px-[94px]">
      <NotificationManIcon className="size-[168px]" />
      <p className="text-center text-sm font-medium text-coolGray-2">
        Notifications will be shown here whenever they are available. Please customize your
        notifications below.
      </p>
    </div>
  );
};

// Helper function to determine which channels should be disabled
const getDisabledChannels = (
  enabledChannels: NotificationChannelType[],
): NotificationChannelType[] => {
  const disabledChannels: NotificationChannelType[] = [];

  // Disable channels that aren't enabled in the master row
  if (!enabledChannels.includes(NotificationChannelType.TELEGRAM_BOT)) {
    disabledChannels.push(NotificationChannelType.TELEGRAM_BOT);
  }
  if (!enabledChannels.includes(NotificationChannelType.EMAIL)) {
    disabledChannels.push(NotificationChannelType.EMAIL);
  }
  if (!enabledChannels.includes(NotificationChannelType.WEB_APP)) {
    disabledChannels.push(NotificationChannelType.WEB_APP);
  }

  return disabledChannels;
};

// Individual notification setting row
const NotificationSettingRow: React.FC<{
  settings: NotificationSettings;
  settingKey: NotificationTypeKey;
  setSettings: (settings: NotificationSettings) => void;
  variant: ToggleVariant;
  telegramConnected: boolean;
  onTelegramConnect?: () => void;
}> = ({ settings, settingKey, setSettings, variant, telegramConnected, onTelegramConnect }) => {
  if (!settings || !settings[settingKey]) return null;

  const toggleChannel = (channel: NotificationChannelType) => {
    const currentChannels = settings[settingKey] || [];
    const isCurrentlyEnabled = currentChannels.includes(channel);

    // Special handling for Telegram in master row
    if (channel === NotificationChannelType.TELEGRAM_BOT &&
        settingKey === NotificationTypeKey.ENABLED_CHANNELS &&
        !isCurrentlyEnabled &&
        !telegramConnected &&
        onTelegramConnect) {
      // If enabling Telegram but not connected, trigger connection
      onTelegramConnect();
    }

    const newChannels = isCurrentlyEnabled
      ? currentChannels.filter((c: NotificationChannelType) => c !== channel)
      : [...currentChannels, channel];

    setSettings({
      ...settings,
      [settingKey]: newChannels,
    });
  };

  // Get master enabled channels
  const masterEnabledChannels = settings[NotificationTypeKey.ENABLED_CHANNELS] || [];

  // Get disabled channels based on variant and master enabled channels
  const disabledChannels = variant == 'child' ? getDisabledChannels(masterEnabledChannels) : [];

  return (
    <>
      <div className="flex justify-center">
        <Toggle
          isOn={settings[settingKey].includes(NotificationChannelType.TELEGRAM_BOT)}
          disabled={disabledChannels.includes(NotificationChannelType.TELEGRAM_BOT)}
          variant={variant}
          onChange={() => toggleChannel(NotificationChannelType.TELEGRAM_BOT)}
        />
      </div>
      <div className="flex justify-center">
        <Toggle
          isOn={settings[settingKey].includes(NotificationChannelType.EMAIL)}
          disabled={disabledChannels.includes(NotificationChannelType.EMAIL)}
          variant={variant}
          onChange={() => toggleChannel(NotificationChannelType.EMAIL)}
        />
      </div>
      <div className="flex justify-center">
        <Toggle
          isOn={settings[settingKey].includes(NotificationChannelType.WEB_APP)}
          disabled={disabledChannels.includes(NotificationChannelType.WEB_APP)}
          variant={variant}
          onChange={() => toggleChannel(NotificationChannelType.WEB_APP)}
        />
      </div>
    </>
  );
};

// NotificationSettings component with collapsible functionality
interface NotificationSettingsProps {
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
}

const NotificationSettings: React.FC<NotificationSettingsProps> = ({
  isExpanded,
  setIsExpanded,
}) => {
  const { t } = useTranslation();
  const [settings, setSettings] = useState(DEFAULT_NOTIFICATION_SETTING);
  const [shouldRefresh, setShouldRefresh] = useState(false);

  // Real user data from GraphQL
  const [{ data: userData, fetching: userLoading }, refetch] = useUserNotificationQuery();
  const [, setUserNotificationPreference] = useSetUserNotificationPreferenceMutation();

  // Check if user has telegram connected
  const telegramConnected = Boolean(userData?.me?.telegramBotChatId);
  const telegramBotActivateToken = userData?.me?.telegramBotActivateToken;

  // Handle Telegram connection
  const handleTelegramConnect = () => {
    if (telegramBotActivateToken) {
      window.open(
        `https://t.me/OfficialCryptoArsenalBot?start=user_${telegramBotActivateToken}`,
        '_blank',
        'noopener,noreferrer'
      );
      setShouldRefresh(true);
    }
  };
  // Handle Telegram refresh
  async const handleTelegramRefresh = () => {
    await refetch();
    setShouldRefresh(false);
  };

  // Update settings when user data loads
  useEffect(() => {
    if (userData?.me?.notificationPreference && !userLoading) {
      setSettings(userData.me.notificationPreference);
    }
  }, [userData?.me?.notificationPreference, userLoading]);

  // Handle user interactions with settings (save to backend)
  const handleUserSettingsChange = useCallback(
    (newSettings: NotificationSettings) => {
      setSettings(newSettings);
      setUserNotificationPreference({ notificationPreference: newSettings });
    },
    [setUserNotificationPreference],
  );

  return (
    <div className="relative bg-background-6">
      {/* Settings Header - Always visible, entire area is clickable */}
      <div
        className="flex h-14 w-full cursor-pointer items-center justify-between px-4 transition-colors duration-200 hover:bg-background-5"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <span className="font-medium text-text-2">Notification Settings</span>
        <div className="transition-transform duration-300">
          {isExpanded ? <ArrowDownGray className="size-4" /> : <ArrowUpGray className="size-4" />}
        </div>
      </div>

      <div className="h-[2px] bg-background-8 opacity-50" />
      {/* Collapsible Content - slides down within component */}
      <div
        className={`overflow-hidden bg-background-6 transition-all duration-300 ease-in-out ${
          isExpanded ? 'max-h-[365px] opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        {/* Settings Content - always rendered for smooth transitions */}
        <div className="px-10 py-[30px]">
          {/* Settings Grid Header */}
          <div className="mb-[30px] grid grid-cols-[175fr_100fr_100fr_100fr] gap-2 text-center text-sm font-medium">
            <div></div>
            <div className="flex flex-col items-center gap-2">
              {true ? (
                <PopoverTrigger triggerType="hover">
                  <AriaButton
                    onPress={handleTelegramRefresh}
                    className="group relative size-6 cursor-pointer transition-all duration-200 hover:scale-110"
                    isDisabled={shouldRefresh}
                    aria-label={shouldRefresh ? 'Refreshing...' : 'Refresh Telegram connection'}
                  >
                    <div className="relative">
                      <TelegramLogoHover
                        className={`size-6 transition-all duration-300 ${
                          shouldRefresh ? 'animate-pulse opacity-70' : 'opacity-100'
                        }`}
                      />
                      {/* Ripple animation effect */}
                      <div className="absolute inset-0 rounded-full bg-blue-500 opacity-20 animate-ping"></div>
                    </div>
                  </AriaButton>
                  <Popover className="p-2.5" placement="top" showArrow>
                    <Dialog className="outline outline-0" aria-label="Telegram refresh">
                      <p className="text-xs/normal font-medium text-text-2">
                        {shouldRefresh ? t('refreshing') : 'Refresh Now'}
                      </p>
                    </Dialog>
                  </Popover>
                </PopoverTrigger>
              ) : (
                <TelegramIcon className="size-6 opacity-60" />
              )}
              <span className="text-text-2">{t('telegram')}</span>
            </div>
            <div className="flex flex-col items-center gap-2">
              <EmailIcon className="size-6" />
              <span className="text-text-2">{t('email')}</span>
            </div>
            <div className="flex flex-col items-center gap-2">
              <CAIcon className="size-6" />
              <span className="text-text-2">Web</span>
            </div>
          </div>

          {/* Enabled Channels Row */}
          <div className="grid grid-cols-[175fr_100fr_100fr_100fr] items-center gap-2">
            <div className="text-sm text-text-0">{t('enabled')}</div>
            <NotificationSettingRow
              settings={settings}
              settingKey={NotificationTypeKey.ENABLED_CHANNELS}
              setSettings={handleUserSettingsChange}
              variant="master"
              telegramConnected={telegramConnected}
              onTelegramConnect={handleTelegramConnect}
            />
          </div>

          {/* Divider Line */}
          <div className="my-[23px] h-[0.2px] bg-text-0 opacity-50" />

          {/* Settings Rows with 23px spacing */}
          <div className="space-y-[23px]">
            {/* Order Placement */}
            <div className="grid grid-cols-[175fr_100fr_100fr_100fr] items-center gap-2">
              <div className="text-sm text-text-0">{t('chart.order-placement')}</div>
              <NotificationSettingRow
                settings={settings}
                settingKey={NotificationTypeKey.REAL_ORDER}
                setSettings={handleUserSettingsChange}
                variant="child"
                telegramConnected={telegramConnected}
                onTelegramConnect={handleTelegramConnect}
              />
            </div>

            {/* Error Log */}
            <div className="grid grid-cols-[175fr_100fr_100fr_100fr] items-center gap-2">
              <div className="text-sm text-text-0">{t('error-log')}</div>
              <NotificationSettingRow
                settings={settings}
                settingKey={NotificationTypeKey.REAL_LOG_ERROR}
                setSettings={handleUserSettingsChange}
                variant="child"
                telegramConnected={telegramConnected}
                onTelegramConnect={handleTelegramConnect}
              />
            </div>

            {/* Renewal */}
            <div className="grid grid-cols-[175fr_100fr_100fr_100fr] items-center gap-2">
              <div className="text-sm text-text-0">Renewal</div>
              <NotificationSettingRow
                settings={settings}
                settingKey={NotificationTypeKey.WALLET_DEPOSIT}
                setSettings={handleUserSettingsChange}
                variant="child"
                telegramConnected={telegramConnected}
                onTelegramConnect={handleTelegramConnect}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export function NotificationPopup({ children }: NotificationPopupProp) {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [isSettingsExpanded, setIsSettingsExpanded] = useState(false); // Settings panel state

  // Optimized state management - single source of truth
  const [paginationState, setPaginationState] = useState({
    cursor: '',
    allNotifications: [] as NotificationsQuery['getNotifications']['edges'],
    isLoadingMore: false,
  });

  // Memoized query variables - only recreate when cursor changes
  const queryVariables = useMemo(
    () => ({
      first: 10,
      after: paginationState.cursor || undefined, // undefined instead of empty string for initial load
    }),
    [paginationState.cursor],
  );

  // Static query context - never changes
  const queryContext = useMemo(() => ({ expensive: true }), []);

  // Primary GraphQL query
  const [{ data, fetching, error }] = useNotificationsQuery({
    variables: queryVariables,
    context: queryContext,
  });

  // Optimized data handling with single state update
  useEffect(() => {
    if (data?.getNotifications) {
      const newEdges = data.getNotifications.edges;
      const isInitialLoad = paginationState.cursor === '';

      setPaginationState((prev) => {
        if (isInitialLoad) {
          // Initial load - replace all notifications
          return {
            ...prev,
            allNotifications: newEdges,
            isLoadingMore: false,
          };
        } else {
          // Load more - append new notifications, avoiding duplicates
          const existingCursors = new Set(prev.allNotifications.map((edge) => edge.cursor));
          const uniqueNewEdges = newEdges.filter((edge) => !existingCursors.has(edge.cursor));

          return {
            ...prev,
            allNotifications: [...prev.allNotifications, ...uniqueNewEdges],
            isLoadingMore: false,
          };
        }
      });
    }
  }, [data, paginationState.cursor]);

  // Optimized load more function with single state update
  const handleLoadMore = useCallback(() => {
    if (
      paginationState.isLoadingMore ||
      fetching ||
      !data?.getNotifications?.pageInfo.hasNextPage ||
      !data?.getNotifications?.pageInfo.endCursor
    ) {
      return;
    }

    setPaginationState((prev) => ({
      ...prev,
      cursor: data.getNotifications.pageInfo.endCursor || '',
      isLoadingMore: true,
    }));
  }, [
    paginationState.isLoadingMore,
    fetching,
    data?.getNotifications?.pageInfo.hasNextPage,
    data?.getNotifications?.pageInfo.endCursor,
  ]);

  // Optimized reset logic
  useEffect(() => {
    if (isOpen && paginationState.allNotifications.length === 0 && !fetching) {
      // Reset to initial state when opening popup
      setPaginationState({
        cursor: '',
        allNotifications: [],
        isLoadingMore: false,
      });
    }
  }, [isOpen, paginationState.allNotifications.length, fetching]);

  return (
    <>
      {/* Dark overlay that covers the entire page when popup is open */}
      {isOpen && (
        <div className="fixed inset-0 z-40 bg-black/60" onClick={() => setIsOpen(false)} />
      )}

      <Popup
        action={['click']}
        popupVisible={isOpen}
        onPopupVisibleChange={setIsOpen}
        getPopupContainer={() => document.body}
        popupPlacement="topRightFixed"
        popup={
          <div
            className="fixed right-0 top-0 h-[752px] w-[480px] rounded-lg bg-background-8 shadow-lg"
            style={{ zIndex: 9999 }}
          >
            <div className="flex h-full flex-col">
              {/* Header Section - Fixed height, no flex grow */}
              <div className="shrink-0 bg-background-6 px-5 py-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-medium text-text-2">{t('notifications')}</h2>
                </div>
              </div>

              {/* Notification Content Area with Overlay Support - Flexible, shrinks when settings expand */}
              <div className="relative flex min-h-0 flex-1 flex-col">
                {/* Notification List Area - Uses all available space, shrinks when settings expand */}
                <div className="min-h-0 flex-1 pr-2">
                  {fetching && paginationState.cursor === '' ? (
                    <div className="flex items-center justify-center">
                      <div className="text-sm text-text-2">{t('loading')}</div>
                    </div>
                  ) : error ? (
                    <div className="flex h-full items-center justify-center">
                      {error?.graphQLErrors?.[0]?.message?.includes('unauthorized') ? (
                        <div className="text-sm text-text-2">Please Login for Notifications</div>
                      ) : (
                        <div className="text-sm text-text-2">Error Loading Notifications</div>
                      )}
                    </div>
                  ) : paginationState.allNotifications.length > 0 ? (
                    <div className="notification-scrollbar mt-3 h-full overflow-y-scroll pb-2">
                      <InfiniteScroll
                        pageStart={0}
                        loadMore={handleLoadMore}
                        hasMore={data?.getNotifications?.pageInfo.hasNextPage || false}
                        loader={
                          <div
                            key="loader"
                            className="relative z-20 py-3 text-center text-xs text-text-2"
                          >
                            {t('loading')}
                          </div>
                        }
                        useWindow={false}
                      >
                        <div className="space-y-2 pb-5 pl-[17px] pr-4">
                          {paginationState.allNotifications.map((edge, i) => (
                            <NotificationPopupItem key={edge.cursor || i} data={edge.node} />
                          ))}
                        </div>
                      </InfiniteScroll>
                    </div>
                  ) : (
                    <div className="flex h-full items-center justify-center">
                      <EmptyNotificationState />
                    </div>
                  )}
                </div>

                {/* Dark Overlay - only covers notification content area */}
                <div
                  className={`absolute inset-0 z-10 bg-black/40 transition-opacity duration-300 ${
                    isSettingsExpanded ? 'opacity-100' : 'pointer-events-none opacity-0'
                  }`}
                  onClick={() => setIsSettingsExpanded(false)}
                />
              </div>

              {/* Settings Panel - Fixed height, no flex grow, positioned at bottom */}
              <div className="shrink-0">
                <NotificationSettings
                  isExpanded={isSettingsExpanded}
                  setIsExpanded={setIsSettingsExpanded}
                />
              </div>
            </div>
          </div>
        }
      >
        {children}
      </Popup>
    </>
  );
}
