import type { <PERSON>a, <PERSON>Obj } from '@storybook/react';
import { delay, fromValue, pipe } from 'wonka';

import { NotificationPopup } from './NotificationPopup';

const meta: Meta<typeof NotificationPopup> = {
  title: 'Header/Notification/NotificationPopup',
  component: NotificationPopup,
  args: {},
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof NotificationPopup>;

// Simple mock notification data for visual testing
const createMockNotifications = (count: number, startIndex: number = 0) => {
  return Array.from({ length: count }, (_, i) => ({
    cursor: `cursor-${startIndex + i + 1}`,
    node: {
      time: new Date(Date.now() - (startIndex + i) * 3600000).toISOString(),
      type: 'TASK_OP',
      message: `Notification ${startIndex + i + 1}: ${
        (startIndex + i) % 3 === 0
          ? 'Strategy executed successfully'
          : (startIndex + i) % 3 === 1
            ? 'Order placed successfully'
            : 'Position closed with profit'
      }`,
      unseen: startIndex + i < 2,
      task: {
        id: `task-${startIndex + i + 1}`,
        type: 'TRADER',
        strategy: {
          id: `strategy-${startIndex + i + 1}`,
          name: `${(startIndex + i) % 3 === 0 ? 'BTC Long' : (startIndex + i) % 3 === 1 ? 'ETH Scalping' : 'DOGE Moon'} Strategy`,
          owner: { id: 'user-1' },
          battlefieldStrategy: { id: `bf-${startIndex + i + 1}` },
        },
      },
    },
  }));
};

const example = createMockNotifications(15, 0);
console.log(example);

// Simple mock user data with complete notification preferences
const createMockUserData = () => ({
  id: 'user-1',
  email: '<EMAIL>',
  username: 'testuser',
  avatarUrl: 'https://via.placeholder.com/40',
  telegramBotChatId: 123456789,
  notificationPreference: {
    // Use the exact keys from NotificationTypeKey enum
    enabledChannels: ['WEB_APP', 'EMAIL', 'TELEGRAM_BOT'],
    'real.order': ['WEB_APP', 'TELEGRAM_BOT'],
    'real.log-error': ['WEB_APP', 'EMAIL', 'TELEGRAM_BOT'],
    'simulate.order': ['WEB_APP', 'TELEGRAM_BOT'],
    'simulate.log-error': ['WEB_APP', 'TELEGRAM_BOT'],
    'wallet.deposit': ['WEB_APP', 'EMAIL', 'TELEGRAM_BOT'],
    'wallet.withdraw': ['WEB_APP', 'EMAIL', 'TELEGRAM_BOT'],
  },
});

export const Default: Story = {
  parameters: {
    urql: {
      executeQuery: (req) => {
        const queryBody = req.query?.loc?.source?.body || '';

        // Mock notifications query
        if (queryBody.includes('getNotifications')) {
          return fromValue({
            data: {
              getNotifications: {
                totalCount: 8,
                edges: createMockNotifications(8, 0),
                pageInfo: {
                  endCursor: 'cursor-8',
                  hasNextPage: false,
                },
              },
            },
          });
        }

        // Mock user data queries
        if (queryBody.includes('me {')) {
          return fromValue({
            data: { me: createMockUserData() },
          });
        }

        return fromValue({ data: {} });
      },
      executeMutation: () => fromValue({ data: {} }),
      executeSubscription: () => fromValue({ data: {} }),
    },
  },
  render: (args) => (
    <div className="flex h-screen items-center justify-center bg-background-8">
      <NotificationPopup {...args}>
        <button
          className="rounded px-6 py-3 text-text-0"
          style={{ backgroundColor: '#3b82f6', color: 'white' }}
        >
          🔔 Open Notifications (8 Items)
        </button>
      </NotificationPopup>
    </div>
  ),
  play: async () => {
    await window.takeSnapshot?.();
    // Visual test - notification popup should be visible when button is clicked
  },
};

export const SettingsExpanded: Story = {
  parameters: {
    urql: {
      executeQuery: (req) => {
        const queryBody = req.query?.loc?.source?.body || '';

        // Mock notifications query - empty for focus on settings
        if (queryBody.includes('getNotifications')) {
          return fromValue({
            data: {
              getNotifications: {
                totalCount: 0,
                edges: [],
                pageInfo: {
                  endCursor: null,
                  hasNextPage: false,
                },
              },
            },
          });
        }

        // Mock user data queries with complete notification preferences
        if (queryBody.includes('me {')) {
          return fromValue({
            data: { me: createMockUserData() },
          });
        }

        return fromValue({ data: {} });
      },
      executeMutation: (req) => {
        // Mock the settings mutation to handle toggle changes
        const queryBody = req.query?.loc?.source?.body || '';
        if (queryBody.includes('setUserNotificationPreference')) {
          return fromValue({
            data: {
              setUserNotificationPreference: req.variables?.notificationPreference || {},
            },
          });
        }
        return fromValue({ data: {} });
      },
      executeSubscription: () => fromValue({ data: {} }),
    },
  },
  render: (args) => (
    <div className="flex h-screen items-center justify-center bg-background-8">
      <NotificationPopup {...args}>
        <button
          className="rounded px-6 py-3 text-text-0"
          style={{ backgroundColor: '#10b981', color: 'white' }}
        >
          ⚙️ Settings Panel Demo (All Toggles Visible)
        </button>
      </NotificationPopup>
    </div>
  ),
  play: async () => {
    await window.takeSnapshot?.();
    // Visual test - settings panel should show all toggle controls for:
    // - Enabled (master toggles)
    // - Order Placement (child toggles)
    // - Error Log (child toggles)
    // - Renewal (child toggles)
  },
};

export const InfiniteScrollTest: Story = {
  parameters: {
    urql: {
      executeQuery: (req) => {
        const queryBody = req.query?.loc?.source?.body || '';

        // Mock notifications query with pagination
        if (queryBody.includes('getNotifications')) {
          const { after } = req.variables || {};

          console.log('🔄 Storybook: Loading notifications with cursor:', after || 'initial');

          // Initial load - 10 notifications (items 1-10)
          if (!after || after === '') {
            const mockData = {
              data: {
                getNotifications: {
                  totalCount: 25,
                  edges: createMockNotifications(10, 0),
                  pageInfo: {
                    endCursor: 'cursor-10',
                    hasNextPage: true,
                  },
                },
              },
            };
            console.log('📦 Storybook: Returning initial 10 notifications (1-10)');
            return fromValue(mockData);
          }

          // Second load - 10 more notifications (items 11-20)
          if (after === 'cursor-10') {
            const mockData = {
              data: {
                getNotifications: {
                  totalCount: 25,
                  edges: createMockNotifications(10, 10),
                  pageInfo: {
                    endCursor: 'cursor-20',
                    hasNextPage: true,
                  },
                },
              },
            };
            console.log('📦 Storybook: Returning second batch of 10 notifications (11-20)');
            return fromValue(mockData);
          }

          // Third load - 5 final notifications (items 21-25)
          if (after === 'cursor-20') {
            const mockData = {
              data: {
                getNotifications: {
                  totalCount: 25,
                  edges: createMockNotifications(5, 20),
                  pageInfo: {
                    endCursor: 'cursor-25',
                    hasNextPage: false, // No more items
                  },
                },
              },
            };
            console.log('📦 Storybook: Returning final 5 notifications (21-25) - END');
            return fromValue(mockData);
          }

          // Fallback for any other cursor
          console.warn('⚠️ Storybook: Unexpected cursor value:', after);
          return fromValue({
            data: {
              getNotifications: {
                totalCount: 25,
                edges: [],
                pageInfo: {
                  endCursor: null,
                  hasNextPage: false,
                },
              },
            },
          });
        }

        // Mock user data queries
        if (queryBody.includes('me {')) {
          return fromValue({
            data: { me: createMockUserData() },
          });
        }

        return fromValue({ data: {} });
      },
      executeMutation: (req) => {
        const queryBody = req.query?.loc?.source?.body || '';
        if (queryBody.includes('setUserNotificationPreference')) {
          return fromValue({
            data: {
              setUserNotificationPreference: req.variables?.notificationPreference || {},
            },
          });
        }
        return fromValue({ data: {} });
      },
      executeSubscription: () => fromValue({ data: {} }),
    },
  },
  render: (args) => (
    <div className="flex h-screen items-center justify-center bg-background-8">
      <NotificationPopup {...args}>
        <button
          className="rounded px-6 py-3 text-text-0"
          style={{ backgroundColor: '#8b5cf6', color: 'white' }}
        >
          🔄 Infinite Scroll Test (25 Items, 3 Loads)
        </button>
      </NotificationPopup>
    </div>
  ),
  play: async () => {
    await window.takeSnapshot?.();
    // Comprehensive infinite scroll test:
    // 1. Initial load: 10 notifications (1-10) with loading indicator
    // 2. Scroll down triggers second load: 10 more notifications (11-20)
    // 3. Scroll down triggers third load: 5 final notifications (21-25)
    // 4. No more loading after final batch (hasNextPage: false)
    // 5. Custom scrollbar color (var(--text-3)) visible throughout
  },
};

export const InfiniteScrollWithLoadingDelay: Story = {
  parameters: {
    urql: {
      executeQuery: (req) => {
        const queryBody = req.query?.loc?.source?.body || '';

        // Mock notifications query with realistic loading delays and proper loading states
        if (queryBody.includes('getNotifications')) {
          const { after } = req.variables || {};

          console.log('⏳ Storybook: Starting query with 2s delay, cursor:', after || 'initial');

          // Create mock data based on cursor
          let mockData;

          // Initial load - 10 notifications (items 1-10)
          if (!after || after === '') {
            mockData = {
              data: {
                getNotifications: {
                  totalCount: 25,
                  edges: createMockNotifications(10, 0),
                  pageInfo: {
                    endCursor: 'cursor-10',
                    hasNextPage: true,
                  },
                },
              },
            };
            console.log('📦 Storybook: Will return initial 10 notifications (1-10) after delay');
          }
          // Second load - 10 more notifications (items 11-20)
          else if (after === 'cursor-10') {
            mockData = {
              data: {
                getNotifications: {
                  totalCount: 25,
                  edges: createMockNotifications(10, 10),
                  pageInfo: {
                    endCursor: 'cursor-20',
                    hasNextPage: true,
                  },
                },
              },
            };
            console.log(
              '📦 Storybook: Will return second batch of 10 notifications (11-20) after delay',
            );
          }
          // Third load - 5 final notifications (items 21-25)
          else if (after === 'cursor-20') {
            mockData = {
              data: {
                getNotifications: {
                  totalCount: 25,
                  edges: createMockNotifications(5, 20),
                  pageInfo: {
                    endCursor: 'cursor-25',
                    hasNextPage: false, // No more items
                  },
                },
              },
            };
            console.log(
              '📦 Storybook: Will return final 5 notifications (21-25) after delay - COMPLETE',
            );
          }
          // Fallback
          else {
            console.warn('⚠️ Storybook: Unexpected cursor value:', after);
            mockData = {
              data: {
                getNotifications: {
                  totalCount: 25,
                  edges: [],
                  pageInfo: { endCursor: null, hasNextPage: false },
                },
              },
            };
          }

          // ✅ CORRECT: Return stream with delay using wonka operators
          // This maintains the wonka stream chain that URQL expects
          return pipe(fromValue(mockData), delay(2000));
        }

        // Mock user data queries
        if (queryBody.includes('me {')) {
          return fromValue({
            data: { me: createMockUserData() },
          });
        }

        return fromValue({ data: {} });
      },
      executeMutation: (req) => {
        const queryBody = req.query?.loc?.source?.body || '';
        if (queryBody.includes('setUserNotificationPreference')) {
          return fromValue({
            data: {
              setUserNotificationPreference: req.variables?.notificationPreference || {},
            },
          });
        }
        return fromValue({ data: {} });
      },
      executeSubscription: () => fromValue({ data: {} }),
    },
  },
  render: (args) => (
    <div className="flex h-screen items-center justify-center bg-background-8">
      <NotificationPopup {...args}>
        <button
          className="rounded px-6 py-3 text-text-0"
          style={{ backgroundColor: '#f59e0b', color: 'white' }}
        >
          ⏳ Loading States Demo (2s delays)
        </button>
      </NotificationPopup>
    </div>
  ),
  play: async () => {
    await window.takeSnapshot?.();
    // Loading states test with realistic delays:
    // 1. Initial load shows loading indicator for 2s, then 10 notifications
    // 2. Scroll down shows "Loading..." for 2s, then 10 more notifications
    // 3. Scroll down shows "Loading..." for 2s, then 5 final notifications
    // 4. No more loading after final batch
    // 5. Custom scrollbar visible throughout with proper color
  },
};

export const InfiniteScrollOptimalDelay: Story = {
  parameters: {
    urql: {
      executeQuery: (req) => {
        const queryBody = req.query?.loc?.source?.body || '';

        // Mock notifications query with optimal loading delays (800ms)
        if (queryBody.includes('getNotifications')) {
          const { after } = req.variables || {};

          console.log('🚀 Storybook: Query started, cursor:', after || 'initial');

          // Create mock data based on cursor
          let mockData;

          // Initial load - 10 notifications (items 1-10)
          if (!after || after === '') {
            mockData = {
              data: {
                getNotifications: {
                  totalCount: 25,
                  edges: createMockNotifications(10, 0),
                  pageInfo: {
                    endCursor: 'cursor-10',
                    hasNextPage: true,
                  },
                },
              },
            };
            console.log('📦 Storybook: Returning initial 10 notifications (1-10) after 800ms');
          }
          // Second load - 10 more notifications (items 11-20)
          else if (after === 'cursor-10') {
            mockData = {
              data: {
                getNotifications: {
                  totalCount: 25,
                  edges: createMockNotifications(10, 10),
                  pageInfo: {
                    endCursor: 'cursor-20',
                    hasNextPage: true,
                  },
                },
              },
            };
            console.log(
              '📦 Storybook: Returning second batch of 10 notifications (11-20) after 800ms',
            );
          }
          // Third load - 5 final notifications (items 21-25)
          else if (after === 'cursor-20') {
            mockData = {
              data: {
                getNotifications: {
                  totalCount: 25,
                  edges: createMockNotifications(5, 20),
                  pageInfo: {
                    endCursor: 'cursor-25',
                    hasNextPage: false, // No more items
                  },
                },
              },
            };
            console.log(
              '📦 Storybook: Returning final 5 notifications (21-25) after 800ms - COMPLETE',
            );
          }
          // Fallback
          else {
            console.warn('⚠️ Storybook: Unexpected cursor value:', after);
            mockData = {
              data: {
                getNotifications: {
                  totalCount: 25,
                  edges: [],
                  pageInfo: { endCursor: null, hasNextPage: false },
                },
              },
            };
          }

          // ✅ OPTIMAL: 800ms delay - long enough to see loading states, short enough to be responsive
          return pipe(fromValue(mockData), delay(800));
        }

        // Mock user data queries
        if (queryBody.includes('me {')) {
          return fromValue({
            data: { me: createMockUserData() },
          });
        }

        return fromValue({ data: {} });
      },
      executeMutation: (req) => {
        const queryBody = req.query?.loc?.source?.body || '';
        if (queryBody.includes('setUserNotificationPreference')) {
          return fromValue({
            data: {
              setUserNotificationPreference: req.variables?.notificationPreference || {},
            },
          });
        }
        return fromValue({ data: {} });
      },
      executeSubscription: () => fromValue({ data: {} }),
    },
  },
  render: (args) => (
    <div className="flex h-screen items-center justify-center bg-background-8">
      <NotificationPopup {...args}>
        <button
          className="rounded px-6 py-3 text-text-0"
          style={{ backgroundColor: '#10b981', color: 'white' }}
        >
          ⚡ Optimal Loading Demo (800ms delays)
        </button>
      </NotificationPopup>
    </div>
  ),
  play: async () => {
    await window.takeSnapshot?.();
    // Optimal loading states test:
    // 1. Initial load shows loading indicator for 800ms, then 10 notifications
    // 2. Scroll down shows "Loading..." for 800ms, then 10 more notifications
    // 3. Scroll down shows "Loading..." for 800ms, then 5 final notifications
    // 4. Loading states are clearly visible but not annoyingly long
    // 5. Custom scrollbar visible throughout with proper color
  },
};

export const EmptyState: Story = {
  parameters: {
    urql: {
      executeQuery: (req) => {
        const queryBody = req.query?.loc?.source?.body || '';

        // Mock notifications query - empty
        if (queryBody.includes('getNotifications')) {
          return fromValue({
            data: {
              getNotifications: {
                totalCount: 0,
                edges: [],
                pageInfo: {
                  endCursor: null,
                  hasNextPage: false,
                },
              },
            },
          });
        }

        // Mock user data queries
        if (queryBody.includes('me {')) {
          return fromValue({
            data: { me: createMockUserData() },
          });
        }

        return fromValue({ data: {} });
      },
      executeMutation: () => fromValue({ data: {} }),
      executeSubscription: () => fromValue({ data: {} }),
    },
  },
  render: (args) => (
    <div className="flex h-screen items-center justify-center bg-background-8">
      <NotificationPopup {...args}>
        <button className="rounded px-6 py-3 text-text-0" style={{ backgroundColor: '#6b7280' }}>
          📭 Empty Notifications
        </button>
      </NotificationPopup>
    </div>
  ),
  play: async () => {
    await window.takeSnapshot?.();
    // Visual test - empty state should be displayed
  },
};
