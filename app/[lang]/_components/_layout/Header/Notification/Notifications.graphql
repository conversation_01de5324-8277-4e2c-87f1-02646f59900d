query Notifications($first: Int = 10, $after: String = "") {
  getNotifications(first: $first, after: $after) {
    totalCount
    edges {
      cursor
      node {
        time
        type
        message
        unseen
        task {
          id
          type
          strategy {
            id
            name
            owner {
              id
            }
            battlefieldStrategy {
              id
            }
          }
        }
      }
    }
    pageInfo {
      endCursor
      hasNextPage
    }
  }
}
