import { BrokerType } from '__generated__/types.app';

export enum NotificationChannelType {
  TELEGRAM_BOT = 'TELEGRAM_BOT',
  EMAIL = 'EMAIL',
  WEB_APP = 'WEB_APP',
}

export enum NotificationTypeKey {
  SIMULATE_ORDER = 'simulate.order',
  REAL_ORDER = 'real.order',
  SIMULATE_LOG_ERROR = 'simulate.log-error',
  REAL_LOG_ERROR = 'real.log-error',
  ENABLED_CHANNELS = 'enabledChannels',
  WALLET_DEPOSIT = 'wallet.deposit',
  WALLET_WITHDRAW = 'wallet.withdraw',
}

export const ALL_NOTIFY_CHANNEL = [
  NotificationChannelType.TELEGRAM_BOT,
  NotificationChannelType.EMAIL,
  NotificationChannelType.WEB_APP,
];

export const DEFAULT_NOTIFICATION_SETTING = {
  [NotificationTypeKey.ENABLED_CHANNELS]: [
    NotificationChannelType.EMAIL,
    NotificationChannelType.WEB_APP,
  ],
  [NotificationTypeKey.REAL_ORDER]: [
    NotificationChannelType.TELEGRAM_BOT,
    NotificationChannelType.WEB_APP,
  ],
  [NotificationTypeKey.SIMULATE_LOG_ERROR]: [
    NotificationChannelType.TELEGRAM_BOT,
    NotificationChannelType.WEB_APP,
  ],
  [NotificationTypeKey.REAL_LOG_ERROR]: [
    NotificationChannelType.TELEGRAM_BOT,
    NotificationChannelType.WEB_APP,
  ],
  [NotificationTypeKey.SIMULATE_ORDER]: [
    NotificationChannelType.TELEGRAM_BOT,
    NotificationChannelType.WEB_APP,
  ],
  [NotificationTypeKey.WALLET_DEPOSIT]: [
    NotificationChannelType.TELEGRAM_BOT,
    NotificationChannelType.EMAIL,
    NotificationChannelType.WEB_APP,
  ],
  [NotificationTypeKey.WALLET_WITHDRAW]: [
    NotificationChannelType.TELEGRAM_BOT,
    NotificationChannelType.EMAIL,
    NotificationChannelType.WEB_APP,
  ],
};

export const BrokerTaskNameMap = {
  [BrokerType.Backtest]: 'Backtest',
  [BrokerType.Simulation]: 'Simulate',
  [BrokerType.Trader]: 'Live Trading',
};
