import { tw } from '@/_services/utils/tailwind';
import { ReactElement, ReactNode } from 'react';
import { Popup } from './Popup';
import { Placement } from './placements';

type InfoPopupProp = {
  action?: string[];
  placement: Placement;
  children: ReactElement;
  content: ReactNode;
  className?: string;
  popupVisible?: boolean;
};

const placementMap: Record<Placement, string> = {
  top: tw`before:-bottom-2 before:left-1/2 before:-translate-x-1/2 before:border-x-[0.5rem] before:border-t-[0.5rem] before:border-x-transparent`,
  topRight: tw`-left-4 before:-bottom-2 before:left-4 before:border-x-[0.5rem] before:border-t-[0.5rem] before:border-x-transparent`,
  topLeft: tw`-right-4 before:-bottom-2 before:right-4 before:border-x-[0.5rem] before:border-t-[0.5rem] before:border-x-transparent`,
  bottom: tw`before:-top-2 before:left-1/2 before:-translate-x-1/2 before:border-x-[0.5rem] before:border-b-[0.5rem] before:border-x-transparent`,
  bottomRight: tw`-left-4 before:-top-2 before:left-4 before:border-x-[0.5rem] before:border-b-[0.5rem] before:border-x-transparent`,
  bottomLeft: tw`-right-4 before:-top-2 before:right-4 before:border-x-[0.5rem] before:border-b-[0.5rem] before:border-x-transparent`,
  right: tw`before:-left-2 before:top-1/2 before:-translate-y-1/2 before:border-y-[0.5rem] before:border-r-[0.5rem] before:border-y-transparent`,
  rightTop: tw`-bottom-4 before:-left-2 before:bottom-4 before:border-y-[0.5rem] before:border-r-[0.5rem] before:border-y-transparent`,
  rightBottom: tw`-top-4 before:-left-2 before:top-4 before:border-y-[0.5rem] before:border-r-[0.5rem] before:border-y-transparent`,
  left: tw`before:-right-2 before:top-1/2 before:-translate-y-1/2 before:border-y-[0.5rem] before:border-l-[0.5rem] before:border-y-transparent`,
  leftTop: tw`-bottom-4 before:-right-2 before:bottom-4 before:border-y-[0.5rem] before:border-l-[0.5rem] before:border-y-transparent`,
  leftBottom: tw`-top-4 before:-right-2 before:top-4 before:border-y-[0.5rem] before:border-l-[0.5rem] before:border-y-transparent`,
  topRightFixed: tw`before:-bottom-2 before:left-1/2 before:-translate-x-1/2 before:border-x-[0.5rem] before:border-t-[0.5rem] before:border-x-transparent`,
};

function PopupContainer({
  placement,
  children,
  className,
}: {
  placement: Placement;
  children?: ReactNode;
  className?: string;
}) {
  return (
    <div
      className={[
        tw`relative rounded bg-background-6 p-2.5 text-xs shadow-[4px_4px_8px_0px_rgba(8,9,28,0.4)] before:absolute before:block before:border-background-6 before:content-['']`,
        placementMap[placement],
        className,
      ].join(' ')}
    >
      {children}
    </div>
  );
}

export function InfoPopup(props: InfoPopupProp) {
  const { action = ['hover'], placement, children, className, content, ...rest } = props;

  return (
    <Popup
      popup={
        <PopupContainer placement={placement} className={className}>
          {content}
        </PopupContainer>
      }
      action={action}
      popupPlacement={placement}
      {...rest}
    >
      {children}
    </Popup>
  );
}
