const autoAdjustOverflow = {
  adjustX: 1,
  adjustY: 1,
};

const targetOffset = [0, 0];

export type Placement = keyof typeof placements;

export const placements = {
  left: {
    points: ['cr', 'cl'],
    overflow: autoAdjustOverflow,
    offset: [-10, 0],
    targetOffset,
  },
  right: {
    points: ['cl', 'cr'],
    overflow: autoAdjustOverflow,
    offset: [10, 0],
    targetOffset,
  },
  top: {
    points: ['bc', 'tc'],
    overflow: autoAdjustOverflow,
    offset: [0, -10],
    targetOffset,
  },
  bottom: {
    points: ['tc', 'bc'],
    overflow: autoAdjustOverflow,
    offset: [0, 10],
    targetOffset,
  },
  topRight: {
    points: ['bl', 'tl'],
    overflow: autoAdjustOverflow,
    offset: [0, -10],
    targetOffset,
  },
  leftBottom: {
    points: ['tr', 'tl'],
    overflow: autoAdjustOverflow,
    offset: [-10, 0],
    targetOffset,
  },
  topLeft: {
    points: ['br', 'tr'],
    overflow: autoAdjustOverflow,
    offset: [0, -10],
    targetOffset,
  },
  rightBottom: {
    points: ['tl', 'tr'],
    overflow: autoAdjustOverflow,
    offset: [10, 0],
    targetOffset,
  },
  bottomLeft: {
    points: ['tr', 'br'],
    overflow: autoAdjustOverflow,
    offset: [0, 10],
    targetOffset,
  },
  rightTop: {
    points: ['bl', 'br'],
    overflow: autoAdjustOverflow,
    offset: [10, 0],
    targetOffset,
  },
  bottomRight: {
    points: ['tl', 'bl'],
    overflow: autoAdjustOverflow,
    offset: [0, 10],
    targetOffset,
  },
  leftTop: {
    points: ['br', 'bl'],
    overflow: autoAdjustOverflow,
    offset: [-10, 0],
    targetOffset,
  },
  topRightFixed: {
    points: ['tr', 'tr'],
    overflow: { adjustX: 0, adjustY: 0 },
    offset: [0, 0],
    targetOffset: [0, 0],
  },
};
