import { tw } from '@/_services/utils/tailwind';
import clsx from 'clsx';
import { ReactNode, useRef } from 'react';
import { PopoverProps as AriaPopoverProps, Button, Dialog } from 'react-aria-components';
import { P, match } from 'ts-pattern';
import { Popover, PopoverTrigger } from '.';

type TooltipProp = {
  title: ReactNode;
  content: ReactNode;
  dialogAriaLabel: string;
  placement?: AriaPopoverProps['placement'];
  triggerType?: 'click' | 'hover';
  showArrow?: boolean;
  /**
   * The width or height for `triggerRef`. The arrow will point to the center of `triggerRef`
   *
   * ```
   * size = margin + arrowWidth + margin
   * ```
   *
   * - `arrowWidth` is 15px
   * - `margin` is the distance between the arrow and the placement border
   *   - If `placement` is `bottom left`, margin is the distance to the left border
   *   - If `placement` is `right top`, margin is the distance to the top border
   */
  triggerRefClassName?: `w-${string}` | `h-${string}`;
  className?: string;
  titleClassName?: string;
  arrowClassName?: string;
};

function hasTextClassName(className: string | undefined): boolean {
  return match(className)
    .with(P.string.regex(/(^|\s|:)(text-|font-)\S+($|\s)/), () => true)
    .otherwise(() => false);
}

function getTriggerRefClassName(
  placement: NonNullable<TooltipProp['placement']>,
  triggerRefClassName: TooltipProp['triggerRefClassName'],
): string {
  const defaultClassName = match(placement)
    .returnType<{ position: string; size: string }>()
    .with(P.union('bottom left', 'bottom start', 'top left', 'top start'), () => ({
      position: tw`inset-y-0 left-0`,
      size: tw`w-[55px]`,
    }))
    .with(P.union('bottom right', 'bottom end', 'top right', 'top end'), () => ({
      position: tw`inset-y-0 right-0`,
      size: tw`w-[55px]`,
    }))
    .with(P.union('left top', 'start top', 'right top', 'end top'), () => ({
      position: tw`inset-x-0 top-0`,
      size: tw`h-[55px]`,
    }))
    .with(P.union('left bottom', 'start bottom', 'right bottom', 'end bottom'), () => ({
      position: tw`inset-x-0 bottom-0`,
      size: tw`h-[55px]`,
    }))
    .with(P.union('bottom', 'top', 'left', 'start', 'right', 'end'), () => ({
      position: tw`inset-0`,
      size: '',
    }))
    .exhaustive();

  return clsx(
    defaultClassName.position,
    !triggerRefClassName ? defaultClassName.size : triggerRefClassName,
  );
}

export function Tooltip({
  title,
  content,
  dialogAriaLabel,
  placement = 'bottom',
  triggerType = 'hover',
  showArrow = true,
  triggerRefClassName,
  className,
  titleClassName,
  arrowClassName,
}: TooltipProp) {
  const triggerRef = useRef<HTMLSpanElement>(null);

  return (
    <PopoverTrigger triggerType={triggerType}>
      <Button
        className={clsx(
          titleClassName,
          !hasTextClassName(titleClassName) &&
            'text-sm font-medium text-text-popover-trigger-text-color hover:text-text-popover-trigger-text-hover-color',
          'relative underline decoration-dashed decoration-1 underline-offset-4',
        )}
      >
        <span
          ref={triggerRef}
          className={clsx(
            'invisible absolute',
            getTriggerRefClassName(placement, triggerRefClassName),
          )}
        />
        {title}
      </Button>
      <Popover
        className={clsx('p-2.5', className)}
        arrowClassName={arrowClassName}
        showArrow={showArrow}
        placement={placement}
        triggerRef={triggerRef}
      >
        <Dialog className="outline outline-0" aria-label={dialogAriaLabel}>
          {match(content)
            .with(P.union(P.string, P.number), () => (
              <p className="whitespace-pre-wrap text-xs/normal font-medium text-text-2">
                {content}
              </p>
            ))
            .otherwise(() => content)}
        </Dialog>
      </Popover>
    </PopoverTrigger>
  );
}
