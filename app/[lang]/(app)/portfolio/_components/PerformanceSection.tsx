import { Tooltip } from '@/_components/Popover';
import { RollerBar } from '@/_components/RollerBar';
import { ScreenResolutions, useBreakPoint } from '@/_hook/useBreakPoint';
import { tw } from '@/_services/utils/tailwind';
import { LiveTradeBrokerTaskQuery, useLiveTradeBrokerTaskQuery } from '__generated__/types.app';
import clsx from 'clsx';
import { TFunction } from 'next-i18next';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import EditIcon from 'static/edit-icon.svg';
import { match } from 'ts-pattern';
import { toFixed } from 'utils';
import { OpenPositionEditModal } from './OpenPositionEditModal';
import { PerformanceSectionEditModal } from './PerformanceSectionEditModal';
import { PerformanceSectionItem, PerformanceSectionItemProp } from './PerformanceSectionItem';
import { LogBody, PerformanceTableDataRow } from './section/Section.elements';

type PerformanceSectionProp = {
  taskId: string;
  isOpen: boolean;
};
const getPerformance = (
  liveTradeBrokerTaskQuery: LiveTradeBrokerTaskQuery | undefined,
  t: TFunction,
  breakpoint: ScreenResolutions,
): PerformanceSectionItemProp[] => {
  let performance: LiveTradeBrokerTaskQuery['getBroker']['performance'] | undefined;
  if (liveTradeBrokerTaskQuery) {
    performance = liveTradeBrokerTaskQuery.getBroker.performance;
  }
  return [
    {
      label: t('initial-capital'),
      isInfoPopup: false,
      value: performance?.startingCapital,
      formatter: (v) =>
        v.toLocaleString('en', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }),
      noDataString: 'N/A',
    },
    {
      label: t('final-capital'),
      isInfoPopup: false,
      value: performance?.finalCapital,
      formatter: (v) =>
        v.toLocaleString('en', { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
      noDataString: 'N/A',
      className: tw`sm:justify-self-end md:justify-self-auto`,
    },
    {
      label: t('sharpe-ratio'),
      isInfoPopup: true,
      value: performance?.sharpeRatio,
      formatter: (v) => v.toLocaleString('en', { maximumFractionDigits: 2 }),
      infoPopupStyle:
        'w-[390px] translate-x-4 sm:translate-x-0 md:translate-x-4 sm:w-[242px] md:w-[390px]',
      infoPopupText: t('sharpe-ratio-description', { joinArrays: '\n' }),
      noDataString: 'N/A',
      arrowPlacement: breakpoint === 'sm' ? 'bottom start' : 'bottom end',
    },
    {
      label: t('win-rate'),
      isInfoPopup: true,
      value: performance?.winRate,
      formatter: (v) => toFixed(Number(v) * 100, 2) + '%',
      infoPopupStyle: tw`w-[372px] sm:w-[242px] sm:translate-x-4 md:w-[372px] md:translate-x-0`,
      infoPopupText: t('win-rate-description'),
      arrowPlacement: breakpoint === 'sm' ? 'top end' : 'bottom start',
      popRefStyle: 'w-[55px] sm:w-4 md:w-[55px]',
      noDataString: 'N/A',
      className: tw`sm:justify-self-end md:justify-self-auto`,
    },
    {
      label: t('roi'),
      isInfoPopup: false,
      value: performance?.roi,
      formatter: (value) => `${value >= 0 ? '+' : ''}${toFixed(value * 100, 2)}%`,
      isValueStyleCondition: true,
      noDataString: 'N/A',
    },
    {
      label: t('MDD'),
      isInfoPopup: true,
      value: performance?.mddPercentage,
      formatter: (v) => toFixed(Number(v) * 100, 2) + '%',
      infoPopupStyle: tw`w-[353px] translate-x-1.5 sm:w-[242px] md:w-[353px]`,
      infoPopupText: t(breakpoint === 'sm' ? 'mdd-description-short' : 'mdd-description', {
        joinArrays: '\n',
      }),
      arrowPlacement: breakpoint === 'sm' ? 'top end' : 'bottom end',
      popRefStyle: 'w-full sm:-me-6 md:me-0',
      noDataString: 'N/A',
      className: tw`order-none sm:order-2 sm:justify-self-end md:order-none md:justify-self-auto`,
    },
    {
      label: t('realized-roi'),
      isInfoPopup: false,
      value: performance?.roiRealized,
      formatter: (value) => `${value >= 0 ? '+' : ''}${toFixed(value * 100, 2)}%`,
      isValueStyleCondition: true,
      noDataString: '0%',
      className: tw`sm:justify-self-end md:justify-self-auto`,
    },
    {
      label: t('unrealized-roi'),
      isInfoPopup: true,
      value: performance?.roiUnrealized,
      formatter: (v) => `${v >= 0 ? '+' : ''}${toFixed(v * 100, 2)}%`,
      infoPopupStyle: tw`w-[390px] sm:w-[242px] sm:translate-x-4 md:w-[390px] md:translate-x-0`,
      infoPopupText: t('Tooltip-Detail-Unrealized ROI'),
      isValueStyleCondition: true,
      arrowPlacement: breakpoint === 'sm' ? 'top end' : 'top',
      popRefStyle: 'w-24 sm:w-[18px] md:w-24',
      noDataString: '0%',
      className: tw`order-none sm:order-1 sm:justify-self-end md:order-none md:justify-self-auto`,
    },
    {
      label: t('buy-and-hold-equity'),
      isInfoPopup: false,
      value: performance?.buyAndHoldEquity,
      formatter: (v) => toFixed(Number(v) * 100, 0) + '%',
      noDataString: '0%',
      className: 'order-none sm:order-1 md:order-none',
    },
    {
      label: t('realized-gain'),
      isInfoPopup: false,
      value:
        performance?.startingCapital !== undefined &&
        performance?.roiRealized !== undefined &&
        performance?.roiRealized !== null
          ? performance.startingCapital * performance.roiRealized
          : undefined,
      formatter: (v) =>
        v.toLocaleString('en', { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
      isValueStyleCondition: true,
      noDataString: 'N/A',
    },
    {
      label: t('profit-factor'),
      isInfoPopup: true,
      value: performance?.profitFactor,
      formatter: (v) => String(toFixed(Number(v), 2)),
      infoPopupStyle: 'w-[358px] sm:w-[242px] md:w-[358px]',
      infoPopupText: t('pf-description', { joinArrays: '\n' }),
      arrowPlacement: breakpoint === 'sm' ? 'top start' : 'bottom',
      noDataString: 'N/A',
      className: 'order-none sm:order-2 md:order-none',
    },
    {
      label: t('odds-ratio'),
      isInfoPopup: true,
      value: performance?.oddsRatio,
      formatter: (v) => String(toFixed(Number(v), 2)),
      infoPopupStyle: 'w-[370px] sm:w-[242px] md:w-[370px]',
      infoPopupText: t('odds-ratio-description', {
        joinArrays: '\n',
        context: breakpoint === 'sm' ? 'short' : undefined,
      }),
      arrowPlacement: breakpoint === 'sm' ? 'top start' : 'bottom end',
      noDataString: 'N/A',
      className: 'order-none sm:order-3 md:order-none',
    },
    {
      label: t('Return DD'),
      isInfoPopup: true,
      value: performance?.returnDivDd,
      formatter: (v) => String(toFixed(Number(v), 2)),
      infoPopupStyle: 'w-[173px] sm:translate-x-5 md:translate-x-0',
      infoPopupText: t('Tooltip-Detail-Return DD'),
      arrowPlacement: breakpoint === 'sm' ? 'top end' : 'top start',
      popRefStyle: 'w-[55px] sm:w-1 md:w-[55px]',
      noDataString: 'N/A',
      className: tw`order-none sm:order-2 sm:justify-self-end md:order-none md:justify-self-auto`,
    },
    {
      label: t('num-of-trades'),
      isInfoPopup: false,
      value: performance?.tradeCount,
      formatter: (v) => v.toLocaleString('en'),
      noDataString: 'N/A',
      className: tw`order-none sm:order-3 sm:justify-self-end md:order-none md:justify-self-auto`,
    },
    {
      label: t('LTTMNH'),
      isInfoPopup: true,
      value: performance?.longestTimeToMakeNewHigh,
      formatter: (v) => toFixed(Number(v), 2) + ' hrs',
      infoPopupStyle: 'w-[241px]',
      infoPopupText: t('lttmnh-stands-for-longest-time-to-make-new-high'),
      arrowPlacement: breakpoint === 'sm' ? 'top start' : 'top end',
      noDataString: 'N/A',
      className: 'order-none sm:order-3 md:order-none',
    },
  ];
};
export function PerformanceSection({ isOpen, taskId }: PerformanceSectionProp) {
  const { t } = useTranslation();
  const breakpoint = useBreakPoint();
  const [showModal, setShowModal] = useState(false);
  const [showOpenPositionModal, setShowOpenPositionModal] = useState(false);
  const [{ data, fetching, error }, reexecuteQuery] = useLiveTradeBrokerTaskQuery({
    variables: { taskId },
    pause: !taskId,
  });
  const result = data?.getBroker;
  const [isManualMode, setIsManualMode] = useState<boolean>(() => {
    const suggestedStopPoints = result?.strategy?.battlefieldStrategy?.suggestedStopPoints || 0;
    const suggestedTakeProfit = result?.strategy?.battlefieldStrategy?.suggestedTakeProfit || 0;
    const currentStopLossRatio = result?.stopLossRatio || 0;
    const currentLockGainRatio = result?.lockGainRatio || 0;

    return (
      currentStopLossRatio !== suggestedStopPoints * 100 ||
      currentLockGainRatio !== suggestedTakeProfit * 100
    );
  });

  useEffect(() => {
    const suggestedStopPoints = result?.strategy?.battlefieldStrategy?.suggestedStopPoints || 0;
    const suggestedTakeProfit = result?.strategy?.battlefieldStrategy?.suggestedTakeProfit || 0;
    const currentStopLossRatio = result?.stopLossRatio || 0;
    const currentLockGainRatio = result?.lockGainRatio || 0;

    const updatedIsManualMode =
      currentStopLossRatio !== suggestedStopPoints * 100 ||
      currentLockGainRatio !== suggestedTakeProfit * 100;

    if (isManualMode !== updatedIsManualMode) {
      setIsManualMode(updatedIsManualMode);
    }
  }, [
    result?.stopLossRatio,
    result?.lockGainRatio,
    result?.strategy?.battlefieldStrategy?.suggestedStopPoints,
    result?.strategy?.battlefieldStrategy?.suggestedTakeProfit,
    isManualMode,
  ]);

  if (error)
    return (
      <LogBody isOpen={isOpen}>
        <></>
      </LogBody>
    );
  const isEditable = taskId && Object.keys(result || {}).length > 0;
  const refetchBrokerTask = () => {
    reexecuteQuery({ requestPolicy: 'network-only' });
  };

  if (fetching) return <p>Loading...</p>;
  const strategyInvestmentTypeLabel = match(result?.strategyInvestmentType)
    .with('USD_M_FUTURES', () => 'USDⓈ-M Futures')
    .with('COIN_M_FUTURES', () => 'COIN-M Futures')
    .with('SPOT', () => 'Spot')
    .otherwise(() => 'N/A');
  return (
    <LogBody isOpen={isOpen}>
      <PerformanceSectionEditModal
        taskId={taskId}
        brokerTaskQuery={data}
        show={showModal}
        setShow={setShowModal}
        refetchBrokerTask={refetchBrokerTask}
        isManual={isManualMode}
      />
      <OpenPositionEditModal
        urlExchange='"ca"'
        show={showOpenPositionModal}
        setShow={setShowOpenPositionModal}
        taskId={taskId}
        autoClosePosition={result?.autoClosePosition === false ? 'keep' : 'sell'}
        refetchBrokerTask={refetchBrokerTask}
      />

      <RollerBar type="modal" height="0" className="grow sm:pe-3 md:pe-0">
        <div
          className={clsx(
            'grid grid-cols-3 whitespace-nowrap sm:grid-cols-2 md:grid-cols-3',
            'gap-x-8 gap-y-4 sm:gap-x-7 sm:gap-y-3 md:gap-x-8 md:gap-y-4',
            '*:min-w-[106px] sm:even:*:justify-self-end md:even:*:justify-self-start',
            'mb-5 px-5 py-3 sm:px-7 sm:py-2 md:px-5 md:py-3',
            'text-sm/4 font-semibold sm:text-xs md:text-sm/4',
            'rounded border border-text-5',
          )}
        >
          <div>
            <div className="mb-1 flex flex-row items-center gap-2">
              <p className="text-text-5">{t('stop-loss')}</p>
              <a
                onClick={() => isEditable && setShowModal(true)}
                className={isEditable ? 'cursor-pointer' : 'cursor-not-allowed'}
              >
                <EditIcon
                  className={`size-4 stroke-text-6 ${isEditable && 'hover:stroke-text-2'}`}
                />
              </a>
            </div>
            <p className={result?.stopLossRatio ? 'text-negative' : 'text-text-3'}>
              {result?.stopLossRatio ? '-' + toFixed(result.stopLossRatio, 2) + ' %' : 'N/A'}
            </p>
          </div>
          <div>
            <div className="mb-1 flex flex-row items-center gap-2">
              <p className="text-text-5">{t('take-profit')}</p>
              <a
                onClick={() => isEditable && setShowModal(true)}
                className={isEditable ? 'cursor-pointer' : 'cursor-not-allowed'}
              >
                <EditIcon
                  className={`size-4 stroke-text-6 ${isEditable && 'hover:stroke-text-2'}`}
                />
              </a>
            </div>
            <p className={result?.lockGainRatio ? 'text-positive' : 'text-text-3'}>
              {result?.lockGainRatio ? '+' + toFixed(result?.lockGainRatio, 2) + ' %' : 'N/A'}
            </p>
          </div>
          <div className="sm:order-2 md:order-none">
            <p className="mb-1 text-text-5">{t('leverage')}</p>
            <p className={result?.lockGainRatio ? 'text-text-2' : 'text-text-3'}>
              {result?.leverage ?? 'N/A'}
            </p>
          </div>
          <div className="sm:order-1 md:order-none">
            <p className="mb-1.5 text-text-5 sm:mb-1 md:mb-1.5">{t('type')}</p>
            <p className={strategyInvestmentTypeLabel === 'N/A' ? 'text-text-3' : 'text-text-2'}>
              {strategyInvestmentTypeLabel}
            </p>
          </div>
          <div className="col-span-2 sm:col-span-1 md:col-span-2">
            <div className="mb-1.5 flex flex-row items-center gap-2 sm:mb-1 md:mb-1.5">
              <Tooltip
                title={t('open-position')}
                content={'How to deal with your open positions when the strategy is stopped.'}
                dialogAriaLabel={t('open-position')}
                className="w-[173px]"
                titleClassName={tw`text-text-5 hover:text-text-1`}
              />
              <a
                onClick={() => isEditable && setShowOpenPositionModal(true)}
                className={isEditable ? 'cursor-pointer' : 'cursor-not-allowed'}
              >
                <EditIcon
                  className={`size-4 stroke-text-6 ${isEditable && 'hover:stroke-text-2'}`}
                />
              </a>
            </div>
            <p
              className={
                result?.autoClosePosition === null || result?.autoClosePosition === undefined
                  ? 'text-text-3'
                  : 'text-text-2'
              }
            >
              {result?.autoClosePosition === null || result?.autoClosePosition === undefined
                ? 'N/A'
                : result?.autoClosePosition === false
                  ? t('keep-on-my-own')
                  : breakpoint === 'sm'
                    ? t('Sell with mrkt price')
                    : t('Sell with market price')}
            </p>
          </div>
        </div>
        <PerformanceTableDataRow>
          {getPerformance(data, t, breakpoint).map((item) => (
            <PerformanceSectionItem
              key={item.label}
              label={item.label}
              value={item.value}
              isInfoPopup={item.isInfoPopup}
              infoPopupStyle={item.infoPopupStyle}
              infoPopupText={item.infoPopupText}
              formatter={item.formatter}
              isValueStyleCondition={item.isValueStyleCondition}
              arrowPlacement={item.arrowPlacement}
              popRefStyle={item.popRefStyle}
              noDataString={item.noDataString}
              className={item.className}
            />
          ))}
        </PerformanceTableDataRow>
      </RollerBar>
    </LogBody>
  );
}
