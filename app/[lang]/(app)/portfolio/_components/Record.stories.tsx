import { RecordRestart as LaunchLiveTradeStoryModal } from '@/(app)/marketplace/_components/LaunchLiveTradeModal/LaunchLiveTradeModal.stories';
import type { Meta, StoryObj } from '@storybook/react';
import { fn, within } from '@storybook/test';
import {
  BrokerChargingMethod,
  BrokerStatus,
  DeleteBrokerTaskDocument,
  DeleteBrokerTaskMutation,
  GetMyTraderTasksDocument,
  GetMyTraderTasksQuery,
  GetMyTraderTasksQueryVariables,
  StrategyInvestmentTrend,
  StrategyInvestmentType,
  SupportedExchange,
} from '__generated__/types.app';
import { fromValue } from 'wonka';
import { Record } from './Record';

const meta: Meta<typeof Record> = {
  title: 'Portfolio/Record',
  component: Record,
  args: {},
  argTypes: {},
  parameters: {
    urql: {
      executeQuery: (req) => {},
      executeMutation: (req) => {
        if (req.query === DeleteBrokerTaskDocument) {
          return fromValue<{ data: DeleteBrokerTaskMutation }>({
            data: {
              deleteBrokerTask: 'OK',
            },
          });
        }
      },
      executeSubscription: (req) => {},
    },
  },
};

export default meta;
type Story = StoryObj<typeof Record>;

// mock data creation
const mockTasks = (
  isStopped: boolean,
): GetMyTraderTasksQuery['me']['traderTasksCursor']['edges'] => {
  const statusFilter = isStopped ? BrokerStatus.Stopped : BrokerStatus.Running;
  return Array(50)
    .fill(0)
    .map((_, index) => ({
      cursor: `cursor-${index}`,
      node: {
        id: `7599${index}`,
        createdAt: '2021-05-30 00:00',
        updatedAt: '2021-05-30 00:00',
        rangeEnd: '2021-05-30 00:00',
        strategyInvestmentType: StrategyInvestmentType.Spot,
        strategyInvestmentTrend: StrategyInvestmentTrend.Long,
        roi: index < 20 ? 0.1312 : -0.1122,
        status: index < 20 ? BrokerStatus.Running : BrokerStatus.Stopped,
        strategyId: `strategy-${index}`,
        strategy: {
          id: `7599${index}`,
          owner: {
            id: `12345`,
            avatarUrl:
              'https://ca-user-avatars.s3.ap-northeast-1.amazonaws.com/f89c0bdf-c5b3-4c36-8537-2e3bdd23ec81',
          },
          name: '1h_EMA144_MOM10_MACross in PY copied (958)',
          investmentType: StrategyInvestmentType.Spot,
          investmentTrend: StrategyInvestmentTrend.Long,
          templateName: 'Cloud Strategy',
          templateSerialNumber: 2,
        },
        targetExchangePair: { okex: { pairs: ['BTC/EXUSD:EXUSD'] } },
        lockGainRatio: 50,
        stopLossRatio: 20,
        baseExchange: SupportedExchange.Okex,
        autoClosePosition: true,
        exchangeApiKeys: [
          {
            comment: 'apple#1',
            apiKey: '',
          },
        ],
        chargingMethod: BrokerChargingMethod.MonthlySubscription,
      },
    }))
    .filter((edge) => edge.node.status === statusFilter);
};

// create pagination responses
const createPaginatedResponse = (
  tasks: GetMyTraderTasksQuery['me']['traderTasksCursor']['edges'],
  first: GetMyTraderTasksQueryVariables['first'],
  after: GetMyTraderTasksQueryVariables['after'],
  userId = '12345',
) => {
  const edges = [];
  let isPushing = false;
  const limit = first ?? 10;

  for (const mockTask of tasks) {
    if (!isPushing && !after && mockTask.cursor != after) {
      isPushing = true;
      edges.push(mockTask);
      continue;
    }
    if (!isPushing && after && mockTask.cursor == after) {
      isPushing = true;
      continue;
    }
    if (!isPushing) {
      continue;
    }
    if (edges.length >= Number(limit)) {
      break;
    }
    edges.push(mockTask);
  }

  return {
    data: {
      me: {
        id: userId,
        traderTasksCursor: {
          edges: edges,
          pageInfo: {
            endCursor: edges[edges.length - 1]?.cursor ?? null,
            hasNextPage: edges[edges.length - 1]?.cursor != tasks[tasks.length - 1]?.cursor,
          },
        },
      },
    },
  };
};

export const LiveTrading: Story = {
  tags: ['skip-test'],
  args: { isTabShown: false, isStopped: false, setSelectedTask: fn() },
  parameters: {
    urql: {
      executeQuery: (req) => {
        if (req.query === GetMyTraderTasksDocument) {
          const { first, after } = req.variables as GetMyTraderTasksQueryVariables;
          return fromValue(createPaginatedResponse(mockTasks(false), first, after));
        }
        const res = LaunchLiveTradeStoryModal.parameters?.urql?.executeQuery?.(req);
        if (res) return res;
      },
    },
  },
  play: async ({ args, canvasElement }) => {
    await window.takeSnapshot?.();

    const canvas = within(canvasElement.querySelector('#app') as HTMLElement);
    // https://storybook.js.org/docs/writing-tests/interaction-testing
  },
};

export const Stopped: Story = {
  tags: ['skip-test'],
  args: { isTabShown: false, isStopped: true, setSelectedTask: fn() },
  parameters: {
    urql: {
      executeQuery: (req) => {
        if (req.query === GetMyTraderTasksDocument) {
          const { first, after } = req.variables as GetMyTraderTasksQueryVariables;
          return fromValue(createPaginatedResponse(mockTasks(true), first, after));
        }
        const res = LaunchLiveTradeStoryModal.parameters?.urql?.executeQuery?.(req);
        if (res) return res;
      },
    },
  },
  play: async ({ args, canvasElement }) => {
    await window.takeSnapshot?.();

    const canvas = within(canvasElement.querySelector('#app') as HTMLElement);
  },
};

export const OtherUserStrategy: Story = {
  tags: ['skip-test'],
  args: { isTabShown: false, isStopped: true, setSelectedTask: fn() },
  parameters: {
    urql: {
      executeQuery: (req) => {
        if (req.query === GetMyTraderTasksDocument) {
          const { first, after } = req.variables as GetMyTraderTasksQueryVariables;
          return fromValue(createPaginatedResponse(mockTasks(true), first, after, '56789'));
        }
        const res = LaunchLiveTradeStoryModal.parameters?.urql?.executeQuery?.(req);
        if (res) return res;
      },
    },
  },
  play: async ({ args, canvasElement }) => {
    await window.takeSnapshot?.();

    const canvas = within(canvasElement.querySelector('#app') as HTMLElement);
  },
};

export const Fold: Story = {
  tags: ['skip-test'],
  args: { isTabShown: false, setSelectedTask: fn() },
  parameters: {
    urql: {
      executeQuery: (req) => {
        if (req.query === GetMyTraderTasksDocument) {
          const { first, after } = req.variables as GetMyTraderTasksQueryVariables;
          return fromValue(createPaginatedResponse(mockTasks(false), first, after));
        }
        const res = LaunchLiveTradeStoryModal.parameters?.urql?.executeQuery?.(req);
        if (res) return res;
      },
    },
  },
  play: async ({ args, canvasElement }) => {
    await window.takeSnapshot?.();

    const canvas = within(canvasElement.querySelector('#app') as HTMLElement);
  },
};

export const Unfold: Story = {
  tags: ['skip-test'],
  args: { isTabShown: true, setSelectedTask: fn() },
  parameters: {
    urql: {
      executeQuery: (req) => {
        if (req.query === GetMyTraderTasksDocument) {
          const { first, after } = req.variables as GetMyTraderTasksQueryVariables;
          return fromValue(createPaginatedResponse(mockTasks(false), first, after));
        }
        const res = LaunchLiveTradeStoryModal.parameters?.urql?.executeQuery?.(req);
        if (res) return res;
      },
    },
  },
  play: async ({ args, canvasElement }) => {
    await window.takeSnapshot?.();

    const canvas = within(canvasElement.querySelector('#app') as HTMLElement);
  },
};

export const NoData: Story = {
  tags: ['skip-test'],
  args: { isTabShown: true, setSelectedTask: fn() },
  parameters: {
    urql: {
      executeQuery: (req) => {
        if (req.query === GetMyTraderTasksDocument) {
          return fromValue({
            data: {
              me: {
                id: '12345',
                traderTasksCursor: {
                  edges: [],
                  pageInfo: {
                    endCursor: null,
                    hasNextPage: false,
                  },
                },
              },
            },
          });
        }
        const res = LaunchLiveTradeStoryModal.parameters?.urql?.executeQuery?.(req);
        if (res) return res;
      },
    },
  },
  play: async ({ args, canvasElement }) => {
    await window.takeSnapshot?.();

    const canvas = within(canvasElement.querySelector('#app') as HTMLElement);
  },
};
