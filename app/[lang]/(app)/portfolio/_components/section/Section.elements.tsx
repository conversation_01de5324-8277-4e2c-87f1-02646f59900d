export const ActivityPlaceholderLoader = (): JSX.Element => (
  <>
    <svg
      role="img"
      width="100%"
      height="800px"
      aria-labelledby="loading-aria"
      viewBox="0 0 100% 200"
      preserveAspectRatio="none"
    >
      <title id="loading-aria">Loading...</title>
      <rect
        x="0"
        y="0"
        width="100%"
        height="100%"
        clipPath="url(#clip-path)"
        style={{
          fill: 'url("#fill")',
        }}
      ></rect>
      <defs>
        <clipPath id="clip-path">
          <rect x="3" y="10" rx="5" ry="5" width="95%" height="30" />
          <rect x="3" y="50" rx="5" ry="5" width="95%" height="30" />
          <rect x="3" y="90" rx="5" ry="5" width="95%" height="30" />
          <rect x="3" y="130" rx="5" ry="5" width="95%" height="30" />
          <rect x="3" y="170" rx="5" ry="5" width="95%" height="30" />
          <rect x="3" y="210" rx="5" ry="5" width="95%" height="30" />
          <rect x="3" y="250" rx="5" ry="5" width="95%" height="30" />
          <rect x="3" y="290" rx="5" ry="5" width="95%" height="30" />
        </clipPath>
        <linearGradient id="fill">
          <stop offset="0.599964" stopColor="#C8D2E5" stopOpacity="1">
            <animate
              attributeName="offset"
              values="-2; -2; 1"
              keyTimes="0; 0.25; 1"
              dur="1s"
              repeatCount="indefinite"
            ></animate>
          </stop>
          <stop offset="1.59996" stopColor="#C8D2E5" stopOpacity="1">
            <animate
              attributeName="offset"
              values="-1; -1; 2"
              keyTimes="0; 0.25; 1"
              dur="1s"
              repeatCount="indefinite"
            ></animate>
          </stop>
          <stop offset="2.59996" stopColor="#6f76a0" stopOpacity="1">
            <animate
              attributeName="offset"
              values="0; 0; 3"
              keyTimes="0; 0.25; 1"
              dur="1s"
              repeatCount="indefinite"
            ></animate>
          </stop>
        </linearGradient>
      </defs>
    </svg>
  </>
);
interface LogBodyProps {
  isOpen: boolean;
  children: React.ReactNode;
}

export function LogBody({ isOpen, children }: LogBodyProps) {
  return (
    <div
      className={`relative ${isOpen ? 'z-10' : 'z-0'} flex h-[823px] w-[465px] flex-col bg-background-5 p-4 text-sm/4 font-medium sm:size-full sm:pe-0 md:h-[823px] md:w-[465px] md:pe-4`}
    >
      {children}
    </div>
  );
}

export function LogTableHeader({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex border-b border-text-6 px-2 pb-4 pt-0 text-sm font-semibold">
      {children}
    </div>
  );
}
interface LogTableDataRowProps {
  children: React.ReactNode;
}

export function LogTableDataRow({ children }: LogTableDataRowProps) {
  return <div className="flex min-h-full w-[446px] p-2 text-sm">{children}</div>;
}

interface LogTableBodyProps {
  innerRef: React.RefObject<HTMLDivElement>;
  children?: React.ReactNode;
}

export function LogTableBody({ children, innerRef }: LogTableBodyProps) {
  return (
    <div ref={innerRef} className="mt-1 h-[95%] w-full overflow-y-auto overflow-x-hidden">
      {children}
    </div>
  );
}

export function LogEmpty({ children }: { children: React.ReactNode }) {
  return (
    <div className="absolute left-1/2 top-[55%] -translate-x-1/2 -translate-y-1/2 text-sm font-medium">
      {children}
    </div>
  );
}

export function HistoryTableHeader({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex justify-between border-b border-text-6 px-2 pb-4 pt-0 text-sm/4 font-semibold text-text-2 sm:me-3 sm:text-xs md:me-0 md:text-sm/4">
      {children}
    </div>
  );
}

type HistoryTableBodyProp = {
  innerRef: React.RefObject<HTMLDivElement>;
  children?: React.ReactNode;
};

export function HistoryTableBody({ children, innerRef }: HistoryTableBodyProp) {
  return (
    <div ref={innerRef} className="mt-1 h-[95%] w-full overflow-y-auto overflow-x-hidden">
      {children}
    </div>
  );
}

export function HistoryTableDataRow({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex w-full items-center justify-between p-2 text-sm font-medium text-text-2 hover:bg-background-10 sm:text-xs md:text-sm">
      {children}
    </div>
  );
}
export function PerformanceTableDataRow({ children }: { children: React.ReactNode }) {
  return (
    <div className="grid grid-cols-3 gap-x-1.5 gap-y-[25px] font-semibold sm:grid-cols-2 sm:gap-4 md:grid-cols-3 md:gap-x-1.5 md:gap-y-[25px]">
      {children}
    </div>
  );
}
