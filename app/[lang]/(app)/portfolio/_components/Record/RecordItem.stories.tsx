import { RecordRestart as LaunchLiveTradeStoryModal } from '@/(app)/marketplace/_components/LaunchLiveTradeModal/LaunchLiveTradeModal.stories';
import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import {
  BrokerChargingMethod,
  BrokerStatus,
  LiveTradeBrokerTaskDocument,
  LiveTradeBrokerTaskQueryVariables,
  StrategyInvestmentTrend,
  StrategyInvestmentType,
  SupportedExchange,
} from '__generated__/types.app';
import { toaster } from 'app/[lang]/_components/Toast/Toast';
import { fromValue } from 'wonka';
import TrashIcon from '../assets/trashIcon.svg';
import { Primary as StopLiveTrading } from './StopLiveTradingPopup.stories';

import { RecordItem } from './RecordItem';

const getMockTask = ({ status }: { status?: BrokerStatus }) => {
  return {
    timePeriod: '2021-05-30 00:00 ~ 2021-05-30 00:00',
    roi: 0.1312,
    status: status || BrokerStatus.Running,
    strategyId: '75995',
    taskId: '75995',
    developerAvatar:
      'https://ca-user-avatars.s3.ap-northeast-1.amazonaws.com/f89c0bdf-c5b3-4c36-8537-2e3bdd23ec81',
    deverloperId: '12345',
    strategyName: 'MACross in PY copied (958)',
    investmentType: StrategyInvestmentType.Spot,
    investmentTrend: StrategyInvestmentTrend.Long,
    targetExchangePair: { okex: { pairs: ['BTC/USDT'] } },
    takeProfit: '+50.00%',
    stopLoss: '-20.00%',
    baseExchange: SupportedExchange.Okex,
    autoClosePosition: false,
    openPositionHandling: 'Keep on my own',
    apiKeyLabel: 'apple#1',
    pair: {
      base: 'BTC',
      quote: 'USDT',
    },
    tooltipStrategyName: 'MACross in PY copied (958)',
    chargingMethod: BrokerChargingMethod.ProfitSharing,
  };
};

const meta: Meta<typeof RecordItem> = {
  title: 'Portfolio/Record/RecordItem',
  component: RecordItem,
  args: {
    task: getMockTask({}),
  },
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof RecordItem>;

export const Pending: Story = {
  parameters: {
    urql: {
      executeQuery: (req) => {},
      executeMutation: (req) => {
        const res = StopLiveTrading.parameters?.urql?.executeMutation?.(req);
        if (res) return res;
      },
      executeSubscription: (req) => {},
    },
  },
  args: {
    task: getMockTask({ status: BrokerStatus.Pending }),
    currentUserId: '12345',
    isTabShown: false,
    setSelectedTask: fn(),
  },
  play: async ({ args, canvasElement }) => {
    await window.takeSnapshot?.();
  },
};

export const LiveTrading: Story = {
  parameters: {
    urql: {
      // https://formidable.com/open-source/urql/docs/advanced/testing/
      executeQuery: (req) => {},
      executeMutation: (req) => {
        const res = StopLiveTrading.parameters?.urql?.executeMutation?.(req);
        if (res) return res;
      },
      executeSubscription: (req) => {},
    },
  },
  args: {
    task: getMockTask({ status: BrokerStatus.Running }),
    currentUserId: '12345',
    isTabShown: false,
    setSelectedTask: fn(),
  },
  play: async ({ args, canvasElement }) => {
    await window.takeSnapshot?.();
  },
};

export const Stopped: Story = {
  parameters: {
    urql: {
      // https://formidable.com/open-source/urql/docs/advanced/testing/
      executeQuery: (req) => {
        if (req.query == LiveTradeBrokerTaskDocument) {
          const { taskId } = req.variables as LiveTradeBrokerTaskQueryVariables;
          return fromValue({
            data: {
              getBroker: {
                taskId: taskId,
                stopLossRatio: 1,
                lockGainRatio: 2,
                leverage: 1,
                strategyInvestmentType: 'USD_M_FUTURES',
                autoClosePosition: true,
                baseCurrency: 'USDT',
                initAssetsValues: [
                  {
                    currency: 'USDT',
                    amount: 10000.0,
                    equivalentQuoteAmount: 10000.0,
                  },
                ],
                performance: {
                  startingCapital: 1000000.0,
                  finalCapital: 1131200.0,
                  sharpeRatio: 0.04,
                  winRate: 0.5,
                  roi: 0.1312,
                  mddPercentage: 2.7454,
                  roiRealized: 0.1512,
                  roiUnrealized: -0.02,
                  buyAndHoldEquity: 0.09,
                  profitFactor: 2.03,
                  oddsRatio: 5.59,
                  returnDivDd: 0.44,
                  tradeCount: 100,
                  longestTimeToMakeNewHigh: 5.25,
                },
                strategy: {
                  battlefieldStrategy: {
                    suggestedStopPoints: 0.01,
                    suggestedTakeProfit: 0.02,
                  },
                },
              },
            },
          });
        }
        const res = LaunchLiveTradeStoryModal.parameters?.urql?.executeQuery?.(req);
        if (res) return res;
      },
      executeMutation: (req) => {},
      executeSubscription: (req) => {},
    },
  },
  args: {
    task: getMockTask({ status: BrokerStatus.Stopped }),
    isTabShown: false,
    onDeleteTask: () => {
      toaster.success(
        { message: 'Successfully Deleted!' },
        {
          position: 'top-center',
          className: 'gap-10 ps-[74px] first:mt-11',
          icon: <TrashIcon className="-m-4 size-11 [&_path]:text-text-1" />,
        },
      );
    },
    currentUserId: '12345',
    setSelectedTask: fn(),
  },
  play: async ({ args, canvasElement }) => {
    await window.takeSnapshot?.();
  },
};

export const OtherUserStrategy: Story = {
  parameters: {
    urql: {
      // https://formidable.com/open-source/urql/docs/advanced/testing/
      executeQuery: (req) => {
        const res = LaunchLiveTradeStoryModal.parameters?.urql?.executeQuery?.(req);
        if (res) return res;
      },
      executeMutation: (req) => {},
      executeSubscription: (req) => {},
    },
  },
  args: {
    task: getMockTask({ status: BrokerStatus.Stopped }),
    isTabShown: false,
    onDeleteTask: fn(),
    currentUserId: '67890',
    setSelectedTask: fn(),
  },
  play: async ({ args, canvasElement }) => {
    await window.takeSnapshot?.();
  },
};
