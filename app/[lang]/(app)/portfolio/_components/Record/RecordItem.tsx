'use client';

import { Button } from '@/_components/Button';
import { ExchangeIcons } from '@/_components/Icon/ExchangeIcon';
import { MultiOverlappingPair } from '@/_components/Pair';
import { Popover, PopoverTrigger } from '@/_components/Popover';
import { useBreakPoint } from '@/_hook/useBreakPoint';
import { useTranslation } from '@/_services/i18n/client';
import { ExchangeMap } from '@/_services/utils/strategy';
import { tw } from '@/_services/utils/tailwind';
import {
  BrokerChargingMethod,
  BrokerStatus,
  StrategyInvestmentTrend,
  StrategyInvestmentType,
  SupportedExchange,
} from '__generated__/types.app';
import clsx from 'clsx';
import { useState } from 'react';
import { Button as AriaButton, Dialog } from 'react-aria-components';
import { match } from 'ts-pattern';
import { toFixed } from 'utils';
import { LaunchLiveTradeModal } from '../../../marketplace/_components';
import TrashHoverIcon from '../assets/trashHoverIcon.svg';
import TrashIcon from '../assets/trashIcon.svg';
import ViewHoverIcon from '../assets/viewHoverIcon.svg';
import ViewIcon from '../assets/viewIcon.svg';
import { StopLiveTradingPopup } from './StopLiveTradingPopup';

export type RecordTaskInfo = {
  developerAvatar: string;
  deverloperId: string;
  strategyName: string;
  strategyId: string;
  taskId: string;
  pair: {
    base: string;
    quote: string;
  };
  tooltipStrategyName: string;
  timePeriod: string;
  investmentType: StrategyInvestmentType;
  investmentTrend: StrategyInvestmentTrend;
  baseExchange: SupportedExchange;
  stopLoss: string;
  takeProfit: string;
  autoClosePosition: boolean;
  openPositionHandling: string;
  apiKeyLabel: string;
  roi: number;
  status: BrokerStatus;
  chargingMethod: BrokerChargingMethod | undefined | null;
};

type RecordItemProp = {
  task: RecordTaskInfo;
  isTabShown: boolean;
  onDeleteTask: (taskId: string) => void;
  onStopTask: (taskId: string) => void;
  onRestartTask: (taskId: string) => void;
  currentUserId: string;
  setSelectedTask: () => void;
  className?: string;
};

function RoiColumn({ roi }: { roi: number }) {
  return (
    <div>
      {roi >= 0 ? (
        <span className="text-base font-semibold text-green-positive">
          +{toFixed(roi * 100, 2)}%
        </span>
      ) : (
        <span className="text-base font-semibold text-red-1">{toFixed(roi * 100, 2)}%</span>
      )}
    </div>
  );
}

function ViewTooltipRow({ item, value }: { item: string; value: string }) {
  return (
    <div className="flex gap-2.5">
      <div className="w-[147px]">{item}</div>
      <div className="line-clamp-2 w-[119px]">{value}</div>
    </div>
  );
}

export function RecordItem({
  task,
  isTabShown,
  onDeleteTask,
  onStopTask,
  onRestartTask,
  currentUserId,
  className,
  setSelectedTask,
}: RecordItemProp) {
  const { t } = useTranslation();
  const breakpoint = useBreakPoint();
  const [viewIsHovered, setViewIsHovered] = useState(false);
  const [trashIsHovered, setTrashIsHovered] = useState(false);
  const [showStopLiveTradingPopup, setShowStopLiveTradingPopup] = useState(false);
  const [showLiveTrade, setShowLiveTrade] = useState(false);
  const ExchangeIcon = ExchangeIcons[task.baseExchange as SupportedExchange];

  const statusCellData: { className: string; text: string } = match(task.status)
    .with(BrokerStatus.Pending, () => ({
      className: tw`bg-yellow-1 shadow-[0_0_6px_0_#FFCC33]`,
      text: t('mapping-status.Pending'),
    }))
    .with(BrokerStatus.Running, () => ({
      className: tw`bg-primary-1 shadow-[0_0_6px_0_#3F73FF]`,
      text: t('mapping-status.Live Trading'),
    }))
    .otherwise(() => ({ className: 'bg-background-1', text: t('mapping-status.User Stopped') }));

  return (
    <>
      {showStopLiveTradingPopup &&
        [BrokerStatus.Running, BrokerStatus.Pending].includes(task.status) && (
          <StopLiveTradingPopup
            taskName={task.strategyName}
            taskId={task.taskId}
            onClose={() => setShowStopLiveTradingPopup(false)}
            onStop={() => onStopTask(task.taskId)}
          />
        )}
      {showLiveTrade && task.status === BrokerStatus.Stopped && (
        <LaunchLiveTradeModal
          strategyId={task.strategyId}
          taskId={task.taskId}
          setIsShow={setShowLiveTrade}
          onSuccess={(strategyId: string, taskId: string) => {
            onRestartTask(taskId);
          }}
        />
      )}
      <div
        className={`${className} relative z-10 flex h-[54px] w-full shrink-0 items-center justify-between bg-background-5 px-4 text-sm text-text-2 hover:bg-background-10`}
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            setSelectedTask();
          }
        }}
      >
        {isTabShown || task.status === BrokerStatus.Stopped ? null : (
          <div className="flex min-w-[211px] items-center justify-center">
            {/* next image will return timeout*/}
            <div className="relative left-[-18px] flex">
              <img
                src={task.developerAvatar}
                width="48"
                height="48"
                alt="Developer"
                className="mr-[10px] rounded-full"
              />
              <PopoverTrigger triggerType="hover">
                <AriaButton>
                  <div className="w-[116px] truncate text-sm">{task.strategyName}</div>
                </AriaButton>
                <Popover showArrow={true} placement="bottom" offset={3}>
                  <Dialog className="p-2.5 text-sm text-text-2 outline outline-0">
                    <p className="whitespace-pre-wrap">{task.strategyName}</p>
                  </Dialog>
                </Popover>
              </PopoverTrigger>
            </div>
          </div>
        )}
        <div className="pointer-events-none w-[59px] truncate text-center">{task.taskId}</div>
        <div className="pointer-events-none flex w-[100px] items-center justify-center gap-2.5">
          {ExchangeIcon && <ExchangeIcon className="size-[30px] pr-px text-text-1" />}
          <MultiOverlappingPair pairs={[task.pair]} className="first:*:mr-1" />
        </div>
        <PopoverTrigger triggerType="hover">
          <AriaButton
            className="mt-px flex h-[22px] w-[53px] items-center justify-center text-text-2"
            onHoverStart={() => setViewIsHovered(true)}
            onHoverEnd={() => setViewIsHovered(false)}
          >
            <div>
              {viewIsHovered ? (
                <ViewHoverIcon height={69} width={69} />
              ) : (
                <ViewIcon height={24} width={24} />
              )}
            </div>
          </AriaButton>
          <Popover
            showArrow={true}
            placement={breakpoint === 'sm' ? 'top' : 'right'}
            offset={3}
            containerPadding={25}
          >
            <Dialog
              className="w-[310px] p-2.5 text-xs leading-[150%] text-text-2 outline outline-0"
              aria-label="Broker Info"
            >
              <ViewTooltipRow item={t('Strategy Name')} value={task.tooltipStrategyName} />
              <ViewTooltipRow item={t('Time Period')} value={task.timePeriod} />
              <ViewTooltipRow item={t('Type')} value={task.investmentType} />
              <ViewTooltipRow item={t('Trend')} value={task.investmentTrend} />
              <ViewTooltipRow item={t('Exchange')} value={ExchangeMap[task.baseExchange || '']} />
              <ViewTooltipRow
                item={t('base-quote')}
                value={task.pair.base + ' / ' + task.pair.quote}
              />
              <ViewTooltipRow item={t('stop-loss')} value={task.stopLoss} />
              <ViewTooltipRow item={t('take-profit')} value={task.takeProfit} />
              <ViewTooltipRow
                item={t('open-position-handling')}
                value={task.openPositionHandling}
              />
            </Dialog>
          </Popover>
        </PopoverTrigger>
        <div className="pointer-events-none flex w-[90px] items-center justify-center text-center">
          <RoiColumn roi={task.roi || 0} />
        </div>
        <div className="pointer-events-none flex w-[120px] items-center gap-2">
          <span className={clsx('size-2 rounded-full', statusCellData.className)} />
          <div className="text-text-3">{statusCellData.text}</div>
        </div>
        <div>
          {[BrokerStatus.Running, BrokerStatus.Pending].includes(task.status) ? (
            <AriaButton
              className="h-[30px] w-[100px] rounded bg-primary-5 text-sm font-medium text-text-1 hover:bg-background-modal-portfolio-contained-button-hover"
              onPress={() => {
                setShowStopLiveTradingPopup(true);
              }}
            >
              {t('stop')}
            </AriaButton>
          ) : (
            <Button
              variant="contained"
              className="h-[30px] w-[100px] rounded bg-primary-5 text-sm font-medium text-text-1 hover:bg-background-modal-portfolio-contained-button-hover"
              onClick={() => setShowLiveTrade(true)}
            >
              {t('btn.Recopy')}
            </Button>
          )}
        </div>
        {task.status === BrokerStatus.Stopped &&
          (task.roi === 0 ? (
            <AriaButton
              className="flex h-[30px] w-[83px] items-center justify-center text-text-2 outline-none transition-none"
              onHoverStart={() => setTrashIsHovered(true)}
              onHoverEnd={() => setTrashIsHovered(false)}
              onPress={() => onDeleteTask(task.taskId)}
            >
              {trashIsHovered ? (
                <TrashHoverIcon className="w-full" />
              ) : (
                <TrashIcon className="w-full" />
              )}
            </AriaButton>
          ) : (
            <div className="flex h-[30px] w-[83px] items-center justify-center text-background-2">
              {<TrashIcon />}
            </div>
          ))}
      </div>
    </>
  );
}
