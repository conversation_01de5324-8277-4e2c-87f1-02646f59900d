'use client';

import SortAsc from '@/_components/Icon/assets/SortAsc.svg';
import SortDesc from '@/_components/Icon/assets/SortDesc.svg';
import SortNone from '@/_components/Icon/assets/SortNone.svg';
import { Loading } from '@/_components/Loading';
import { RollerBar } from '@/_components/RollerBar';
import { toaster } from '@/_components/Toast/Toast';
import { useTranslation } from '@/_services/i18n/client';
import {
  BrokerStatus,
  GetMyTraderTasksQuery,
  GetMyTraderTasksQueryVariables,
  TraderTaskFilterInput,
  useDeleteBrokerTaskMutation,
  useGetMyTraderTasksQuery,
} from '__generated__/types.app';
import { TFunction } from 'next-i18next';
import { usePathname, useRouter } from 'next/navigation';
import { Dispatch, SetStateAction, useCallback, useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroller';
import { match } from 'ts-pattern';
import { TabName } from '../Portfolio';
import RecordNotFound from '../assets/recordNotFound.svg';
import TrashIcon from '../assets/trashIcon.svg';
import { RecordItem, RecordTaskInfo } from './RecordItem';

type RecordProp = {
  className?: string;
  isTabShown: boolean;
  isStopped: boolean;
  selectedTask:
    | GetMyTraderTasksQuery['me']['traderTasksCursor']['edges'][number]['node']
    | undefined;
  setSelectedTask: Dispatch<
    SetStateAction<
      GetMyTraderTasksQuery['me']['traderTasksCursor']['edges'][number]['node'] | undefined
    >
  >;
  setTab: Dispatch<SetStateAction<TabName>>;
  setIsStopped: Dispatch<SetStateAction<boolean>>;
};

type RecordBodyPageProp = {
  variables: GetMyTraderTasksQueryVariables;
  setEndCursor: Dispatch<SetStateAction<string>>;
  isTabShown: boolean;
  isStopped: boolean;
  selectedTask:
    | GetMyTraderTasksQuery['me']['traderTasksCursor']['edges'][number]['node']
    | undefined;
  setSelectedTask: Dispatch<
    SetStateAction<
      GetMyTraderTasksQuery['me']['traderTasksCursor']['edges'][number]['node'] | undefined
    >
  >;
  setIsEmpty: Dispatch<SetStateAction<boolean>>;
  setTab: Dispatch<SetStateAction<TabName>>;
  setIsStopped: Dispatch<SetStateAction<boolean>>;
  setIsError: Dispatch<SetStateAction<boolean>>;
};

function parseRecordTaskInfo(
  data: GetMyTraderTasksQuery['me']['traderTasksCursor']['edges'][number]['node'],
  t: TFunction,
): RecordTaskInfo {
  const pair = (
    Object.values(data.targetExchangePair) as Array<{ pairs: string[] }>
  )[0]?.pairs[0]?.split(':')[0];
  return {
    developerAvatar: data.strategy?.owner.avatarUrl || '',
    deverloperId: data.strategy?.owner.id || '',
    strategyName: data.strategy?.name || '',
    strategyId: data.strategy?.id || '',
    taskId: data.id || '',
    pair: {
      base: pair?.split('/')[0] || 'BTC',
      quote: pair?.split('/')[1] || 'USDT',
    },
    tooltipStrategyName:
      data.strategy?.name ||
      data.strategy?.templateName + ' #' + data.strategy?.templateSerialNumber ||
      '',
    timePeriod:
      data.status !== BrokerStatus.Stopped
        ? data.createdAt + ' ~'
        : data.createdAt + ' ~ ' + (data.rangeEnd || data.updatedAt),
    investmentType: data.strategy?.investmentType || t('spot'),
    investmentTrend: data.strategy?.investmentTrend || t('long'),
    baseExchange: data.baseExchange,
    stopLoss: data.stopLossRatio ? '-' + data.stopLossRatio.toFixed(2) + '%' : '',
    takeProfit: data.lockGainRatio ? '+' + data.lockGainRatio.toFixed(2) + '%' : '',
    autoClosePosition: data.autoClosePosition || false,
    openPositionHandling: data.autoClosePosition ? t('sell-w-mrkt-price') : t('keep-on-my-own'),
    apiKeyLabel: data.exchangeApiKeys?.length ? data.exchangeApiKeys[0].comment || 'N/A' : 'N/A',
    roi: data.roi || 0,
    status: data.status,
    chargingMethod: data.chargingMethod,
  };
}

function Roi() {
  const [direction, setDirection] = useState<String | null>(null);
  const { t } = useTranslation();
  const icon = match(direction)
    .with(null, () => <SortNone />)
    .with('asc', () => <SortAsc />)
    .with('desc', () => <SortDesc />)
    .otherwise(() => <SortNone />);

  const onClick = useCallback(() => {
    if (direction === 'asc' || direction === null) {
      return setDirection('desc');
    }
    if (direction === 'desc') {
      return setDirection('asc');
    }
  }, [direction]);

  return (
    <button
      onClick={onClick}
      className={`cursor-auto ${direction !== null ? 'text-primary-5' : 'text-text-2'} relative left-1 flex min-w-[90px] items-center justify-center`}
    >
      <span>{t('ROI')}</span>
      <span className="pl-2">{icon}</span>
    </button>
  );
}

const RecordBodyPage = ({
  variables,
  setEndCursor,
  isStopped,
  isTabShown,
  selectedTask,
  setSelectedTask,
  setIsEmpty,
  setIsStopped,
  setTab,
  setIsError,
}: RecordBodyPageProp) => {
  const { t } = useTranslation();
  const router = useRouter();
  const pathname = usePathname();
  const params =
    typeof window !== 'undefined' ? new URLSearchParams(window.location.search) : undefined;
  const paramTaskId = params?.get('task');

  const [{ data, fetching, error }, reexecuteQuery] = useGetMyTraderTasksQuery({
    variables,
    requestPolicy: 'network-only',
  });
  const result = data?.me.traderTasksCursor;

  const [{ fetching: fetchingBrokerTask }, deleteBrokerTask] = useDeleteBrokerTaskMutation();
  const deleteTask = (taskId: string) => {
    const variables = {
      taskId,
    };
    deleteBrokerTask(variables)
      .then((result) => {
        if (result.data?.deleteBrokerTask === 'OK') {
          toaster.success(
            { message: t('successfully-deleted') },
            {
              position: 'top-center',
              className: 'gap-10 ps-[74px] first:mt-11',
              icon: <TrashIcon className="-m-4 size-11 [&_path]:text-text-1" />,
            },
          );
          reexecuteQuery();
        } else {
          toaster.error(
            { message: t('can-not-delete-this-broker') },
            { position: 'top-center', className: 'first:mt-11' },
          );
        }
      })
      .catch((e) => {
        toaster.error(
          { message: e.message || t('can-not-delete-this-broker') },
          { position: 'top-center', className: 'first:mt-11' },
        );
      });
  };

  const findTaskById = (id: string | undefined) => result?.edges.find((e) => e.node.id === id);

  useEffect(() => {
    if (variables.after) {
      return;
    }

    if (paramTaskId) {
      const matchedSelectedTask = findTaskById(paramTaskId)?.node;
      if (matchedSelectedTask) {
        setSelectedTask(matchedSelectedTask);
        router.push(`${pathname}`);
      }
    } else if (result?.edges && result?.edges[0]?.node) {
      setSelectedTask(result?.edges[0].node);
    }
  }, [result, isStopped]);

  if (error) {
    setIsError(true);
    console.log(`Error: ${error.message}`);
    return;
  }
  if (fetching) {
    console.log(`Loading...`);
    return;
  }
  if (!variables.after && result?.edges.length == 0) {
    setIsEmpty(true);
  }
  if (result?.pageInfo.endCursor) {
    setEndCursor(result?.pageInfo.endCursor);
  }

  return (
    <div>
      {result?.edges &&
        result.edges.map((edge) => {
          const task = parseRecordTaskInfo(edge.node, t);
          return (
            <RecordItem
              key={`record-item-${task.taskId}`}
              task={task}
              isTabShown={isTabShown}
              className="mb-3"
              onDeleteTask={() => {
                deleteTask(task.taskId);
              }}
              currentUserId={data?.me.id || ''}
              setSelectedTask={() => {
                setSelectedTask(edge.node);
              }}
              onStopTask={(taskId) => {
                console.log('onStopTask', taskId);
                setTab(TabName.Stopped);
                setIsStopped(true);
                setSelectedTask(undefined);
              }}
              onRestartTask={(taskId) => {
                console.log('onRestartTask', taskId);
                setTab(TabName.LiveTrading);
                setIsStopped(false);
                router.push(`${pathname?.replace(/portfolio.*$/, `portfolio?task=${taskId}`)}`);
              }}
            />
          );
        })}
    </div>
  );
};

export function Record({
  className,
  isTabShown,
  isStopped,
  selectedTask,
  setSelectedTask,
  setTab,
  setIsStopped,
}: RecordProp) {
  const params =
    typeof window !== 'undefined' ? new URLSearchParams(window.location.search) : undefined;
  const paramTaskId = params?.get('task');
  const limit = 5;
  const scrollRecordRef = useRef<HTMLDivElement>(null);
  const [pageVariables, setPageVariables] = useState<GetMyTraderTasksQueryVariables[]>([
    { first: limit, after: null },
  ]);
  const [endCursor, setEndCursor] = useState<string>('');
  const [isEmpty, setIsEmpty] = useState(false);
  const [isError, setIsError] = useState(false);

  const { t } = useTranslation();
  const [filter, setFilter] = useState<TraderTaskFilterInput>({
    investmentTypes: [],
    investmentTrends: [],
    exchanges: [],
    exchangePairs: [],
    statuses: [],
    results: [],
    maxRoi: null,
    minRoi: null,
    roiOrder: null,
  });
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (paramTaskId && !selectedTask) {
      interval = setInterval(() => {
        console.log('refetch');
        setPageVariables([{ first: limit, after: null }]);
        setEndCursor('');
        setIsEmpty(false);
      }, 2500);
    }
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [selectedTask, paramTaskId]);

  useEffect(() => {
    if (isStopped && !filter.statuses?.includes(BrokerStatus.Stopped)) {
      setFilter((e) => ({
        ...e,
        statuses: [BrokerStatus.Stopped],
      }));
    }
    if (!isStopped && !filter.statuses?.includes(BrokerStatus.Running)) {
      setFilter((e) => ({
        ...e,
        statuses: [BrokerStatus.Pending, BrokerStatus.Running],
      }));
    }
    setPageVariables([{ first: limit, after: null }]);
    setEndCursor('');
    setIsEmpty(false);
  }, [isStopped, filter]);

  return (
    <div className={`box-border grid grid-rows-[3rem_1fr] ${className} overflow-hidden`}>
      <div className="relative z-10 flex w-full items-center bg-background-11 px-[15px] py-[9px] text-base text-text-0">
        {t('Record')}
      </div>
      <div
        className={`bg-background-5 pb-4 pt-6 ${
          isTabShown ? (!isStopped ? 'pl-3.5' : 'pl-0') : 'pl-2'
        } overflow-hidden pr-[22px]`}
      >
        {isEmpty ? (
          <div className="mt-[71px] flex items-center justify-center px-5">
            <div>
              <div className="flex items-center justify-center text-center">
                <RecordNotFound />
              </div>
              <p className="mt-[9px] text-sm text-text-3">{t('There is NO record found')}</p>
            </div>
          </div>
        ) : (
          <div className="relative h-full">
            <RollerBar height="100%" type="modal" className="relative -right-3.5">
              <div className="flex h-full min-w-max flex-col pr-2">
                <div className="z-10 mb-0.5 flex items-center justify-between px-4 text-sm font-semibold">
                  {isTabShown || isStopped ? null : (
                    <div className="flex w-[211px]">
                      <div>{t('Developer/Strategy Label')}</div>
                    </div>
                  )}
                  <div className="w-[59px] text-center">{t('column.id')}</div>
                  <div className="w-[100px] text-center">{t('Market')}</div>
                  <div className="w-[53px] text-center">{t('View')}</div>
                  <div className="w-[90px]">
                    <Roi />
                  </div>
                  <div className="w-[150px] text-center">{t('Status')}</div>
                  <div className={'w-[100px] text-center'}>{t('column.action')}</div>
                  <div className={`${isStopped ? 'w-[83px]' : 'hidden'} text-center`}>
                    {t('delete')}
                  </div>
                </div>
                <div className="relative flex-1">
                  <RollerBar type="modal" height="100%">
                    <InfiniteScroll
                      pageStart={0}
                      loadMore={() => {
                        if (
                          endCursor != pageVariables[pageVariables.length - 1].after &&
                          endCursor
                        ) {
                          setPageVariables([...pageVariables, { first: limit, after: endCursor }]);
                        }
                      }}
                      hasMore={endCursor != pageVariables[pageVariables.length - 1].after}
                      loader={!isError ? <Loading variant="spin" /> : <></>}
                      useWindow={false}
                    >
                      <div ref={scrollRecordRef}>
                        {!isEmpty && (
                          <>
                            {pageVariables.map((variables, i) => (
                              <RecordBodyPage
                                key={'' + variables.after}
                                variables={{ filter, ...variables, reverseOrder: true }}
                                setEndCursor={setEndCursor}
                                isStopped={isStopped}
                                isTabShown={isTabShown}
                                selectedTask={selectedTask}
                                setSelectedTask={setSelectedTask}
                                setIsEmpty={setIsEmpty}
                                setTab={setTab}
                                setIsStopped={setIsStopped}
                                setIsError={setIsError}
                              />
                            ))}
                          </>
                        )}
                      </div>
                    </InfiniteScroll>
                  </RollerBar>
                </div>
              </div>
            </RollerBar>
          </div>
        )}
      </div>
    </div>
  );
}
