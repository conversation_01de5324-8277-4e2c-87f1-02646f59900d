query LiveTradeBrokerTask($taskId: ID!) {
  getBroker(id: $taskId) {
    id
    baseExchange
    createdAt
    rangeEnd
    stopLossRatio
    lockGainRatio
    leverage
    strategyInvestmentType
    autoClosePosition
    baseCurrency
    initAssetsValues {
      currency
      amount
      equivalentQuoteAmount
    }
    profitSharingRatio
    performance {
      startingCapital
      finalCapital
      tradeCount
      roi
      roiRealized
      roiUnrealized
      winRate
      mddPercentage
      profitFactor
      oddsRatio
      sharpeRatio
      returnDivDd
      longestTimeToMakeNewHigh
      buyAndHoldEquity
    }
    strategy {
      targetCurrency
      battlefieldStrategy {
        suggestedStopPoints
        suggestedTakeProfit
      }
    }
  }
}
