import type { Meta, StoryObj } from '@storybook/react';
import { within } from '@storybook/test';
import {
  LiveTradeBrokerTaskDocument,
  LiveTradeBrokerTaskQueryVariables,
} from '__generated__/types.app';
import { fromValue } from 'wonka';
import { PerformanceSection } from './PerformanceSection';

const meta: Meta<typeof PerformanceSection> = {
  title: 'Portfolio/PerformanceSection',
  component: PerformanceSection,
  args: {
    isOpen: true,
  },
  argTypes: {
    taskId: { control: { type: 'text' } },
  },
  decorators: [
    (Story) => (
      <div className="h-[calc(100vh-2rem)] min-w-[360px]">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof PerformanceSection>;

let suggestedStopLossRatio = 10;
let suggestedLockGainRatio = 50;
let manualStopLossRatio = 1;
let manualLockGainRatio = 5;
let autoClosePosition = true;

export const SuggestedRatio: Story = {
  args: {
    taskId: '1',
  },
  parameters: {
    urql: {
      executeQuery: (req) => {
        if (req.query === LiveTradeBrokerTaskDocument) {
          const { taskId } = req.variables as LiveTradeBrokerTaskQueryVariables;
          if (taskId === '1') {
            return fromValue({
              data: {
                getBroker: {
                  taskId,
                  stopLossRatio: suggestedStopLossRatio,
                  lockGainRatio: suggestedLockGainRatio,
                  autoClosePosition: autoClosePosition,
                  leverage: 1,
                  strategyInvestmentType: 'USD_M_FUTURES',
                  baseCurrency: 'USDT',
                  initAssetsValues: [
                    {
                      currency: 'USDT',
                      amount: 10000.0,
                      equivalentQuoteAmount: 10000.0,
                    },
                  ],
                  performance: {
                    startingCapital: 1000000.0,
                    finalCapital: 1131200.0,
                    sharpeRatio: 0.04,
                    winRate: 0.5,
                    roi: 0.1312,
                    mddPercentage: 2.7454,
                    roiRealized: 0.1512,
                    roiUnrealized: -0.02,
                    buyAndHoldEquity: 0.09,
                    profitFactor: 2.03,
                    oddsRatio: 5.59,
                    returnDivDd: 0.44,
                    tradeCount: 100,
                    longestTimeToMakeNewHigh: 5.25,
                  },
                  strategy: {
                    battlefieldStrategy: {
                      suggestedStopPoints: 0.1,
                      suggestedTakeProfit: 0.5,
                    },
                  },
                },
              },
            });
          }
        }
      },
      executeMutation: (req) => {
        const result = req.variables;
        suggestedStopLossRatio = result?.userSetting.stopLossRatio;
        suggestedLockGainRatio = result?.userSetting.lockGainRatio;
        autoClosePosition = result?.userSetting.autoClosePosition;

        return fromValue({
          data: {
            updateLiveTradeSettings: {
              taskId: result?.taskId,
              userSetting: {
                stopLossRatio: result?.userSetting.stopLossRatio,
                lockGainRatio: result?.userSetting.lockGainRatio,
                autoClosePosition: result?.userSetting.autoClosePosition,
              },
            },
          },
        });
      },
      executeSubscription: (req) => {},
    },
  },
  play: async ({ args, canvasElement }) => {
    await window.takeSnapshot?.();

    const canvas = within(canvasElement.querySelector('#app') as HTMLElement);
    // https://storybook.js.org/docs/writing-tests/interaction-testing
  },
};

export const ManualRatio: Story = {
  args: {
    taskId: '2',
  },
  parameters: {
    urql: {
      executeQuery: (req) => {
        if (req.query === LiveTradeBrokerTaskDocument) {
          const { taskId } = req.variables as LiveTradeBrokerTaskQueryVariables;
          if (taskId === '2') {
            return fromValue({
              data: {
                getBroker: {
                  taskId,
                  stopLossRatio: manualStopLossRatio,
                  lockGainRatio: manualLockGainRatio,
                  autoClosePosition: autoClosePosition,
                  leverage: 1,
                  strategyInvestmentType: 'USD_M_FUTURES',
                  baseCurrency: 'USDT',
                  initAssetsValues: [
                    {
                      currency: 'USDT',
                      amount: 10000.0,
                      equivalentQuoteAmount: 10000.0,
                    },
                  ],
                  performance: {
                    startingCapital: 1000000.0,
                    finalCapital: 1100000.0,
                    sharpeRatio: 0.03,
                    winRate: 0.45,
                    roi: 0.1,
                    mddPercentage: 3.1,
                    roiRealized: 0.12,
                    roiUnrealized: -0.02,
                    buyAndHoldEquity: 0.08,
                    profitFactor: 1.9,
                    oddsRatio: 5.0,
                    returnDivDd: 0.38,
                    tradeCount: 80,
                    longestTimeToMakeNewHigh: 4.5,
                  },
                  strategy: {
                    battlefieldStrategy: {
                      suggestedStopPoints: 0.1,
                      suggestedTakeProfit: 0.5,
                    },
                  },
                },
              },
            });
          }
        }
      },
      executeMutation: (req) => {
        const result = req.variables;
        manualStopLossRatio = result?.userSetting.stopLossRatio;
        manualLockGainRatio = result?.userSetting.lockGainRatio;
        autoClosePosition = result?.userSetting.autoClosePosition;

        return fromValue({
          data: {
            updateLiveTradeSettings: {
              taskId: result?.taskId,
              userSetting: {
                stopLossRatio: result?.userSetting.stopLossRatio,
                lockGainRatio: result?.userSetting.lockGainRatio,
                autoClosePosition: result?.userSetting.autoClosePosition,
              },
            },
          },
        });
      },
      executeSubscription: (req) => {},
    },
  },
  play: async ({ args, canvasElement }) => {
    await window.takeSnapshot?.();

    const canvas = within(canvasElement.querySelector('#app') as HTMLElement);
    // https://storybook.js.org/docs/writing-tests/interaction-testing
  },
};

export const NoData: Story = {
  args: {
    taskId: '3',
  },
  parameters: {
    urql: {
      executeQuery: (req) => {
        if (req.query === LiveTradeBrokerTaskDocument) {
          return fromValue({
            data: {
              getBroker: {},
            },
          });
        }
      },
      executeMutation: (req) => {},
      executeSubscription: (req) => {},
    },
  },
  play: async ({ args, canvasElement }) => {
    await window.takeSnapshot?.();

    const canvas = within(canvasElement.querySelector('#app') as HTMLElement);
    // https://storybook.js.org/docs/writing-tests/interaction-testing
  },
};
