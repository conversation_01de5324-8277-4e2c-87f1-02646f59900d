'use client';

import { TabButton } from '@/_components/Button';
import ArrowLeft from '@/_components/Icon/assets/ArrowLeft.svg';
import ArrowRight from '@/_components/Icon/assets/ArrowRight.svg';
import { RollerBar } from '@/_components/RollerBar';
import { useBreakPoint } from '@/_hook/useBreakPoint';
import { useTranslation } from '@/_services/i18n/client';
import { GetMyTraderTasksQuery, useLiveTradeBrokerTaskQuery } from '__generated__/types.app';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
import { Button } from 'react-aria-components';
import { MdKeyboardArrowRight } from 'react-icons/md';
import { Tabs } from '../types';
import { ChartSection } from './ChartSection';
import { HistorySection } from './HistorySection';
import { LogSection } from './LogSection';
import { PerformanceSection } from './PerformanceSection';
import { Record } from './Record';
import { SectionToolTips } from './SectionToolTips';

export enum TabName {
  LiveTrading = 'liveTrading',
  Stopped = 'stopped',
}

export function Portfolio() {
  const { t } = useTranslation();
  const breakpoint = useBreakPoint();
  const breakpointCache = useRef(breakpoint);
  const [tab, setTab] = useState(TabName.LiveTrading);
  const [isStopped, setIsStopped] = useState(tab === TabName.Stopped);
  const [chartExpanded, setChartExpanded] = useState(false);
  const [tabIsOpen, setTabIsOpen] = useState(true);
  const [showTab, setShowTab] = useState<Tabs>(breakpoint === '2xl' ? Tabs.PERFORMANCE : Tabs.NONE);
  const [selectedTask, setSelectedTask] = useState<
    GetMyTraderTasksQuery['me']['traderTasksCursor']['edges'][number]['node'] | undefined
  >(undefined);
  const selectedTaskId = selectedTask?.id || '';
  const [{ data: brokerTask }, refetchBroker] = useLiveTradeBrokerTaskQuery({
    variables: {
      taskId: selectedTaskId,
    },
    pause: !selectedTaskId,
  });
  const isSideTabOpen = showTab !== Tabs.NONE;

  useEffect(() => {
    if (!selectedTask) {
      setShowTab(Tabs.NONE);
      return;
    }
    refetchBroker();
  }, [selectedTask, refetchBroker]);

  useEffect(() => {
    if (showTab === Tabs.NONE) {
      setTabIsOpen(false);
    } else {
      setTabIsOpen(true);
    }
  }, [showTab]);

  useEffect(() => {
    setChartExpanded(false);
  }, [isStopped]);

  useEffect(() => {
    if (breakpoint === '2xl') {
      setShowTab(Tabs.PERFORMANCE);
    } else if (breakpointCache.current === '2xl') {
      setShowTab(Tabs.NONE);
    }
    breakpointCache.current = breakpoint;
  }, [breakpoint]);

  return (
    <div className="grid h-full grid-cols-1 justify-items-center">
      <div className="min-h-0 w-full min-w-[calc(360px)] max-w-[calc(1440px-4rem)]">
        <div className="ml-[30px] flex h-full max-h-[calc(100vh-1rem)] flex-col pb-5 pe-0.5 pt-4 sm:mx-4 sm:max-h-[calc(100vh-0.75rem)] sm:py-0 sm:pe-0 md:mr-0 md:max-h-[calc(100vh-1rem)] md:pb-5 md:pe-0.5 md:pt-4 lg:ml-5">
          <div className="hidden items-center gap-4 py-2.5 sm:flex md:hidden">
            <div className="flex min-w-0 flex-1 gap-1.5">
              <p
                className={clsx(
                  'text-sm',
                  selectedTask?.strategy?.name ? 'text-text-3' : 'text-text-2',
                )}
              >
                {t('Portfolio')}
              </p>
              {selectedTask?.strategy?.name && (
                <>
                  <MdKeyboardArrowRight className="size-5 text-text-3" />
                  <p className="flex-1 truncate text-sm text-text-2">
                    {selectedTask.strategy.name}
                  </p>
                </>
              )}
            </div>
          </div>
          <div
            className={clsx(
              'flex items-end justify-between sm:mt-3 md:mt-0',
              tabIsOpen && 'sm:hidden md:flex',
            )}
          >
            <div className="flex gap-[6px] ps-4">
              <TabButton
                className="w-40 sm:h-8 sm:w-[110px] sm:text-xs md:h-9 md:w-40 md:text-base"
                active={tab === TabName.LiveTrading}
                onClick={() => {
                  setTab(TabName.LiveTrading);
                  setIsStopped(false);
                }}
              >
                {t('LIVE TRADING')}
              </TabButton>
              <TabButton
                className="w-40 sm:h-8 sm:w-[110px] sm:text-xs md:h-9 md:w-40 md:text-base"
                active={tab === TabName.Stopped}
                onClick={() => {
                  setTab(TabName.Stopped);
                  setIsStopped(true);
                }}
              >
                {t('STOPPED')}
              </TabButton>
            </div>
            <Button
              className="hidden h-10 w-4 items-center justify-center text-text-6 hover:text-text-1 sm:flex md:hidden"
              onPress={() => {
                setShowTab(Tabs.PERFORMANCE);
              }}
            >
              <ArrowLeft />
            </Button>
          </div>
          <RollerBar type="modal" height="100%" className="pe-2.5 sm:pe-0 md:pe-2.5">
            <div className="flex h-full">
              <div
                className={clsx(
                  'w-full min-w-0',
                  tabIsOpen && 'sm:hidden md:block',
                  chartExpanded ? 'pr-[30px] sm:pr-0 md:pr-[30px]' : 'md:pr-[54px]',
                  isSideTabOpen ? 'md:w-[calc(100%-465px)]' : 'md:w-full',
                )}
              >
                <ChartSection
                  isStopped={isStopped}
                  brokerTaskQuery={selectedTask ? brokerTask : undefined}
                  selectedTask={selectedTask}
                  isExpanded={chartExpanded}
                  onExpandedChange={setChartExpanded}
                  refetchBroker={refetchBroker}
                  height={'h-[367px]'}
                  width={'w-full'}
                />
                {(!chartExpanded || breakpoint === 'sm') && (
                  <div className="flex md:relative">
                    <Record
                      isTabShown={tabIsOpen}
                      isStopped={isStopped}
                      className={'h-[404px] w-full shrink'}
                      selectedTask={selectedTask}
                      setSelectedTask={setSelectedTask}
                      setTab={setTab}
                      setIsStopped={setIsStopped}
                    />
                  </div>
                )}
              </div>
              {(!chartExpanded || breakpoint === 'sm') && (
                <div
                  className={clsx(
                    'flex sm:w-full sm:flex-col md:absolute md:right-0 md:top-0 md:z-10 md:w-auto md:flex-row md:pe-2.5',
                    showTab === Tabs.NONE && 'sm:hidden md:flex',
                  )}
                >
                  <div className="ml-1 mt-[58px] flex justify-between sm:ml-0 sm:mt-0 sm:items-center md:ml-1 md:mt-[58px] md:items-start">
                    <SectionToolTips showTab={showTab} setShowTab={setShowTab} />
                    <Button
                      className="hidden size-4 justify-center text-text-1 sm:flex md:hidden"
                      onPressEnd={() => {
                        setShowTab(Tabs.NONE);
                      }}
                    >
                      <ArrowRight />
                    </Button>
                  </div>
                  <div className="flex-1">
                    {showTab === Tabs.NONE && <div className="w-1.5 md:w-0"></div>}
                    {showTab === Tabs.LOG && (
                      <LogSection taskId={selectedTaskId} isOpen={tabIsOpen} />
                    )}
                    {showTab === Tabs.HISTORY && (
                      <HistorySection taskId={selectedTaskId} isOpen={tabIsOpen} />
                    )}
                    {showTab === Tabs.PERFORMANCE && (
                      <PerformanceSection taskId={selectedTaskId} isOpen={tabIsOpen} />
                    )}
                  </div>
                </div>
              )}
            </div>
          </RollerBar>
        </div>
      </div>
    </div>
  );
}
