'use client';

import { useMemo, useState } from 'react';

import { useTranslation } from '@/_services/i18n/client';
import { useGetStrategiesQuery } from '__generated__/types.app';

// components
import { RollerBar } from '@/_components/RollerBar';
import { Filter, FilterState } from '../Filter';
import { AddNewStrategyButton } from './AddNewStrategyButton';
import { StrategiesListItem } from './StrategiesListItem';

type StrategiesListProp = {};

const StrategyTitle: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="flex h-[20px] min-w-[1306px] items-center justify-between pl-9 pr-5 text-sm font-medium text-text-2">
      <div className="w-[268px] grow pr-8 text-center">{t('column.name')}</div>
      <div className="w-[100px] grow text-center">{t('column.exchange')}</div>
      <div className="w-[100px] grow text-center">{t('column.pair')}</div>
      <div className="w-[100px] grow text-center">{t('View')}</div>
      <div className="w-[132px] grow text-center">{t('column.status')}</div>
      <div className="w-[180px] grow text-center">{t('Equity Curve')}</div>
      <div className="w-[172px] grow text-center">{t('column.action')}</div>
      <div className="w-[218px] grow text-center">{t('column.operation')}</div>
    </div>
  );
};

export function StrategiesList(props: StrategiesListProp) {
  const [filterState, setFilterState] = useState<FilterState>({
    types: [],
    trends: [],
    exchanges: [],
    pairs: [],
    status: [],
    minDd: undefined,
    maxDd: undefined,
    minRoi: undefined,
    minSharpeRatio: undefined,
    paymentMethods: [],
    period: undefined,
    keywords: '',
  });

  const [{ data }, refetchStrategies] = useGetStrategiesQuery({
    requestPolicy: 'network-only',
  });

  const filteredStrategies = useMemo(
    () =>
      data?.me.strategies?.filter((strategy) => {
        const { types, trends, exchanges, pairs, status } = filterState;
        const matchesType =
          types.length === 0 ||
          (strategy.investmentType && types.includes(strategy.investmentType));
        const matchesTrend =
          trends.length === 0 ||
          (strategy.investmentTrend && trends.includes(strategy.investmentTrend));
        const matchesExchange =
          exchanges.length === 0 ||
          (strategy.baseExchange && exchanges.includes(strategy.baseExchange));
        const matchesPair =
          pairs.length === 0 ||
          pairs.includes(`${strategy.targetCurrency}/${strategy.baseCurrency}`);
        const matchesStatus =
          status.length === 0 ||
          status.some((val) => {
            switch (val) {
              case 'Simulating':
                return strategy.isSimulating;
              case 'Live Trading':
                return strategy.isTrading;
              case 'Pending':
                return strategy.status === 'REGISTRY';
              case 'On Board':
                return strategy.status === 'VERIFIED' || strategy.status === 'ONBOARD';
              case 'Locked':
                return strategy.status === 'LOCKED';
              default:
                return false;
            }
          });
        return matchesType && matchesTrend && matchesExchange && matchesPair && matchesStatus;
      }),
    [data, filterState],
  );

  return (
    <div className="flex h-full flex-col gap-[22px]">
      <div className="flex w-full min-w-[1306px] flex-col gap-3">
        {/* Filter Tabs */}
        <div className="flex items-start justify-between">
          <div className="min-w-0 flex-1">
            <Filter filterState={filterState} setFilterState={setFilterState} />
          </div>
          <div className="ml-4 shrink-0">
            <AddNewStrategyButton />
          </div>
        </div>
      </div>

      <div className="flex min-h-0 flex-1 flex-col gap-5">
        <StrategyTitle />
        <RollerBar type="modal" height="100%">
          <div className="flex flex-col gap-4 pb-4 pl-[23px]">
            {filteredStrategies?.map((strategy) => {
              return (
                <StrategiesListItem
                  key={strategy.id}
                  strategy={strategy}
                  refetchStrategies={refetchStrategies}
                />
              );
            })}
          </div>
        </RollerBar>
      </div>
    </div>
  );
}
