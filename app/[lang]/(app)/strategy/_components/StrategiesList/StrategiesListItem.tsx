'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';

import { useTranslation } from '@/_services/i18n/client';
import {
  GetStrategiesQuery,
  StrategyInvestmentTrend,
  StrategyInvestmentType,
  SupportedExchange,
  useDuplicateStrategyMutation,
  VerifiedStatusLevel,
} from '__generated__/types.app';
import { getDiffDaysInDates } from 'utils';

// components
import { Button } from '@/_components/Button';
import { MiniAreaChart } from '@/_components/Chart/MiniAreaChart';
import { OverlappingPair } from '@/_components/Pair';
import { Popover, PopoverTrigger } from '@/_components/Popover';
import { toaster } from '@/_components/Toast/Toast';
import { Button as AriaButton, Dialog, Menu, MenuItem, MenuTrigger } from 'react-aria-components';
import { DeleteModal } from './DeleteModal';
import { VersionTag } from './VersionTag';

// icons
import { StatefulIcon } from '@/_components/Icon';
import RegisterHover from '@/_components/Icon/assets/RegisterHover.svg';
import RegisterNormal from '@/_components/Icon/assets/RegisterNormal.svg';
import StoreNormal from '@/_components/Icon/assets/StoreNormal.svg';
import StrategyCheckHover from '@/_components/Icon/assets/StrategyCheckHover.svg';
import StrategyCheckNormal from '@/_components/Icon/assets/StrategyCheckNormal.svg';
import StrategyCopyHover from '@/_components/Icon/assets/StrategyCopyHover.svg';
import StrategyCopyNormal from '@/_components/Icon/assets/StrategyCopyNormal.svg';
import StrategyEditHover from '@/_components/Icon/assets/StrategyEditHover.svg';
import StrategyEditNormal from '@/_components/Icon/assets/StrategyEditNormal.svg';
import TrashDisable from '@/_components/Icon/assets/TrashDisable.svg';
import TrashHover from '@/_components/Icon/assets/TrashHover.svg';
import TrashNormal from '@/_components/Icon/assets/TrashNormal.svg';
import VerifiedDisabled from '@/_components/Icon/assets/VerifiedDisabled.svg';
import VerifiedWhite from '@/_components/Icon/assets/VerifiedWhite.svg';
import VerifiedWhiteHover from '@/_components/Icon/assets/VerifiedWhiteHover.svg';
import VerifiedYellow from '@/_components/Icon/assets/VerifiedYellow.svg';
import VerifiedYellowHover from '@/_components/Icon/assets/VerifiedYellowHover.svg';
import ExchangeIcons from '@/_components/Icon/ExchangeIcon';

// assets
import { useRouter } from 'next/navigation';
import RobotIconHover from '../assets/robot-hover.png';
import RobotIconNormal from '../assets/robot.png';

type StrategiesListItemProp = {
  strategy: GetStrategiesQuery['me']['strategies'][number];
  refetchStrategies: () => void;
};

const StrategyTypeMap = {
  [StrategyInvestmentType.Spot]: 'Spot',
  [StrategyInvestmentType.UsdMFutures]: 'USDⓈ-M Futures',
  [StrategyInvestmentType.CoinMFutures]: 'COIN-M Futures',
  unknown: 'Unknown',
};
const StrategyTrendMap = {
  [StrategyInvestmentTrend.Long]: 'Long',
  [StrategyInvestmentTrend.Short]: 'Short',
  [StrategyInvestmentTrend.LongAndShort]: 'Long and Short',
  unknown: 'Unknown',
};

export function StrategiesListItem(props: StrategiesListItemProp) {
  const [isHovered, setIsHovered] = useState(false);
  const [deleteClicked, setDeleteClicked] = useState(false);

  const { t } = useTranslation();

  const ExchangeIcon = ExchangeIcons[props.strategy.baseExchange || SupportedExchange.Binance];

  const [{ data: duplicationRes }, duplicateStrategy] = useDuplicateStrategyMutation();

  const [selectedStatus, setSelectedStatus] = useState({
    // default seq: Live Trading -> Simulating -> Backtest
    isTrading: props.strategy.isTrading,
    isSimulating: !props.strategy.isTrading && props.strategy.isSimulating,
    isBacktesting:
      !props.strategy.isTrading && !props.strategy.isSimulating && props.strategy.isBacktesting,
  });

  const router = useRouter();

  function goToItem(event: React.MouseEvent<HTMLElement>) {
    // To prevent the click event from being triggered by any element other than the HTML element that this handler is attached to
    if (event.target === event.currentTarget) {
      router.push(`./strategy/${props.strategy.id}`);
    }
  }

  const [task, setTask] = useState<
    | GetStrategiesQuery['me']['strategies'][number]['traderTasks'][number]
    | GetStrategiesQuery['me']['strategies'][number]['simulationTasks'][number]
    | GetStrategiesQuery['me']['strategies'][number]['backtestTasks'][number]
    | null
  >(null);
  useEffect(() => {
    if (selectedStatus.isTrading) {
      setTask(props.strategy.traderTasks[0]);
    } else if (selectedStatus.isSimulating) {
      setTask(props.strategy.simulationTasks[0]);
    } else if (selectedStatus.isBacktesting) {
      setTask(props.strategy.backtestTasks[0]);
    } else {
      setTask(null);
    }
  }, [
    selectedStatus,
    props.strategy.backtestTasks,
    props.strategy.simulationTasks,
    props.strategy.traderTasks,
  ]);

  const verifiedStatus =
    props.strategy.verifiedStatus === VerifiedStatusLevel.Yellow
      ? {
          icon: {
            normal: VerifiedYellow,
            hover: VerifiedYellowHover,
            disable: false,
          },
          popup: {
            width: 'w-fit',
            message: t('yellow-verified-message'),
          },
        }
      : props.strategy.verifiedStatus === VerifiedStatusLevel.White
        ? {
            icon: {
              normal: VerifiedWhite,
              hover: VerifiedWhiteHover,
              disable: false,
            },
            popup: {
              width: 'w-80',
              message: t('white-verified-message', { exchange: props.strategy.baseExchange }),
            },
          }
        : {
            icon: {
              normal: VerifiedDisabled,
              hover: VerifiedDisabled,
              disable: VerifiedDisabled,
            },
            popup: {
              width: 'w-80',
              message: t('blue-verified-message', { exchange: props.strategy.baseExchange }),
            },
          };

  function ViewTooltip() {
    const roi = props.strategy.battlefieldStrategy?.rankingTaskPerformance?.roi
      ? `${props.strategy.battlefieldStrategy?.rankingTaskPerformance.roi >= 0 ? '+' : '-'} ${Math.abs(
          props.strategy.battlefieldStrategy?.rankingTaskPerformance.roi * 100,
        ).toFixed(2)} %`
      : 'N/A';

    const tradeDays = getDiffDaysInDates(
      new Date(),
      props.strategy.battlefieldStrategy?.onboardedAt,
    );

    return (
      <div className="flex gap-2.5">
        <div className="w-fit">
          <p>{t('type')}</p>
          <p>{t('trend')} </p>
          <p>{t('exchange')}</p>
          <p>{t('base-quote')} </p>

          {/* battlefieldStrategy */}
          {props.strategy.battlefieldStrategy && (
            <>
              <p>{t('Total Invested Amnt')}</p>
              <p>{t('Realized PNL')}</p>
              <p>{t('roi')}</p>
              <p>{t('Trade Days')}</p>
            </>
          )}
        </div>

        <div className="w-fit">
          <p>{StrategyTypeMap[props.strategy.investmentType || 'unknown']}</p>
          <p>{StrategyTrendMap[props.strategy.investmentTrend || 'unknown']}</p>
          <p>
            {props.strategy.baseExchange &&
              props.strategy.baseExchange.charAt(0).toUpperCase() +
                props.strategy.baseExchange.slice(1).toLowerCase()}
          </p>
          <p>{`${props.strategy.targetCurrency} / ${props.strategy.baseCurrency}`}</p>

          {/* battlefieldStrategy */}
          {props.strategy.battlefieldStrategy && (
            <>
              <p>{`${props.strategy.battlefieldStrategy.liveInvestmentAmount.toLocaleString()} ${props.strategy.baseCurrency}`}</p>
              <p>{`${(
                props.strategy.battlefieldStrategy.totalLaunchedInvestmentAmount *
                props.strategy.battlefieldStrategy.totalTradersRoiRealized
              )
                .toLocaleString('en', { maximumFractionDigits: 2 })
                .replace('-0', '0')} ${props.strategy.baseCurrency}`}</p>
              <p>{roi}</p>
              <p>{tradeDays}</p>
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div
      className={`relative flex h-[92px] min-w-[1306px] cursor-pointer items-center justify-between rounded-[2px] bg-background-6 pl-9 pr-5 hover:shadow-[0_8px_8px_rgba(0,0,0,0.55)]`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={goToItem}
    >
      {/* StrategyVersion */}
      <div className="absolute top-[10px]">
        {props.strategy.version > 1 && <VersionTag version={props.strategy.version} />}
      </div>

      <div
        className="flex h-[35px] w-[268px] grow items-center justify-between pr-8"
        // Make area between buttons clickable
        onClick={goToItem}
      >
        {/* StrategyName */}
        <PopoverTrigger triggerType="hover">
          <AriaButton>
            <div className="flex max-w-[204px] items-center border-b border-dashed border-text-2 hover:border-text-1">
              <p className="truncate text-sm font-semibold text-text-2 hover:text-text-1">
                {props.strategy.name}
              </p>
            </div>
          </AriaButton>

          <Popover showArrow={true} placement="right">
            <Dialog className="w-fit rounded p-2.5 text-xs font-medium text-text-2 shadow outline outline-0">
              <p className="whitespace-pre-wrap">{props.strategy.desc || t('no-desc')}</p>
            </Dialog>
          </Popover>
        </PopoverTrigger>

        {/* Verified Icon */}
        <PopoverTrigger triggerType="hover">
          <AriaButton className="flex items-center justify-center">
            <StatefulIcon
              className="size-[20px]"
              normal={verifiedStatus.icon.normal}
              hover={verifiedStatus.icon.hover}
              disable={verifiedStatus.icon.disable}
              disabled={verifiedStatus.icon.disable}
            />
          </AriaButton>

          <Popover showArrow={true} placement="right" offset={13}>
            <Dialog
              className={`${verifiedStatus.popup.width} rounded p-2.5 text-xs font-medium text-text-2 shadow outline outline-0`}
            >
              <p className="text-xs font-medium">{verifiedStatus.popup.message}</p>
            </Dialog>
          </Popover>
        </PopoverTrigger>
      </div>

      {/* StrategyExchange */}
      <div className="pointer-events-none flex w-[100px] grow items-center justify-center">
        <ExchangeIcon className="size-7" />
      </div>

      {/* StrategyPair */}
      <div className="pointer-events-none flex w-[100px] grow items-center justify-center">
        <OverlappingPair
          base={props.strategy.targetCurrency || 'BTC'}
          quote={props.strategy.baseCurrency || 'USDT'}
        />
      </div>

      {/* StrategyView*/}
      <div
        className="flex w-[100px] grow items-center justify-center"
        // Make area between buttons clickable
        onClick={goToItem}
      >
        <PopoverTrigger triggerType="hover">
          <AriaButton className="flex items-center justify-center">
            <StatefulIcon
              className="h-[23px] w-5"
              normal={StrategyCheckNormal}
              hover={StrategyCheckHover}
            />
          </AriaButton>

          <Popover showArrow={true} placement="right">
            <Dialog className="w-fit rounded p-2.5 text-xs font-medium text-text-2 shadow outline outline-0">
              <ViewTooltip />
            </Dialog>
          </Popover>
        </PopoverTrigger>
      </div>

      {/* Status */}
      <div
        className="flex h-[63px] w-[132px] grow flex-col items-center justify-center"
        onClick={goToItem}
      >
        <div className="w-[104px]">
          {props.strategy.isTrading && (
            <div className="flex items-center gap-[9px]" onClick={goToItem}>
              <div className="relative -top-px size-2 rounded-full bg-primary-1 shadow-[0px_0px_6px_0px_#3F73FF]"></div>
              <span
                className={`${selectedStatus.isTrading ? 'text-text-1' : 'text-text-3'} cursor-pointer text-sm font-medium`}
                onClick={() => {
                  setSelectedStatus({
                    isTrading: true,
                    isSimulating: false,
                    isBacktesting: false,
                  });
                }}
              >
                {t('mapping-status.Live Trading')}
              </span>
            </div>
          )}

          {props.strategy.isSimulating && (
            <div className="flex items-center gap-[9px]" onClick={goToItem}>
              <div className="relative -top-px size-2 rounded-full bg-secondary-0 shadow-[0px_0px_6px_0px_#870B7A]"></div>
              <span
                className={`${selectedStatus.isSimulating ? 'text-text-1' : 'text-text-3'} cursor-pointer text-sm font-medium`}
                onClick={() => {
                  setSelectedStatus({
                    isTrading: false,
                    isSimulating: true,
                    isBacktesting: false,
                  });
                }}
              >
                {t('status.simulating')}
              </span>
            </div>
          )}

          {props.strategy.isBacktesting && (
            <div className="flex items-center gap-[9px]" onClick={goToItem}>
              <div className="relative -top-px size-2 rounded-full bg-secondary-1"></div>
              <span
                className={`${selectedStatus.isBacktesting ? 'text-text-1' : 'text-text-3'} cursor-pointer text-sm font-medium`}
                onClick={() => {
                  setSelectedStatus({
                    isTrading: false,
                    isSimulating: false,
                    isBacktesting: true,
                  });
                }}
              >
                {t('status.backtest')}
              </span>
            </div>
          )}

          {!props.strategy.isTrading &&
            !props.strategy.isSimulating &&
            !props.strategy.isBacktesting && (
              <div className="flex items-center gap-[9px]" onClick={goToItem}>
                <div className="size-2 rounded-full bg-background-1"></div>
                <span className="text-sm font-medium text-background-2">{t('status.none')}</span>
              </div>
            )}
        </div>
      </div>

      {/* Curve */}
      <div
        className="flex h-[63px] w-[180px] grow flex-col items-center justify-between"
        // Make extra area within div clickable
        onClick={goToItem}
      >
        {Object.values(selectedStatus).every((value) => value === false) ? (
          <div className="flex h-full items-center">
            <p className="text-sm font-bold text-text-3">N / A</p>
          </div>
        ) : (
          <>
            <div className="flex h-5 items-center gap-1">
              <span
                className={`${!task?.roi ? '' : task.roi > 0 ? 'text-positive' : 'text-negative'} text-base font-bold`}
              >
                {task?.roi ? (task.roi > 0 ? '+' : '') + (task.roi * 100).toFixed(2) : '+0.00'}%
              </span>
              <span className="text-xs font-medium text-text-3">{`(${t('field.roi')})`}</span>
            </div>

            <MiniAreaChart
              data={task?.profitVector.points.map((item) => ({
                time: item.time,
                value: item.profit,
              }))}
              className="h-[47px] w-[117px]"
              color={
                selectedStatus.isTrading ? 'blue' : selectedStatus.isSimulating ? 'pink' : 'purple'
              }
            />
          </>
        )}
      </div>

      {/* StrategyAction */}
      <div
        className="flex w-[172px] grow items-center justify-center"
        // Make extra area within div clickable
        onClick={goToItem}
      >
        <Button variant="contained" className="h-[30px] w-[91px] text-sm font-medium text-text-1">
          {t('btn.live')}
        </Button>
      </div>

      {/* StrategyOperation */}
      <div className="flex w-[218px] grow items-center justify-center">
        <div
          className="flex max-w-[171px] grow items-center justify-between"
          // Make area between buttons clickable
          onClick={goToItem}
        >
          {/* Edit */}
          <PopoverTrigger triggerType="hover">
            <Link href={`./strategy/${props.strategy.id}`}>
              <AriaButton>
                <div className="flex">
                  <StatefulIcon
                    className="size-[18px]"
                    normal={StrategyEditNormal}
                    hover={StrategyEditHover}
                  />
                </div>
              </AriaButton>
            </Link>

            <Popover showArrow={true} placement="top" offset={16}>
              <Dialog className="w-fit rounded p-2.5 text-xs font-medium text-text-2 shadow outline outline-0">
                {t('Edit')}
              </Dialog>
            </Popover>
          </PopoverTrigger>

          {/* Duplicate */}
          <PopoverTrigger triggerType="hover">
            <AriaButton>
              <div
                className="flex"
                onClick={() =>
                  duplicateStrategy({ strategyId: props.strategy.id })
                    .then((res) => {
                      const duplicatedStrategy = res.data?.duplicateStrategy;
                      if (duplicatedStrategy?.id) {
                        toaster.success({ message: t('successfully-duplicated') });
                        props.refetchStrategies();
                      }
                    })
                    .catch((reason) => toaster.error({ message: t('failed-to-duplicate') }))
                }
              >
                <StatefulIcon
                  className="size-[18px]"
                  normal={StrategyCopyNormal}
                  hover={StrategyCopyHover}
                />
              </div>
            </AriaButton>

            <Popover showArrow={true} placement="top" offset={16}>
              <Dialog className="w-fit rounded p-2.5 text-xs font-medium text-text-2 shadow outline outline-0">
                {t('btn.duplicate')}
              </Dialog>
            </Popover>
          </PopoverTrigger>

          {/* Share? */}
          <MenuTrigger>
            <AriaButton className="outline-none">
              <div className="flex">
                <StatefulIcon
                  className="size-[18px]"
                  normal={RegisterNormal}
                  hover={RegisterHover}
                />
              </div>
            </AriaButton>

            <Popover placement="bottom end" offset={3} crossOffset={-9} className="shadow-none">
              <Menu
                className="w-[276px] rounded-sm border border-background-5 bg-background-7 py-2 text-sm text-text-2 outline-0"
                onAction={(selectedId) => {}}
              >
                <MenuItem
                  id="ca"
                  className="flex items-center gap-2 px-4 py-2 hover:cursor-pointer hover:bg-background-5 focus:outline-0"
                >
                  <StoreNormal className="size-6" />
                  {t('Register to Marketplace CA')}
                </MenuItem>
                <MenuItem
                  id="zke"
                  className="flex items-center gap-2 px-4 py-2 hover:cursor-pointer hover:bg-background-5 focus:outline-0"
                >
                  <StoreNormal className="size-6" />
                  {t('Register to Marketplace ZKE')}
                </MenuItem>
              </Menu>
            </Popover>
          </MenuTrigger>

          {/* Deletion */}
          <PopoverTrigger triggerType="hover">
            <AriaButton>
              <div
                className="flex"
                onClick={() => {
                  !props.strategy.isTrading && setDeleteClicked(true);
                }}
              >
                <StatefulIcon
                  className="size-[18px]"
                  normal={TrashNormal}
                  hover={TrashHover}
                  disable={TrashDisable}
                  disabled={props.strategy.isTrading}
                />
              </div>
            </AriaButton>

            <Popover showArrow={true} placement="top" offset={16}>
              <Dialog className="w-fit rounded p-2.5 text-xs font-medium text-text-2 shadow outline outline-0">
                <p className="max-w-80">
                  {props.strategy.isTrading
                    ? t('Popup-Info.Strategy-Edit.running-live-trade-disabled')
                    : t('btn.delete')}
                </p>
              </Dialog>
            </Popover>
          </PopoverTrigger>
        </div>
      </div>

      {deleteClicked && (
        <DeleteModal
          strategyId={props.strategy.id}
          strategyName={props.strategy.name || ''}
          onClose={() => setDeleteClicked(false)}
          refetchStrategies={props.refetchStrategies}
        />
      )}

      {/* Robot */}
      <Image
        src={isHovered ? RobotIconHover.src : RobotIconNormal.src}
        width={51}
        height={71}
        className="absolute left-[-23px] top-[-7px]"
        alt="robot"
      />
    </div>
  );
}
