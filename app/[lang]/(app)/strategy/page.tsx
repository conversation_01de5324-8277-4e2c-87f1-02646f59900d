import type { Metadata } from 'next';

import { StrategiesList } from './_components/StrategiesList';

export default function Page() {
  return (
    <div className="size-full pl-[37px] pr-[60px] pt-7">
      <StrategiesList />
    </div>
  );
}

// https://nextjs.org/docs/app/building-your-application/optimizing/metadata
export const metadata: Metadata = {
  title: '',
};

/* if the metadata is dynamic
export async function generateMetadata(
  { params, searchParams }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
}
*/
