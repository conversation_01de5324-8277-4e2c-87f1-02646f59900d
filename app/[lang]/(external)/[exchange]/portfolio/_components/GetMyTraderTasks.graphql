query GetMyTraderTasks(
  $first: Int = 15
  $after: String = ""
  $filter: TraderTaskFilterInput = {}
  $reverseOrder: Boolean
) {
  me {
    id
    traderTasksCursor(first: $first, after: $after, filter: $filter, reverseOrder: $reverseOrder) {
      edges {
        node {
          id
          createdAt
          updatedAt
          rangeEnd
          strategyInvestmentType
          strategyInvestmentTrend
          roi
          status
          strategyId
          strategy {
            id
            owner {
              id
              avatarUrl
            }
            name
            investmentType
            investmentTrend
            templateName
            templateSerialNumber
          }
          targetExchangePair
          lockGainRatio
          stopLossRatio
          baseExchange
          autoClosePosition
          exchangeApiKeys {
            comment
            apiKey
          }
          chargingMethod
        }
        cursor
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
}
