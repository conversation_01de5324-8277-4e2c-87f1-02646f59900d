import { <PERSON>ada<PERSON> } from 'next';
import { Montserrat } from 'next/font/google';
import { UrqlWrapper } from './_services/graphql/client';
import { serverSideTranslation } from './_services/i18n/server';

import { ErrorScreen } from '@/_components/ErrorScreen';
import { headers } from 'next/headers';
import { PortalProvider } from './_hook/usePortal';
import { I18nProvider } from './_services/i18n/client';
import { ToastProvider } from './_services/toast';
import { isValidHostname, isValidSubdomain } from './_services/utils/tenant';
import { getTenantConfigBySubdomain } from './_tenant/tenant';
import { ThemeName } from './_theme';
import { Theme } from './_theme/server';
import './globals.css';

export const montserrat = Montserrat({
  subsets: ['latin'],
  display: 'swap',
  weight: ['400', '500', '700'],
});

type RootLayoutProps = {
  params: { lang: string };
};

const exchangeThemeMap = {
  ca: 'ca',
  zke: 'zke',
  xfnh: 'zke',
} as const;

type ExchangeKey = keyof typeof exchangeThemeMap;

const getThemeName = ({ exchange, subdomain }: { exchange?: string; subdomain?: string }) => {
  if (subdomain && isValidSubdomain(subdomain)) {
    // get theme from tenant config
    return getTenantConfigBySubdomain(subdomain).theme;
  }
  if (exchange && (exchange as ExchangeKey) in exchangeThemeMap) {
    // get theme from exchange theme map
    return exchangeThemeMap[exchange as ExchangeKey];
  }
  // default theme
  return 'ca';
};

export default function RootLayout({
  params,
  children,
}: {
  children: React.ReactNode;
} & RootLayoutProps): JSX.Element {
  const hostname = headers().get('host') ?? '';
  const subdomain = hostname.split('.')[0];
  const isHostnameValid = isValidHostname(hostname);
  const exchange = headers().get('exchange') || '';
  const themeName = getThemeName({ exchange, subdomain });

  return (
    <html
      lang={params.lang}
      prefix="og: https://ogp.me/ns#"
      className={montserrat.className}
      id="app"
    >
      <body>
        <Theme themeName={themeName as ThemeName}>
          <div className="h-full bg-background-8 text-warmGray-1">
            <PortalProvider>
              <UrqlWrapper>
                <I18nProvider>
                  <ToastProvider>
                    {children}
                    {/* {!isHostnameValid ? <ErrorScreen statusCode={404} /> : children} */}
                  </ToastProvider>
                </I18nProvider>
              </UrqlWrapper>
            </PortalProvider>
          </div>
        </Theme>
      </body>
    </html>
  );
}

export async function generateMetadata({ params }: RootLayoutProps): Promise<Metadata> {
  const { t } = await serverSideTranslation(params.lang, ['common']);
  return {
    title: t('og.title'),
    description: t('og.description'),
    metadataBase: new URL(
      process.env.CA_ENV === 'production'
        ? 'https://www.crypto-arsenal.io'
        : 'https://staging.crypto-arsenal.io',
    ),
    alternates: {
      canonical: '/',
      languages: {
        'x-default': '/',
        'en-us': '/',
        'zh-tw': '/zh-TW',
        'zh-cn': '/zh-CN',
      },
    },
    openGraph: {
      type: 'website',
      images: [
        {
          url: '/static/og-image-0411.png',
          width: 1280,
          height: 720,
          type: 'image/png',
        },
      ],
      locale: params.lang.replace('-', '_'),
      title: t('og.title'),
      description: t('og.description'),
    },
    icons: {
      icon: [
        { url: '/static/favicon/dark-32.png', sizes: '32x32' },
        { url: '/static/favicon/dark-57.png', sizes: '57x57' },
        { url: '/static/favicon/dark-76.png', sizes: '76x76' },
        { url: '/static/favicon/dark-96.png', sizes: '96x96' },
        { url: '/static/favicon/dark-128.png', sizes: '128x128' },
        { url: '/static/favicon/dark-192.png', sizes: '192x192' },
        { url: '/static/favicon/dark-228.png', sizes: '228x228' },
      ],
      shortcut: { sizes: '196x196', url: '/static/favicon/dark-196.png' },
      apple: [
        { url: '/static/favicon/dark-120.png', sizes: '120x120' },
        { url: '/static/favicon/dark-152.png', sizes: '152x152' },
        { url: '/static/favicon/dark-180.png', sizes: '180x180' },
      ],
    },
  };
}

export function generateStaticParams() {
  return [{ lang: 'en-US' }, { lang: 'zh-TW' }];
}
