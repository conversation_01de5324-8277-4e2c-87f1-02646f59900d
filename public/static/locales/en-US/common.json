{"1-you-have-to-verify-the-strategy-before-on-boarding-it-to-trading-club": "1. You have to verify the strategy before on-boarding it to Trading Club.", "100MB": "100MB.", "2-the-on-boarding-process-will-fail-if-the-registered-form-is-incomplete": "2. The on-boarding process will fail if the registered form is incomplete.", "API": "API", "API Access Token": "API Access Token", "API Key": "API Key", "API Key is required.": "API Key is required.", "API Password is required.": "API Password is required.", "API Secret is required.": "API Secret is required.", "API-Form-API Key": "Key", "API-Form-API Password": "Password", "API-Form-API Secret": "Secret", "API-Form-Exchange": "Exchange", "API-Form-Label": "Label", "API-Form-Suggestion": "To secure your API Key, see ", "API-Form-Suggestion-Tutorial": "How to set up your API Whitelist.", "API-Key-Registration": "API Key Registration", "AUM": "AUM", "Accumulated EC": "Accumulated EC", "Action": "Action", "Add": "Add", "Add New API Key": "Add a New API Key", "Add Source": "Add Source", "Add to Watchlist": "Add to Watchlist", "Add to other watchlists": "Add to other watchlists", "Added to Watchlist": "Added to Watchlist", "Adopted": "Adopted", "Adopted Trading Bots": "Adopted Trading Bots", "After this executed strategy is stopped, if there're open positions, what would you want us to do for you?": "After this executed strategy is stopped, if there're open positions, what would you want us to do for you?", "All": "All", "Amount": "Amount", "Apply": "Apply", "Are you sure to delete": "Are you sure to delete?", "Are you sure to delete all": "Are you sure to delete all?", "Are you sure to delete the comment permanently": "Are you sure to delete the comment permanently?", "Arena": "Arena", "Arena-Registration-Step-1": "Read the criteria for acceptance of submitted strategy.", "Arena-Registration-Step-2": "Configure the following information.", "Arena-Registration-Step-3": "Configure the leverage settings for the strategy in terms of selected trading pairs.", "Arena-Registration-Step-4": "Assign investment amount and relevant constraints for strategy adoption in terms of selected trading pairs.", "Arena-Registration-Step-5": "Assign stop loss, take profit a`nd profit sharing ratio in terms of selected trading pairs.", "Arena-Registration-Step-6": "Describe your strategy introduction.", "Arena-Registration-Step-7": "Confirm or modify the following settings.", "Asset Doughnut Charts": "<PERSON><PERSON>", "Assigned": "Assigned", "Assigned Base Quote": "Assigned Base / Quote", "Assigned Exchange": "Assigned Exchange", "Auto Refresh": "Auto Refresh", "Available": "Available", "Average Holding Days": "Average Holding Days", "Average Number of Trades per Month": "Average Number of Trades per Month", "Avg Number of Trades per Month": "Avg. Number of Trades per Month", "BACKTEST": "Backtest", "Back": "Back", "Backtest": "Backtest", "Backtest-Step1": "Configure the settings and enter the investment amount.", "Backtest-Step2": "Specify the historical backtesting interval.", "Backtesting Parameter Configuration": "Backtesting Parameter Configuration", "Backtesting Period from": "Backtesting Period from", "Backtesting Records of Portfolio Strategy": "Backtesting Records of Portfolio Strategy", "Backtesting Records of Strategy": "Backtesting Records of Strategy", "Balance is not enough": "Balance is not enough", "Base": "Base", "Battlefield": "Battlefield", "Before you drag & drop or": "Before you drag & drop or", "Beginner": "<PERSON><PERSON><PERSON>", "Business Operation Days": "Business Operation Days", "Buy Hold Equity": "Buy & Hold Equity", "CA Wallet": "CA Wallet", "COIN-M Futures": "COIN-M Futures", "COMMENT": "COMMENT", "Cancel": "Cancel", "Change Code Base": "Change Code Base", "Changelog": "Changelog", "Chart": "Chart", "Choose your payment method": "Choose your payment method.", "Choose your subscription plan.": "Choose your subscription plan.", "Clear All": "Clear All", "Clear Filter": "Clear Filter", "Club Settings": "Club Settings", "Club Strategy": "Club Strategy", "Code Base": "Code Base", "Coming Soon": "Coming Soon !", "Comment": "Comment", "Comments": "Comments", "Community": "Community", "Configure stop loss and take profit.": "Configure stop loss and take profit.", "Configure stop loss take profit and the option to handle termination of strategy below": "Configure stop loss, take profit, and the option to handle termination of strategy below.", "Configure the API signals through the following settings": "Configure the API signals through the following settings.", "Configure the TradingView signals through the following settings": "Configure the TradingView signals through the following settings.", "Configure the following settings": "Configure the following settings.", "Configure the settings and enter the investment amount": "Configure the settings and enter the investment amount.", "Contest": "Contest", "Copiers": "Copiers", "Copiers PNL": "Copiers' PnL", "Copiers ROI": "Copiers' ROI", "Copiers Trading Bots": "Copier (Trading Bots)", "Copiers potential total profit has yet to be sold for cash": "Copiers’ potential total profit has yet to be sold for cash.", "Copiers realized ROI": "Copie<PERSON>’ realized ROI", "Copiers realized total profit": "Copiers’ realized total profit", "Copiers total profit that has been sold for cash": "Copiers’ total profit that has been sold for cash.", "Copiers unrealized ROI": "Copiers’ unrealized ROI", "Copiers unrealized total profit": "Copiers’ unrealized total profit", "Copy": "Copy", "Copy Duration": "Copy Duration", "Copy Trade": "Copy Trade", "Create a New Watchlist": "Create a New Watchlist", "Crypto-Arsenal NFT": "Crypto-Arsenal NFT", "Dashboard": "Dashboard", "Date": "Date", "Days": "Days", "Delete All": "Delete All", "Delete Comment": "Delete Comment", "Developer": "Developer", "Docs": "Docs", "Dot": ".", "Downgrade to": "Downgrade to", "Download": "Download", "Duration": "Duration", "EXCHANGE": "EXCHANGE", "Edit": "Edit", "Equity Curve": "Equity Curve", "ErrorText-Adopted-Trading-Bots": "Can't be greater than 10,000", "ErrorText-Blank": "Cannot be blank.", "ErrorText-Fail-Upload": "Failed to upload", "ErrorText-Investment-Amount-At-Least": "Investment amount must be at least", "ErrorText-Investment-Balance-Not-Enough": "Balance not enough", "ErrorText-Investment-Loading": "Loading your assets...", "ErrorText-Leverage": "Leverage value must be between the lower and upper limits.", "ErrorText-Leverage-Lower-Exceed": "Cannot exceed the upper limit.", "ErrorText-Leverage-Lower-Less-Than-1": "Cannot be less than 1.", "ErrorText-Leverage-Upper-Below": "Can not below the lower limit.", "ErrorText-Leverage-Upper-More-Than-125": "Cannot be more than 125.", "ErrorText-Minimum-Investment": "Shall not be less than 150 USDT.", "ErrorText-Order-Value-Less-Than-1": "Cannot be less than 1.", "ErrorText-Order-Value-Less-Than-1-Percent": "Cannot be less than 1%.", "ErrorText-Order-Value-More-Than-100-Percent": "Cannot be more than 100%.", "ErrorText-Percentage-Not-Greater": "Should be greater than 1%", "ErrorText-Percentage-Not-In-Between": "Should be between 1-100%", "ErrorText-Percentage-Not-Less": "Should be less than 50%", "ErrorText-Total-Investment-Less-Than-Min": "Shall not be less than Mini. Invest.", "ErrorText-Total-Investment-Over-15-Digit": "The amount can't be greater than the maximum value of a 15 digit number.", "ErrorText-Upload-Oversize": "Failed to upload. Please make sure the file is pkl and less than 100MB", "ErrorText-Virtual-Investment": "Leverage * Virtual Investment Amount must be greater than 100.", "Excellent": "Excellent", "Exchange": "Exchange", "Exclude": "Exclude", "FILTER": "FILTER", "Fail Copy": "Unable to copy, please try again later!", "Failed to Save!": "Failed to Save!", "Failed to Stop": "Failed to Stop!", "Features": "Features", "Fee": "Fee", "Feedback": "<PERSON><PERSON><PERSON>", "File should be in": "File should be in", "Filter": "Filter", "Final Capital": "Final Capital", "Fixed": "Fixed", "Follow the developer to receive the latest notifications": "Follow the developer to receive the latest notifications", "Followers": "Followers", "For Help": "for help", "Found somethings to be modified": "Found somethings to be modified", "Free": "Free", "Get Started Free": "Get Started Free", "Good": "Good", "Guide": "guide", "Help": "Help", "Help Center": "Help Center", "High Risk": "High Risk", "High risk": "High risk", "How to Start": "How to Start", "I agree and accept our": "I agree and accept our", "I agree and accept the": "I agree and accept the", "If the strategy makes profit": "If the strategy makes profit, {{sharingPercent}}% of the profit will be allocated to the strategy provider after the strategy is terminated.", "Include": "Include", "Individual Competition": "Individual Competition", "Individual Contest": "Individual Contest", "Initial Capital": "Initial Capital", "Initial Investment": "Initial Investment", "Introduction": "Introduction", "Introduction of Portfolio Strategy": "Introduction of Portfolio Strategy", "Introduction of Strategy Developing Team": "Introduction of Strategy Developing Team", "Invalid Name": "Invalid name !", "Invalid Password": "Invalid Password", "Invalid key.": "Invalid key.", "Invalid secret.": "Invalid secret.", "Investment": "Investment", "Investment Amount": "Investment Amount", "Investment amount must be at least": "Investment amount must be at least", "Investment amount must be less than the limitation of this strategy": "Investment amount must be less than the limitation of this strategy.", "Investment amount must be less than the limitation of this strategy.": "Investment amount must be less than the limitation of this strategy.", "It is mandatory to write down the introduction": "It is mandatory to write down the introduction if you want to submit the strategy to Marketplace.", "Its empty in here Strategies are coming here soon": "It’s empty in here! Strategies are coming here soon.", "JS": "JavaScript", "Keep on my own": "Keep on my own", "Keep these options and do nothing": "Keep these options and do nothing!", "Keep these positions and do nothing": "Keep these positions and do nothing!", "Keep these positions and do nothing!": "Keep these positions and do nothing!", "LIVE TRADING": "LIVE TRADING", "LOG IN": "LOG IN", "LONG": "LONG", "LONG AND SHORT": "LONG AND SHORT", "LONG_AND_SHORT": "LONG_AND_SHORT", "LTTMNH": "LTTMNH", "LTTMNH stands for Longest Time To Make New High It is to measure the duration of making new highest profit from the previous in hour": "LTTMNH stands for Longest Time To Make New High. It is to measure the duration of making new highest profit from the previous in hour.", "Label is required.": "Label is required.", "Latest 1 Year": "Latest 1 Year", "Latest 3 Months": "Latest 3 Months", "Latest 30 Days": "Latest 30 Days", "Leverage": "Leverage", "Leverage Settings": "Leverage Settings", "Limit": "Limit", "Live Trading": "Live Trading", "Livetrade-Step1": "Select an effective API key or create a new one.", "Livetrade-Step2": "Configure the following settings.", "Livetrade-Step3": "Set stop loss and take profit point.", "Livetrade-Step4": "Pay attention to the notice below and choose properly.", "Livetrade4-Notice1": "Your live-trading bots will be automatically stopped based on the following three conditions:", "Livetrade4-Notice2": " Manual Stop: If you manually stop them. ", "Livetrade4-Notice3": "Stop Loss or Take Profit: If a pre-determined 'STOP LOSS' or 'TAKE PROFIT' level is reached.", "Livetrade4-Notice4": "Subscription Payment Failure: If the subscription fee cannot be deducted from your CA-Wallet on the next payment date, all of your live-trading bots, except for the quota cap of freemium plan, will be stopped", "Loading": "Loading", "Loading your assets": "Loading your assets...", "Loading your assets...": "Loading your assets...", "Log": "Log", "Logout": "Logout", "Long": "<PERSON>", "Long-Term": "Long-Term", "Low Risk": "Low Risk", "Low risk": "Low risk", "MAX": "MAX", "MDD": "MDD", "MULTI_PAIR": "Multi-pair", "Machine Learning": "Machine Learning", "Manual": "Manual", "Manual Stop: If you manually stop them": "Manual Stop: If you manually stop them.", "Market": "Market", "Market & Equity Curve": "Market & Equity Curve", "Market Data": "Market Data", "Market Place": "Market Place", "Market Settings": "Market Settings", "Marketplace": "Marketplace", "Marketplace Filter": "Marketplace Filter", "Marketplace-Registration-Step-1": "Select Exchange and Trading Pair to be supported.", "Marketplace-Registration-Step-2": "Configure the leverage setting.", "Marketplace-Registration-Step-3": "As<PERSON> suggested stop loss and take profit.", "Marketplace-Registration-Step-4": "Set the pricing for the strategy subscription.", "Marketplace-Registration-Step-5": "Describe your strategy introduction.", "Marketplace-Registration-Step-6": "Confirm or modify the following settings.", "Max DD": "<PERSON>", "Max value": "Max value", "Medium": "Medium", "Medium risk": "Medium risk", "Medium-Term": "Medium-Term", "Membership": "Membership", "Metrics": "Metrics", "Metrics Configuration": "Metrics Configuration", "Metrics Display": "Metrics Display", "Min": {" Investment": "Min. Investment", " Investment Amnt": "Min. Investment Amnt.", " Investment Amount": "Min. Investment Amount"}, "Min value": "Min value", "Minimal Investment Amount": "Minimal Investment Amount", "Minimum": "Minimum", "Mixed": "Mixed", "Moderate": "Moderate", "Moderate Risk": "Moderate Risk", "Moderate risk": "Moderate risk", "Monthly": "Monthly", "Monthly PL": "Monthly P&L", "Most comments": "Most comments", "Most likes": "Most likes", "My Arsenal": "My Arsenal", "My Strategy": "Strategy", "My Tier Strategy": "My Tier Strategy", "Need to Deposit Amount:": "Need to Deposit Amount:", "Net profit or loss of all orders": "Net profit (or loss) of all orders, excluding profit sharing from the followers", "New Strategy": "New Strategy", "Newest first": "Newest first", "Next": "Next", "Notice": "Notice", "Notice:": "Notice:", "Notional": "Notional", "Num of Trades": "Num of Trades", "OK": "OK", "Odds Ratio": "<PERSON>s <PERSON>", "On Board": "On Board", "On Chain Data": "On Chain Data", "Only-Member": "Only-Member", "Oops You havent selected any strategies": "Oops!! You haven’t selected any strategies.", "Or You Can": "or you can", "Other": "Other", "Our Solution": "Our Solution", "Overview": "Overview", "PAIR": "PAIR", "PERFORMANCE PERIOD": "PERFORMANCE PERIOD", "PL": "P&L", "PUBLIC": "PUBLIC", "PYTHON": "Python", "Pair": "Pair", "Payment": "Payment", "Payment Method": "Payment Method", "Payment method": "Payment method", "Pending": "Pending", "Percent": "Percent", "Performance": "Performance", "Performance Metrics": "Performance Metrics", "Performance Period": "Performance Period", "Personal Information": "Personal Information", "Placeholder": {"ml-model-name": "Unique key to access the model"}, "Please": "Please", "Please check your": "Please check your ", "Please note that your open positions will be sold with market price as soon as this exectued strategy is stopped": "Please note that your open positions will be sold with market price as soon as this exectued strategy is stopped.", "Please sell them all with the market price": "Please sell them all with the market price.", "Please sell them all with the market price.": "Please sell them all with the market price.", "Popular": "POPULAR", "Popup-Info": {"Arena-Registration": {"assigned-base": "The base you designed the strategy for.", "assigned-exchange": "The exchange you designed the strategy for.", "assigned-quote": "The quote you designed the strategy for.", "constraints": "Please constrain your strategy adoption in terms of following two options, you should at least select one of them. Note that traders won’t be able to adopt your strategy once either one constraints is reached. If both of constraints are specified, either of which reached makes unavailable for new traders to adopt.", "edit-column": "Click to modify the value and apply to the column.", "leverage": "Leverage is 1 by default for SPOT markets.", "locked-register": "Click the Lock to Enable Register", "min-invest-limit": "Most of exchanges require minimum investment amount for every transaction. The minimum amount may vary due to different attributes of the strategies and targeted markets. In general, we suggest that the amount shall not be less than 150 USD.", "percentage": "In order to guarantee the quality of every admitted strategy in Arena, we spend certain efforts to manage each strategy, making sure its smooth execution at any time, thus Crypto-Arsenal ONLY charges some percentage of received splits from each adopted strategy which gained profit for adopters. For example, when 'Strategy A' made 100 USDT for me, I paid 10 USDT to the developer of 'Strategy A' as a split. If the developer of 'Strategy A' remains Starter Plan user while receiving the split, then Crypto-Arsenal will take 5% of the split out of developer's account, which is 10 x 5% = 0.5 USDT. Please note that Crypto-Arsenal reserves rights to make changes in the future.", "profit-sharing-ratio-percentage": "Please specify the percentage you want to take the cut from anyone who had adopted your strategy and gained profit. Note that more % you want, the less interest traders may adopt. We strongly recommend 7~30% is an appropriate sharing ratio.", "trend": "Please verify or change the trend you designed the strategy for. We will update the BASIC INFO of the strategy if you selected a different trend."}, "Backtest": {"In case you want to change it, you need to terminate all the live trading bots and launch them again with a new leverage": "In case you want to change it, you need to terminate all the live trading bots and launch them again with a new leverage.", "Once launching a live trading bot with specifying a certain leverage to the market, some exchange and trading pair, the following live trading bots for the same market should remain the same leverage ratio": "Once launching a live trading bot with specifying a certain leverage to the market, some exchange and trading pair, the following live trading bots for the same market should remain the same leverage ratio.", "backtest-first-step": "Configure the settings and enter the investment amount.", "backtest-second-step": "Specify the historical backtesting interval.", "backtest-three-step": "Adjust the variable settings for strategy (optional).", "fee": "Fee refers to trading/transaction fee paid to the exchange when buying or selling cryptos. Once the value is set, Crypto-Arsenal will minic the real world scenario where the fee is deducted from your initial investment in every successful transaction according to the different rate exchange charges. In other words, you don&apos;t have to specifically calculate the fee in your strategy code base.", "spread": "Spread is the gap between the bid and the ask prices of the asset (crypto). This is known as a bid-ask spread. You can set the value a little larger in order to mimic the real world live-trading scenario while backtesting your strategy.", "variable-setting": "If there is any variables exposed from strategy code base for modification, then you are able to overwrite value(s) of variable(s) in the column(s) below."}, "Create-Strategy": {"Add a max wait second to ensure opposite position is closed before opening a new one. For instance, set a delay of up to 20 seconds, ensuring the previous position closes before opening a new one": "Add a max wait second to ensure opposite position is closed before opening a new one. For instance, set a delay of up to 20 seconds, ensuring the previous position closes before opening a new one.", "Entry Order Mode ＆ Value": "Entry Order Mode ＆ Value:", "Fixed Base Amount": "Fixed Base Amount:", "Fixed Quote Amount": "Fixed Quote Amount:", "If you want to modify, click the locks and save": "; if you want to modify, click the locks.", "Max Reversal Wait": "Max Reversal Wait:", "Percentage of Balance at No Position": "Percentage of Balance at No Position:", "Percentage of Balance with Compounding": "Percentage of Balance with Compounding:", "Percentage of Initial Balance Only": "Percentage of Initial Balance Only:", "Place orders with base asset amount based on TradingView strategy&apos;s Order Size": "Place orders with base asset amount based on TradingView strategy's Order Size.", "Please copy and paste messages to": "Please copy and paste messages to", "Strategy Order Size": "Strategy Order Size:", "Strategy Percentage with Fixed Capital": "Strategy Percentage with Fixed Capital:", "Trade a fixed base asset amount (entry value). E.g., an entry value of 1ETH opens a position worth 1ETH": "Trade a fixed base asset amount (entry value). E.g., an entry value of 1ETH opens a position worth 1ETH.", "Trade a fixed quote amount (entry value). E.g., an entry value of 100U opens a position worth 100U": "Trade a fixed quote amount (entry value). E.g., an entry value of 100U opens a position worth 100U.", "Trade a percentage (entry value) of your balance, including profits. E.g., with 100U, a 10% trade uses 10U, and the next 10% trade uses 9U from the remaining 90U": "Trade a percentage (entry value) of your balance, including profits. E.g., with 100U, a 10% trade uses 10U, and the next 10% trade uses 9U from the remaining 90U.", "Trade a percentage (entry value) of your initial balance, excluding profits. E.g., with 100U, even if it grows to 130U, a 10% trade uses 10U, based on the initial 100U": "Trade a percentage (entry value) of your initial balance, excluding profits. E.g., with 100U, even if it grows to 130U, a 10% trade uses 10U, based on the initial 100U.", "Use a fixed capital amount (entry value) to calculate the investment percentage. This could be the equity of your TradingView strategy or a fixed investment amount": "Use a fixed capital amount (entry value) to calculate the investment percentage. This could be the equity of your TradingView strategy or a fixed investment amount.", "When adding to a position, use a percentage (entry value) of your total balance when no position is open. E.g., with 100U (no position), entering 10% and another 20% uses 10U and 20U": "When adding to a position, use a percentage (entry value) of your total balance when no position is open. E.g., with 100U (no position), entering 10% and another 20% uses 10U and 20U.", "api-connector-name": "This would be how you identify signal", "api-first-step": "Please copy and paste messages for API Trading; if you want to modify, click the locks.", "leverage-futures": "The leverage can be adjusted between 1 and 125.", "leverage-spot": "Leverage is 1 by default for SPOT markets.", "ml-first-step": "Please copy and paste the message to access the model in Strategy IDE later; if you want to modify, click the locks.", "tv-connector-name": "This would be how you identify signal", "tv-first-step-link": "TradingView"}, "Live-Trade": {"leverage": "Once launching a live trading bot with specifying a certain leverage to the market, some exchange and trading pair, the following live trading bots for the same market should remain the same leverage ratio. In case you want to change it, you need to terminate all the live trading bots and launch them again with a new leverage.", "live-trade-first-step": "Select an effective API key or create a new one.", "live-trade-fourth-step": "Pay attention to the notice below and choose properly.", "live-trade-second-step": "Investment Amount", "live-trade-third-step": "Set stop loss and take profit point."}, "Manage-Strategy": {"abort": "Abort", "aborted-message": "Aborted strategy can’t be resumed, you need to re-submit it to the arena.", "live-adoptor-count": "If still have an adopter using this strategy, the strategy can’t be off-boarded.", "lock": "Lock", "offboard": "Off Board", "rejected-message": "The strategy didn’t pass 3 out of 5 test cases. Please re-submit it to arena.", "unlock": "Unlock"}, "Registry-Action": {"Please let us know the reason to abort registration by checking the boxes below and then clicking Submit button on the right": "Please let us know the reason to abort registration by checking the boxes below and then clicking Submit button on the right.", "Please let us know the reason to lock down the strategy by checking the boxes below and then clicking Submit button on the right": "Please let us know the reason to lock down the strategy by checking the boxes below and then clicking Submit button on the right.", "Please let us know the reason to offboard the strategy by checking the boxes below and then clicking Submit button on the right": "Please let us know the reason to offboard the strategy by checking the boxes below and then clicking Submit button on the right.", "When verifying your strategy, you can terminate registration at any time": "When verifying your strategy, you can terminate registration at any time.", "You can lock down the onboarded strategy at any time. Whenever the strategy is being locked down, it cannot be adopted by any NEW traders; however, for traders who had already adopted it, the strategy keeps running for live trading": "You can lock down the onboarded strategy at any time. Whenever the strategy is being locked down, it cannot be adopted by any NEW traders; however, for traders who had already adopted it, the strategy keeps running for live trading.", "You can offboard the onboarded strategy at any time as long as no one use it": "You can offboard the onboarded strategy at any time as long as no one use it."}, "Simulation": {"In case you want to change it, you need to terminate all the live trading bots and launch them again with a new leverage": "In case you want to change it, you need to terminate all the live trading bots and launch them again with a new leverage.", "Once launching a live trading bot with specifying a certain leverage to the market, some exchange and trading pair, the following live trading bots for the same market should remain the same leverage ratio": "Once launching a live trading bot with specifying a certain leverage to the market, some exchange and trading pair, the following live trading bots for the same market should remain the same leverage ratio.", "fee": "Fee refers to trading/transaction fee paid to the exchange when buying or selling cryptos. Once the value is set, Crypto-Arsenal will minic the real world scenario where the fee is deducted from your initial investment in every successful transaction according to the different rate exchange charges. In other words, you don&apos;t have to specifically calculate the fee in your strategy code base.", "spread": "Spread is the gap between the bid and the ask prices of the asset (crypto). This is known as a bid-ask spread. You can set the value a little larger in order to mimic the real world live-trading scenario while backtesting your strategy."}, "Strategy-Edit": {"code-disabled": "Disabled, as you had already submitted the strategy to Strategy Competition.", "fee": "Fee refers to trading/transaction fee paid to the exchange when buying or selling cryptos. Once the value is set, Crypto-Arsenal will minic the real world scenario where the fee is deducted from your initial investment in every successful transaction according to the different rate exchange charges. In other words, you don't have to specifically calculate the fee in your strategy code base.", "mdd": "MDD stands for &apos;Maximum Draw Down. it is a measure of an asset&apos;s largest price drop from a peak to a trough, before a new peak is attained. MDD is an indicator of downside risk over a specified time period. MDD = (Trough Value - Peak Value) / Peak Value Example: Assume an investment portfolio has an initial value of $500,000. The portfolio increases to $750,000 over a period of time, before plunging to $400,000 in a ferocious bear market. It then rebounds to $600,000, before dropping again to $350,000. Subsequently, it more than doubles to $800,000. MDD in this case is ($350,000 - $750,000) / $750,000 = -53.33% The initial peak of $750,000 is used in the MDD calculation. The interim peak of $600,000 is not used, since it does not represent a new high. The new peak of $800,000 is also not used since the original drawdown began from the $750,000 peak. The MDD calculation takes into consideration the lowest portfolio value ($350,000 in this case) before a new peak is made, and not just the first drop to $400,000.", "new-high-longest-time": "LTTMNH stands for Longest Time To Make New High. It is to measure the duration of making new highest profit from the previous in hour.", "new-label": "You can always give a new label to record the backtesting parameters that this strategy adopted for future usage.", "no-data-existed": "There is no data existed.", "no-stopped-live-trades": "You haven’t had any stopped live trades.", "no-stopped-simulation": "You haven’t had any stopped simulations.", "not-working-backtest": "Note that backtesting will not work for TradingView or API signals", "odds-ratio": "Odds Ratio is also known as Profit/Loss Ratio. Odds Ratio is the average profit on winning trades divided by the average loss on losing trades over a specified time period. Odds Ratio = (Total Profit / Number of Winning Trades) / (Total Loss / Number of Losing Trades) Odds Ratio measures how a trading strategy or system is performing. Obviously, the higher the ratio the better. Many trading books call for at least a 2:1 ratio. For example, if a system had a winning average of $750 per trade and an average loss over the same time of $250 per trade, then odds ratio would be 3:1. A consistently solid odds ratio can encourage a trader to leverage bets on the same strategy in an attempt to generate greater absolute profits.", "profit-factor": "Profit Factor is defined as the gross profit divided by the gross loss (including transaction fee) for the entire trading period. Profit Factor (PF) = Gross Profit / |Gross Loss| PF metric helps traders analyze the degree to which wins are greater than losses. Usually, PF is expected to be greater than 1.5. For PF less than 1, it would be a losing system; however, the trading system may be considered over optimized while PF much larger than 1.", "rename": "Double Click to rename.", "return/dd": "The concept of the ratio is similar to <PERSON> Ratio.", "running-live-trade-disabled": "As the strategy is being live traded, it can not be deleted.", "sharpe-ratio": "Sharpe Ratio is used to help investors understand the return of an investment compared to its risk. It is the average return earned in excess of the risk-free rate per unit of volatility or total risk. Volatility is a measure of the price fluctuations of an asset or portfolio. Sharpe Ratio = (Return of Portfolio - Risk-Free Rate) / Standard Deviation of the portfoli&apos;s excess return The Sharpe ratio is one of the most widely used methods for calculating risk-adjusted return. Modern Portfolio Theory (MPT) states that adding assets to a diversified portfolio that has low correlations can decrease portfolio risk without sacrificing return.", "spread": "Spread is the gap between the bid and the ask prices of the asset (crypto). This is known as a bid-ask spread. You can set the value a little larger in order to mimic the real world live-trading scenario while backtesting your strategy.", "unable-rename": "Unable to edit strategy name as the strategy is registered.", "unrealized-roi": "Unrealized ROI refers to the ROI of unsold open positions. It will be updated according to the way you pre-determined to handle whenever the trading bot is terminated. If you determined to sell all positions with market price, unrealized ROI becomes to 0% unless the amount is too small to sell; if you determined to hold all positions, unrealized ROI is calculated and freezed based on the market price of the moment while the trading bot is stopped.", "win-rate": "The number of winning trades divided by the total number of trades. For example, a trader who won on 75 of 100 trades would have a 75% win rate."}}, "Portfolio": "Portfolio", "Portfolio Filter": "Portfolio Filter", "Pricing": "Pricing", "Privacy Notice": "Privacy Notice", "Private": "Private", "Pro": "Pro", "Profile": "Profile", "Profit Factor": "Profit Factor", "Profit Sharing": "Profit Sharing", "Profit Sharing Ratio": "Profit Sharing Ratio", "Public": "Public", "QUANT_TEXT": "QUANT", "Quote": "Quote", "ROI": "ROI", "ROI of the investment that has been sold": "ROI (%) of the investment that has been sold.", "ROI of the investment that has not yet been sold": "ROI (%) of the investment that has not yet been sold. Unrealized ROI (%) = [total market value - total cost] / total cost * 100%", "RankList-item-disabled": "You had already launched a FUTURES strategy with the same pair. Please stop it before launching a new strategy.", "Rating": "Rating", "Reach Out": "reach out", "Realized": "Realized", "Realized + Unrealized ROI": "Realized + Unrealized ROI", "Realized PNL": "Realized Copier's PNL", "Realized ROI Gain": "Realized ROI / Gain", "Recommended Copy Trading Days": "Recommended Copy Trading Days.", "Record": "Record", "Register Strategy to Arena": "Register Strategy to Arena", "Register to Arena": "Register to Arena", "Register to Competition": "Register to Competition", "Register to Marketplace CA": "Register to Marketplace - CA", "Register to Marketplace ZKE": "Register to Marketplace - ZKE", "Register to Trading Club": "Register to Trading Club", "Registered to": "Registered to", "Registration": "Register Strategy to Battlefield", "Registration Progress": "Registration Progress", "Registration@CSIE5434": "Register Strategy to CSIE5434 Competition", "Registration@CSIE5434Team": "Register Strategy to CSIE5434 Team Competition", "Registration@Maicoin": "Register Strategy to MaiCoin Competition", "Reset": "Reset", "Return DD": "Return / DD", "Risk": "Risk", "Risk Scoring": "Risk Scoring", "Risk scoring depends on the stability of the strategy": "Risk scoring depends on the stability of the strategy.", "Risk scoring may be changing due to market fluctuations": "Risk scoring may be changing due to market fluctuations.", "Roadmap": "Roadmap", "S / T / P.S. (%)": "S / T / P.S. (%)", "SHARPE": "SHARPE", "SHORT": "SHORT", "SIGN UP": "SIGN UP", "SIMULATING": "SIMULATING", "SIMULATION": "Simulation", "SINGLE_PAIR": "Single-pair", "SORT BY": "SORT BY", "SPOT": "SPOT", "STATUS": "STATUS", "STOPPED": "STOPPED", "STRATEGY AGGREGATOR": "STRATEGY AGGREGATOR", "Save": "Save", "Save Parameter": "Save {{isBacktest}}Parameter", "Search": "Search", "Select": "Select", "Select 2-4 metrics": "Select 2-4 metrics:", "Select API Key": "Select API Key", "Select All": "Select All", "Select Payment Cryptocurrency": "Select Payment Cryptocurrency", "Select Payment Method": "Select Payment Method", "Select the market the signals will be executed for trading": "Select the market, the signals will be executed for trading.", "Select the method to connect signals": "Select the method to connect signals.", "Sell with market price": "Sell with the market price", "Sell with mrkt price": "Sell w. mrkt price", "Sentiment Analytic Data": "Sentiment Analytic Data", "Sharpe Ratio": "<PERSON>", "Sharpe Ratio measures the risk-adjusted return of trading strategies A higher value indicates better risk-adjusted performance": "Sharpe Ratio measures the risk-adjusted return of trading strategies. A higher value indicates better risk-adjusted performance.", "Short": "Short", "Short-Term": "Short-Term", "Show Less": "Show Less", "Show More": "Show More", "Simulation": "Simulation", "Since Launching": "Since Launching", "Specify 2 to 4 metrics to be displayed": "Specify 2 to 4 metrics to be displayed.", "Specify minimum value": "Specify minimum value", "Specify the historical backtesting interval": "Specify the historical backtesting interval.", "Splitting Calculation ( Ratio:": "Splitting Calculation ( Ratio:", "Spread": "Spread", "Status": "Status", "Stop Loss": "Stop Loss", "Stop Loss or Take Profit": "Stop Loss or Take Profit: If a pre-determined 'STOP LOSS' or 'TAKE PROFIT' level is reached.", "Stop trading if the accumulated loss reaches to": "Stop trading if the accumulated loss reaches:", "Stop trading if the accumulated profit reaches to": "Stop trading if the accumulated profit reaches:", "Strategies": "Strategies", "Strategy": "Strategy", "Strategy Aggregator": "Strategy Aggregator", "Strategy Arsenal": "Strategy Arsenal", "Strategy Code": "Strategy Code", "Strategy Information in Arena": "Strategy Information in Arena", "Strategy Introduction": "Strategy Introduction", "Strategy Label": "Strategy Label", "Strategy Name": "Strategy Name", "Subscription Payment Failure: If the subscription fee cannot be deducted from your CA-Wallet on the next payment date, all of your live-trading bots, except for the quota cap of freemium plan, will be stopped": "Subscription Payment Failure: If the subscription fee cannot be deducted from your CA-Wallet on the next payment date, all of your live-trading bots, except for the quota cap of freemium plan, will be stopped.", "Subscription or Profit Sharing": "Subscription or Profit Sharing", "Successfully Copied": "Successfully Copied!", "Successfully Deleted": "Successfully Deleted!", "Successfully Download": "Successfully Download!", "Successfully Renamed": "Successfully renamed !", "Successfully Stopped": "Successfully Stopped!", "Successfully added to": "Successfully added to", "Successfully created": "Successfully created", "Successfully saved!": "Successfully saved!", "Suggested": "Suggested", "Suggested Leverage": "Suggested Leverage", "Suggested SL": "Suggested SL", "Suggested SLTP": "Suggested SL / TP", "Suggested TP": "Suggested TP", "Supported Base Quote": "Supported Base / Quote", "TRADER": "Live Trade", "TRADER_TEXT": "TRADER", "TREND": "TREND", "TYPE": "TYPE", "Take Profit": "Take Profit", "Team Competition": "Team Competition", "Team Contest": "Team Contest", "Team Register": "Team Registry", "Technical Indicator": "Technical Indicator", "Term of Service": "Terms of Service", "Terms of Service & Privacy Notice": "Terms of Service & Privacy Notice", "The cumulative AUM managed by the strategy": "The cumulative AUM managed by the strategy.", "The cumulative investment made by all the adopters": "The cumulative investment made by all the adopters.", "The cumulative investment made by all the adopters The developer limits the total amount that can be invested into a strategy": "The cumulative investment made by all the adopters. The developer limits the total amount that can be invested into a strategy.", "The minimal investment amount that the strategy requests adopters to invest": "The minimal investment amount that the strategy requests adopters to invest.", "The monthly recurring subscription fee will be automatically deducted from your trading account as soon as the strategy is launched until you cancel the subscription manually": "The monthly recurring subscription fee will be automatically deducted from your trading account as soon as the strategy is launched until you cancel the subscription manually.", "The monthly recurring subscription fee will be automatically deducted from your trading account as soon as the strategy is launched.": "The monthly recurring subscription fee will be automatically deducted from your trading account as soon as the strategy is launched.", "The number of copiers adopting the strategy": "The number of copiers adopting the strategy.", "The number of days a live trading bot has been running": "The number of days a live trading bot has been running.", "The number of trading bots adopting this strategy": "The number of trading bots adopting this strategy.", "The number of users adopting this strategy Developers limit the number of users that can live trade this bot simultaneously": "The number of users adopting this strategy. Developers limit the number of users that can live trade this bot simultaneously.", "The number of winning trades divided by the total number of trades": "The number of winning trades divided by the total number of trades. For example, a trader who won on 75 of 100 trades would have a 75% win rate.", "The ratio that strategy developers ask when the strategy made profit": "The ratio that strategy developers ask when the strategy made profit.", "The total time in hour when reaching the 21st trade": "The total time in hour when reaching the 21st trade.", "There are no onboard strategies exist": "There are no onboard strategies exist.", "There is NO address found": "There is NO address found.", "There is NO data found": "There is NO data found.", "There is NO record found": "There is NO record found!", "There is NO withdrawal record found!": "There is NO withdrawal record found!", "There is no data existed": "There is no data existed.", "This Label already existed.": "This Label already existed.", "This strategy has already been added to all watchlists": "This strategy has already been added to all watchlists", "This watchlist already existed": "This watchlist already existed.", "Tier Management": "Tier Management", "Time": "Time", "Time Period": "Time Period", "Time to the 21st Trades": "Time to the 21st Trades", "Timeline": "Timeline", "Tooltip-Detail-Adopted": "The number of users adopting this strategy. Developers limit the number of users that can live trade this bot simultaneously.", "Tooltip-Detail-Investment": "The cumulative investment made by all the adopters. The developer limits the total amount that can be invested into a strategy.", "Tooltip-Detail-LTTMNH": "stands for Longest Time To Make New High. It is to measure the duration of making new highest profit from the previous in hour", "Tooltip-Detail-MDD": "MDD stands for &apos;Maximum Draw Down. it is a measure of an asset&apos;s largest price drop from a peak to a trough, before a new peak is attained. MDD is an indicator of downside risk over a specified time period. MDD = (Trough Value - Peak Value) / Peak Value Example: Assume an investment portfolio has an initial value of $500,000. The portfolio increases to $750,000 over a period of time, before plunging to $400,000 in a ferocious bear market. It then rebounds to $600,000, before dropping again to $350,000. Subsequently, it more than doubles to $800,000. MDD in this case is ($350,000 - $750,000) / $750,000 = -53.33% The initial peak of $750,000 is used in the MDD calculation. The interim peak of $600,000 is not used, since it does not represent a new high. The new peak of $800,000 is also not used since the original drawdown began from the $750,000 peak. The MDD calculation takes into consideration the lowest portfolio value ($350,000 in this case) before a new peak is made, and not just the first drop to $400,000.", "Tooltip-Detail-Odds Ratio": "Odds Ratio is also known as Profit/Loss Ratio. Odds Ratio is the average profit on winning trades divided by the average loss on losing trades over a specified time period. Odds Ratio = (Total Profit / Number of Winning Trades) / (Total Loss / Number of Losing Trades) Odds Ratio measures how a trading strategy or system is performing. Obviously, the higher the ratio the better. Many trading books call for at least a 2:1 ratio. For example, if a system had a winning average of $750 per trade and an average loss over the same time of $250 per trade, then odds ratio would be 3:1. A consistently solid odds ratio can encourage a trader to leverage bets on the same strategy in an attempt to generate greater absolute profits.", "Tooltip-Detail-Profit Factor": "Profit Factor is defined as the gross profit divided by the gross loss (including transaction fee) for the entire trading period. Profit Factor (PF) = Gross Profit / |Gross Loss| PF metric helps traders analyze the degree to which wins are greater than losses. Usually, PF is expected to be greater than 1.5. For PF less than 1, it would be a losing system; however, the trading system may be considered over optimized while PF much larger than 1.", "Tooltip-Detail-Return DD": "The concept of the ratio is similar to <PERSON> Ratio.", "Tooltip-Detail-Sharpe": "Sharpe Ratio is used to help investors understand the return of an investment compared to its risk. It is the average return earned in excess of the risk-free rate per unit of volatility or total risk. Volatility is a measure of the price fluctuations of an asset or portfolio. Sharpe Ratio = (Return of Portfolio - Risk-Free Rate) / Standard Deviation of the portfoli&apos;s excess return The Sharpe ratio is one of the most widely used methods for calculating risk-adjusted return. Modern Portfolio Theory (MPT) states that adding assets to a diversified portfolio that has low correlations can decrease portfolio risk without sacrificing return.", "Tooltip-Detail-Unrealized ROI": "Unrealized ROI refers to the ROI of unsold open positions. It will be updated according to the way you pre-determined to handle whenever the trading bot is terminated. If you determined to sell all positions with market price, unrealized ROI becomes to 0% unless the amount is too small to sell; if you determined to hold all positions, unrealized ROI is calculated and freezed based on the market price of the moment while the trading bot is stopped.", "Tooltip-Detail-Win Rate": "The number of winning trades divided by the total number of trades. For example, a trader who won on 75 of 100 trades would have a 75% win rate.", "Tooltip-Fee": "Fee refers to trading/transaction fee paid to the exchange when buying or selling cryptos. Once the value is set, Crypto-Arsenal will minic the real world scenario where the fee is deducted from your initial investment in every successful transaction according to the different rate exchange charges. In other words, you don't have to specifically calculate the fee in your strategy code base.", "Tooltip-Leverage-1": "Leverage is 1 by default for SPOT markets.", "Tooltip-Leverage-Suggested": "When a trader is launching this strategy, the default leverage is the Suggested Value set by quant. If you switch to Manual, the leverage can be adjusted between Lower and Upper Limit.", "Tooltip-Livetrade2-Leverage1": "The leverage cannot be adjusted because it cannot be different from other live-trading bot.", "Tooltip-Livetrade2-Leverage2": "Once launching a live trading bot with specifying a certain leverage to the market, some exchange and trading pair, the following live trading bots for the same market should remain the same leverage ratio. In case you want to change it, you need to terminate all the live trading bots and launch them again with a new leverage.", "Tooltip-Livetrade2-Suggested": "When a trader is launching this strategy, the default leverage is the Suggested Value set by quant. If you switch to Munual, the leverage can be adjusted between Lower Limit and Upper Limit.", "Tooltip-Livetrade2-Total Invest Amount": "The cumulative investment made by all the adopters. The developer limits the total amount that can be invested into a strategy.", "Tooltip-Manual": "The default values of ‘Stop Loss’ and ‘Take Profit’ are suggested by strategy developer, you can change them by toggling the button on the left to <PERSON>.\nHowever, we strongly recommend to remain suggested settings.", "Tooltip-Spread": "Spread is the gap between the bid and the ask prices of the asset (crypto). This is known as a bid-ask spread. You can set the value a little larger in order to mimic the real world live-trading scenario while backtesting your strategy.", "Total Invested": "Total Invested", "Total Invested Amnt": "Total Invested Amnt.", "Total Invested Amount": "Total Invested Amount", "Total Investment": "Total Investment", "Trade Days": "Trade Days", "Trade-By-Trade": "Trade By Trade", "Trading": "Trading", "Trading Bots": "Trading Bots", "Trading Bots Launched": "Trading Bots Launched", "Trading Club": "Trading Club", "Trading Source": "Trading Source", "Trading trend changed": "Trading trend changed'", "TradingView": "TradingView", "Training Yard": "Training Yard", "Trend": "Trend", "Type": "Type", "USDⓈ-M Futures": "USDⓈ-M Futures", "Under Construction": "Under Construction", "Unfollow this developer": "Unfollow this developer", "Unlock": "Unlock", "Unrealized": "Unrealized", "Unrealized ROI": "Unrealized ROI", "Upgrade to": "Upgrade to", "Use binary code": "Use binary code", "Use my": "Use my", "User Stopped": "User Stopped", "Verifying": "verifying", "Version": "Version", "View": "View", "Virtual Investment Amnt": "Virtual Investment Amnt.", "Virtual Investment Amount": "Virtual Investment Amount", "Watchlist": "Watchlist", "When submitting a strategy, quant will select the exchanges and trading pairs. Only the API Keys of the selected exchanges will be enabled for use, while the others will be disabled": "When submitting a strategy, quant will select the exchanges and trading pairs. Only the API Keys of the selected exchanges will be enabled for use, while the others will be disabled.", "Win Rate": "Win Rate", "Yearly": "Yearly", "You can use the existing base": "You can use the existing {{base}} in your Spot wallet as the initial investment. When the position is not yet established and the first selling point is reached, the {{base}} will be sold.", "You can use the existing base in your Spot wallet as the initial investment": "You can use the existing {{base}} in your Spot wallet as the initial investment. When the position is not yet established and the first selling point is reached, the {{base}} will be sold.", "You cant add your own strategy into watchlist": "You can’t add your own strategy into watchlist.", "You have selected the maximum number of 4 metrics": "You have selected the maximum number of 4 metrics. Please uncheck some metrics and then select new ones.", "You havent had any stopped simulations": "You haven’t had any stopped simulations.", "You must agree to the API key authorization or complete the KYC first.": "You must agree to the API key authorization or complete the KYC first.", "You must log in first !": "You must log in first !", "You should select at least 2 metrics": "You should select at least 2 metrics.", "You should select at most 4 metrics": "You should select at most 4 metrics.", "You trained the model with": "You trained the model with", "Your live-trading bots will be automatically stopped based on the following three conditions": "Your live-trading bots will be automatically stopped based on the following three conditions:", "about": "About", "accept-term-of-use-and-refund-policy": "I accept the <2>Term of Use</2> and agree to the <6>Refund Policy</6> of Crypto-Arsenal.", "acceptance-highlight": "Arena Strategy Review Standard", "acceptance-label-1": "I accept and agree the", "acceptance-label-2": "of Crypto-Arsenal.", "access": "Access", "access-0": "ACCESS", "accumulated-ec": "Accumulated EC", "acknowledgment-message": "I have followed the", "add-new-api-key": "Add New API Key", "add-new-strategy": "Add New Strategy", "add-strategy-to-trading-club": "Add Strategy to Trading Club", "address": "Address", "address-book": "Address Book", "address-duplicated": "Address duplicated", "address-invalid": "Address invalid", "address-label": "Address Label", "address-management": "Address Management", "admin": "Admin", "admins": "Admins", "advanced-setting": "Advanced Setting", "after-this-executed-strategy-is-stopped-if-there-and-apos-re-open-positions-what-would-you-want-us-to-do-for-you": "After this executed strategy is stopped, if there&apos;re open positions, what would you want us to do for you?", "alert-settings": "<PERSON><PERSON>", "amount": "Amount", "an-error-has-occured-please-try-again-later": "An error has occured! Please try again later.", "and": "and", "and deposit your trading capital": "and deposit your trading capital. When done, come back here and click the refresh icon.", "anyone-can-submit-your-strategies-to-strategy-arena-as-long-as-they-are-qualified-according-to-our-criteria-for-admitted-strategy-in-arena": "Anyone can submit your strategies to Strategy Arena as long as they are qualified according to our criteria for admitted strategy in Arena.", "api key service is currently undergoing maintenance": "api key service is currently undergoing maintenance.", "api-acknowledgment-message": "and copied messages for API Trading.", "api-description": "Trade with API alert webhook", "api-key-label": "API Key Label", "api-key-management": "API Key Management", "api-key-registered": "API Key Registered", "applications": "Applications", "apply": "Apply", "are-you-sure-to-delete": "Are you sure to delete?", "are-you-sure-to-stop-live-trade": "Are you sure to stop Live Trade?", "are-you-sure-to-stop-task-name-live-trade": "Are you sure to stop <highlight>{{taskName}}</highlight> ?", "arena-attention": "Attention: Arena below only ranks by ROI and doesn't consider the inherent risk. We strongly suggest you to filter out strategies which best suit your preferences or risk tolerance, then add them to your watchlist for further verification via backtesting, simulation before launching them for live-trading.", "as initial investment": "as initial investment", "as-long-as-you-and-apos-re-done-with-kyc-to-lv2-you-are-able-to-connect-and-access-to-your-accounts-in-your-favorite-exchanges-please-click-and-apos-manage-and-apos-button-to-provide-us-api-key-and-api-secret-before-you-are-launching-a-trading-bot-for-live-trading": "As long as you&apos;re done with KYC to LV2, you are able to connect and access to your accounts in your favorite exchanges. Please click &apos;Manage&apos; button to provide us API key and API secret before you are launching a trading bot for live-trading.", "as-long-as-you-and-apos-re-done-with-kyc-to-lv2-you-are-freely-given-a-personal-ca-wallet-into-which-you-are-able-to-transfer-cryptos-currently-usds-ca-wallet-is-used-to-pay-your-subscription-fee-pay-or-receive-splits": "As long as you&apos;re done with KYC to LV2, you are freely given a personal CA-Wallet into which you are able to transfer cryptos, currently USDⓈ. CA-Wallet is used to pay your subscription fee, pay or receive splits.", "asset": "<PERSON><PERSON>", "asset-doughnut-charts": "<PERSON><PERSON>", "assign-investment-amount-for-the-strategy": "Assign investment Amount for the strategy.", "assign-stop-loss-take-profit-and-profit-sharing-ratio": "Assign stop loss, take profit and profit sharing ratio.", "assign-your-strategy-access-permission": "Assign your strategy access permission.", "automate-tradingview-indicators-and-strategies-with-alert-webhook": "Automate TradingView indicators and strategies with alert WebHook.", "available-asset-may-be-different-from-your-total-asset-if-you-have-unprocessed-withdrawals-or-running-brokers-that-lock-asset-for-profit-splits": "Available asset may be different from your total asset, if you have unprocessed withdrawals or running brokers that lock asset for profit splits.", "average-roi": "Average ROI", "backtest-executed": "Backtest Executed", "backtest-limit-message": "The number of backtest has reached the limit of your subscription.", "backtesting-month": "Backtesting / Month", "backtests-are-not-available-for-this-strategy": "Backtests are not available for this strategy.", "base-quote": "Base / Quote", "base/quote-of-parameterized-strategy-is-adjustable": "Base/Quote of parameterized strategy is adjustable.", "best-roi": "Best ROI", "blog": "Blog", "blue-verified-message": "This strategy has not been live traded before, and the simulated trading period has not exceeded one month.", "btn": {"Recopy": "Recopy", "Restart": "<PERSON><PERSON>", "add": "Add", "back": "Back", "backtest": "Backtest", "cancel": "Cancel", "change": "Change", "clear-filter": "Clear Filter", "close": "Close", "confirm": "Confirm", "continue": "Continue", "copy": "Copy Strategy", "create": "Create", "delete": "Delete", "delete-all": "Delete All", "disable": "Disable", "discard-and-leave": "Don't Save", "duplicate": "Duplicate", "edit": "Edit", "enable": "Enable", "error-go-back": "GO BACK", "live": "Live Trade", "manage": "Manage", "new": "New Strategy", "next": "Next", "no": "No", "ok": "OK", "overwrite": "Overwrite", "refresh": "Refresh", "register": "Register to Battlefield", "register-csie5434": "Register to CSIE5434 Competition", "register-csie5434-team": "Register to CSIE5434 Team Competition", "register-maicoin": "Register to MaiCoin Prize", "register-stop": "STOP", "register-strategy-team": "Register to Team Competition", "register-team": "Register", "run-debug": "Run and Debug", "save": "Save", "send": "Send", "simulate": "Simulate", "stop": "Stop", "submit": "Submit", "verify": "Verify", "view-details": "View Details", "yes": "Yes"}, "buy-and-hold-equity": "Buy & Hold Equity", "ca-blog-link": "https://docs.crypto-arsenal.io/blog", "ca-wallet": "CA Wallet", "can-not-be-blank": "Can not be blank", "can-not-delete-this-broker": "Can not delete this broker!", "cancel": "Cancel", "cancel-my-membership": "Cancel My Membership", "cancel-transition-to-periodunit-name-plan-and-resume-currentplannamewithperiod-plan": "Can<PERSON> transition to {{periodUnit}} {{name}} plan and resume {{currentPlanNameWithPeriod}} plan", "cannot-change-from-yearly-to-monthly-subscription": "Cannot change from yearly to monthly subscription", "change-plan": "Change Plan", "change-to": "Change to", "charged-for-each-adopted-strategy": "% charged for each Adopted Strategy", "chart": {"cumulative-profit": "Cumulative Profit", "cumulative-profit-and-dd": "Cumulative Profit & Draw Down", "dd": "DD", "lock-gain": "Lock Gain", "market-price": "Market Price", "order-placement": "Order", "price": "Price", "profit": "Profit", "stop-loss": "Stop Loss", "top-profit": "Top Profit"}, "client-id": "Client ID", "club": "Club", "club-information": "Club Information", "club-introduction": "Club Introduction", "club-member": "Club Member", "club-members": "Club Members", "club-name": "Club Name", "club-performance": "Club Performance", "club-photo": "Club Photo", "club-setting": "Club Setting", "clubs-you-manage": "Clubs you manage", "code-is-not-valid-or-expired": "Code is not valid or expired.", "coin-m-futures": "COIN-M Futures", "column": {"Market": "Market", "action": "Action", "amount": "Amount", "arguments": "Arguments", "backtest": "Backtest", "backtest-logs": "Backtest Logs", "backtest-period": "Time Period", "balances": "Balances", "comments": "Comments", "exchange": "Exchange", "finish-time": "Finish Time", "graph": "Graph", "id": "ID", "intro": "Introduction", "invest-settings": "Investment Settings", "latest-roi": "Latest ROI", "live-allocation": "Allocation", "live-api-key": "API Key", "live-notification": "Notification", "live-risk-management": "Stop Point", "logs": "Logs", "message": "Message", "name": "Strategy Name", "operation": "Operation", "pair": "Pair", "pair-type": "Type", "percentage": "Percentage", "performance": "Performance", "plan": "Plan", "price": "Price", "record": "Record", "register-step1": "Strategy Execution Settings", "register-step2": "Investment Amount Settings", "register-step3": "Profit Sharing Settings", "roi": "ROI (%)", "settings": "Settings", "start-time": "Start Time", "statistics": "Statistics", "status": "Status", "time": "Time", "time-period": "Time Period", "trend": "Trend", "type": "Type"}, "coming-soon": "Coming Soon", "comments": "COMMENTS", "company": "Company", "competition-id-missing": "Competition id missing", "confirm": "Confirm", "confirm-information-of-withdrawal-account": "Confirm information of withdrawal account", "confirm-new-login-password": "Confirm New Login Password", "connect": "Connect", "connect-and-access-to-accounts-in-exchanges": "Connect and Access to Accounts in Exchanges", "connect-wallet": "Connect Wallet", "connected": "Connected", "connecting-the-api-key": "Connecting the API Key...", "contact-us-via": "Any questions? Contact us via ", "continue": "Continue", "copiers-pnl": "Copiers’ PnL", "copiers-potential-total-profit-that-has-yet-to-be-sold-for-cash": "Copiers’ potential total profit that has yet to be sold for cash.", "copiers-roi": "Copiers’ ROI", "create a new address": "create a new address", "create-automated-crypto-trading-platform": "Crypto-Arsenal creates automated crypto-trading platform for developers to\ndevelop and traders to adopt trading strategies in a scientific and disciplined\nway.", "create-new-club": "Create New Club", "created": "Created", "crypto": "Crypto", "cryptocurrency-transaction-network-protocol-that-we-support-currently": "Cryptocurrency transaction network / protocol that we support currently.", "current": "Current", "current-login-password": "Current Login Password", "date": "Date", "delete": "Delete", "delete-all-desc": "Are you sure to delete all?", "depending-on-the-congestion-of-the-blockchain-network-the-arrival-time-may-vary-from-several-minutes-to-more-than-ten-minutes": "Depending on the congestion of the blockchain network, the arrival time may vary from several minutes to more than ten minutes.", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "deposit-history": "Deposit History", "deposit-your-ca-wallet": "deposit your CA wallet", "devlpr.": "Devlpr.", "dont-show-again": "Don't show again", "downgrade": "Downgrade", "dump with": "dump with", "duplicate-a-live-trading-bot-from-this-strategy-with-the-same-parameters-which-can-be-modified-accordingly": "Duplicate a live trading bot,\nfrom this strategy with the\nsame parameters, which can\nbe modified accordingly.\n", "edit-permission": "Edit Permission", "edit-photo": "Edit Photo", "edit-user-name": "Edit User Name", "email": "Email", "email-verification-code": "Email Verification Code", "enabled": "Enabled", "ensure-that-the-address-is-correct-and-on-the-same-network-transactions-cannot-be-cancelled": "Ensure that the address is correct and on the same network. Transactions cannot be cancelled.", "ensure-the-network-is": "Ensure the network is", "enter": "Enter", "enter-google-authenticator-code": "Enter Google Authenticator Code", "enter-the-following-information-to-enable-google-authenticator": "Enter the following information to enable Google Authenticator", "error": "Error", "error-log": "<PERSON><PERSON><PERSON>", "error-text": "error text", "ex-club-name-developer": "e.g. club name, developer...", "ex-store-name-developer": "ex: store name, developer, ...", "examine-the-following-information": "Examine the following information.", "exchange": "Exchange", "exchange-of-parameterized-strategy-is-adjustable": "Exchange of parameterized strategy is adjustable.", "expiration-date": "Expiration Date -", "expiration-date-text": "Expiration Date", "explore": "Explore", "failed-to-delete": "Failed to Delete!", "failed-to-duplicate": "Failed to Duplicate!", "failed-to-join-the-membership-tier": "Failed to join the membership tier!", "failed-to-update-the-plan": "Failed to update the plan!", "fee": "Fee", "fee-included": "fee included", "feedback-form": "<PERSON><PERSON><PERSON>", "field": {"21st-trade-time": "Time to 21st Trades", "21st-trade-time-desc": "Time to 21st Trades (in minutes)", "accepted-investment-amount": "Accepted Investment Amount", "accepted-num-of-adoptor": "Accepted Number of Adoptor", "api-connector-name": "Connector Name / API Webhook", "api-key": "Select API Key", "api-key-label": "API Key Label", "api-request-message": "API Request Message", "assigned-base": "Assigned Base", "assigned-exchange": "Assigned Exchange", "assigned-pair": "Assigned Pair", "assigned-quote": "Assigned Quote", "backtest-label": "Label of Backtesting Parameter Configuration", "backtest-period": "Backtest Period", "backtesting-interval": "Backtesting Interval", "base": "Base", "base-currency": "Base Currency", "base-exchange": "Base Exchange", "buy": "Buy", "buy-hold": "Buy & Hold Equity", "cancel": "Cancel", "cancel-type": "Cancel Type", "captain": "Captain", "checkbox-binary-code": "Use Binary Code", "client-order-id": "Client Order ID", "close-long": "Close Long", "close-short": "Close Short", "cloud-strategy": "Cloud Strategy", "code": "Code", "code-lang": "Language", "connector-token": "Connector Token", "constraints": "Select any one of constraints for the strategy adoption.", "created": "Date Created", "dev-note": "Developer Note", "dev-note-hint": "Work notes or to-do-list for this strategy (only you can see)", "email": "User Email", "entry-order-mode": "Entry Order Mode", "entry-order-value": "Entry Order Value", "exchange": "Exchange", "fee": "Fee", "fee-splitting": "Fee-splitting", "final-capital": "Final Capital", "finish-time": "Finish Time", "intro": "Introduction", "intro-hint": "An introduction to this strategy", "invest-amount": "Investment Amount", "is-remote-strategy": "Created on", "latest-roi": "Latest ROI", "leverage": "Leverage", "local-strategy": "Local Strategy", "long": "<PERSON>", "long-short": "<PERSON> and <PERSON>", "lower-leverage-limit": "Lower Leverage Limit (≥1)", "max-dd": "<PERSON>", "max-dd-desc": "During the period of backtest or live trade, the largest peak-to-valley historical drawdown of the strategy performance. It represents the stability of this strategy.", "max-reversal-wait": "<PERSON>", "member": "Member", "min-invest-limit": "Min. Investment Requirement", "model-access": "Model Access", "model-name": "Model Name", "model-upload": "Model Upload", "modified": "Date Modified", "name": "Strategy Name", "new-high-longest-time": "LTTMNH", "new-high-longest-time-desc": "Longest time to make new high (in minutes)", "new-label": "New Label", "num-of-trades": "Num of Trades", "num-trades": "Num Trades", "odds-ratio": "<PERSON>s <PERSON>", "odds-ratio-desc": "Odds ratio is calculated by dividing average trade profits by average trade losses. Odds convert the probability of an event happening into the potential amount of money you will receive back if you bet on it and win", "open-long": "Open Long", "open-position": "Open Position", "open-position-handling": "Open Position Handling", "open-short": "Open Short", "option-default": "<PERSON><PERSON><PERSON>", "option-desc": "Description", "option-hint": "Hint", "option-tips": "Tips", "option-type": "Type", "option-value": "Value", "option-variable": "Variable", "order-type": "Order Type", "profit-factor": "Profit Factor", "profit-factor-desc": "The Profit Factor (also called the Win-to-Loss Ratio) is calculated by dividing total trade profits by total trade losses.", "profit-sharing-ratio": "Profit Sharing Ratio", "profit-sharing-ratio-percentage": "Profit Sharing Ratio ( ≤ 50% )", "quantity-type": "Quantity Type", "quote": "Quote", "quote-currency": "Quote <PERSON><PERSON>", "quote-exchange": "Quote Exchange", "rank-yesterday": "Yesterday", "realized-roi": "Realized ROI/Gain", "return/dd": "Return / DD", "roi": "ROI", "roi-average": "Average ROI", "roi-percent": "ROI (%)", "roi-team": "Team ROI (%)", "sell": "<PERSON>ll", "sharpe-ratio": "<PERSON>", "sharpe-ratio-desc": "Sharpe ratio measures the excess return (or risk premium) per unit of deviation in an investment asset or a trading strategy, typically referred to as risk", "sharpe-ratio-team": "Team <PERSON>", "short": "Short", "spread": "Spread", "start-capital": "Initial Capital", "start-time": "Start Time", "status": "Status", "stop-loss": "Stop Loss", "stop-reason": "Stopped by", "strategy-count": "Total Strategies", "strategy-intro": "Strategy Introduction", "strategy-permission": "Strategy Permission", "suggest-exchange": "Suggest Exchange", "suggest-pair": "Suggest Pair", "suggest-stop-loss": "Suggest Stop Loss", "suggest-take-profit": "Suggest Take Profit", "suggested-leverage": "Suggested Leverage Value", "suggested-stop-loss": "Suggested Stop Loss ( 1~100% )", "suggested-take-profit": "Suggested Take Profit ( ≥ 1% )", "take-profit": "Take Profit", "team-name": "Team Name", "total-invest-limit": "Total Investment Amount", "total-number-of-adoption": "Total Number of Adoption", "total-usdt": "Total USDT", "trade-currency": "Trade Currency", "trade-pair": "Trade Pair", "tradingview-alert-message": "TradingView Alert Message", "trend": "Trend for the Strategy", "tv-connector-name": "Connector Name / TradingView Webhook", "type": "Type", "unrealized-roi": "Unrealized ROI", "upper-leverage-limit": "Upper Leverage Limit (≤125)", "username": "User Name", "variable-setting": "Variable Setting", "wallet-balance": "Wallet Balance", "webhook-url": "Webhook URL", "win-rate": "Win Rate", "win-rate-desc": "Win rate is how often the strategy can expect to win at a period during backtest/simulate/live trade per trade."}, "filter": {"exchange": "EXCHANGE", "pair": "PAIR", "roi": "ROI", "status": "STATUS", "trend": "TREND", "type": "TYPE"}, "final-capital": "Final Capital", "follow-the-developer-to-receive-the-latest-notifications": "Follow the developer to receive the latest notifications.", "followers": "followers", "font": "Font", "footer-title-1": "Join Crypto-Arsenal today", "footer-title-2": "It's free now", "format, and size should be less than": "format, and size should be less than", "free": "FREE", "full-screen": "Full Screen", "get-3-trading-bots-for-free": "Experiencing Crypto-Arsenal Now Get 3 Trading Bots for FREE !", "gift-code": "Gift Code", "go-to": "Go to", "google-authenticator": "Google Authenticator", "google-authenticator-code": "Google Authenticator Code", "google-authenticator-disable": "Google Authenticator - Disable", "google-authenticator-enable": "Google Authenticator - Enable", "guide-cap": "Guide", "here": "here", "highest-roi": "Highest ROI", "history": "History", "home": "Home", "i-understand": "I understand.", "if-you-filled-the-form-already-please-contact-the-team-for-your-application-status": "If you filled the form already, please contact the team for your application status.", "in-order-to-guarantee-the-quality-of-every-admitted-strategy-in-arena-we-spend-certain-efforts-to-manage-each-strategy-making-sure-its-smooth-execution-at-any-time-thus-crypto-arsenal-only-charges-some-percentage-of-received-splits-from-each-adopted-strategy-which-gained-profit-for-adopters-for-example-when-strategy-a-made-100-usdt-for-me-i-paid-10-usdt-to-the-developer-of-strategy-a-as-a-split-if-the-developer-of-strategy-a-remains-starter-plan-user-while-receiving-the-split-then-crypto-arsenal-will-take-5-of-the-split-out-of-developers-account-which-is-10-x-5-0-5-usdt-please-note-that-crypto-arsenal-reserves-rights-to-make-changes-in-the-future": "In order to guarantee the quality of every admitted strategy in Arena, we spend certain efforts to manage each strategy, making sure its smooth execution at any time, thus Crypto-Arsenal ONLY charges some percentage of received splits from each adopted strategy which gained profit for adopters. For example, when 'Strategy A' made 100 USDT for me, I paid 10 USDT to the developer of 'Strategy A' as a split. If the developer of 'Strategy A' remains Starter Plan user while receiving the split, then Crypto-Arsenal will take 5% of the split out of developer's account, which is 10 x 5% = 0.5 USDT. Please note that Crypto-Arsenal reserves rights to make changes in the future.", "initial-capital": "Initial Capital", "invalid": "Invalid", "invalid-amount": "Invalid amount", "invalid-key": "Invalid key.", "invalid-password": "Invalid Password", "invalid-secret": "Invalid secret.", "it-usually-takes-us-1-3-days-to-verify-the-status-of-verification-will-be-shown-on-the-strategy-bar-on-strategy-page": "It usually takes us 1-3 days to verify. The status of verification will be shown on the strategy bar on Strategy page.", "joblib": "joblib", "joblib==1.1.02": "joblib==1.1.02.", "join": "Join", "join-members": "Join Members", "join-successfully": "Join Successfully !", "join-us": "Join us", "keep-on-my-own": "Keep on my own", "keep-these-positions-and-do-nothing": "Keep these positions and do nothing!", "label": "Label", "label-duplicated": "Label duplicated", "label-your-whitelisted-addresses-for-the-future-usage": "Label your whitelisted addresses for the future usage.", "latest-activity": "Latest Activity", "launched": "Launched", "launched-0": "launched", "less-than-0-greater-than-price-less-than-0-greater-than-less-than-1-greater-than-yr-less-than-1-greater-than-with-less-than-2-greater-than-yearly-less-than-2-greater-than-payment": "<0>$ {{price}}</0> <1> / yr </1> with <2> yearly </2>payment", "leverage": "Leverage", "live-trade-checkbox": "After this executed strategy is stopped, if there&apos;re open positions, what would you want us to do for you?", "live-trade-launched": "Live-Trade Launched", "live-trade-launched-0": "Live-trade Launched", "live-trade-limit-message": "The number of live trade has reached the limit of your subscription.", "live-trade-loss-hint": "Stop trading if the accumulated loss reaches:", "live-trade-profit-hint": "Stop trading if the accumulated profit reaches:", "live-trade-slider": "Use my {{base}} asset if available", "live-trading": "Live-Trading", "live-trading-delete": "Live Trading - Delete", "loading": "Loading...", "loading-your-clubs": "Loading your clubs...", "loading-your-strategies": "Loading your strategies...", "lock": "locked", "lock-spliting-amount": "{{amount}} USDT is {{state}} to your wallet", "locked": "Locked", "log": "Log", "login-password": "Login Password", "long": "<PERSON>", "lower-leverage-limit": "Lower Leverage Limit", "lttmnh-stands-for-longest-time-to-make-new-high": "LTTMNH stands for Longest Time To Make New High. It is to measure the \nduration of making new highest profit from the previous in hour.", "manage-registration": "Manage Registration", "manual-stop-if-you-manually-stop-them": "Manual Stop: If you manually stop them.", "mapping-status": {"Error": "Error", "Live Trading": "Live Trading", "None": "None", "Pending": "Pending", "Reached Stop Loss": "Reached Stop Loss", "Reached Take Profit": "Reached Take Profit", "Simulating": "Simulating", "Stopped": "Stopped", "Trading": "Trading", "User Stopped": "User Stopped"}, "marketplace": {"Copy": "Copy", "Detail": "Detail", "Marketplace": "Marketplace", "More Strategies": "More Strategies", "My Strategy": "My Strategy", "number-of-trades": "Number of Trades", "overview": "Overview", "roi": "ROI"}, "maximum-duration-of-live-trading": "Maximum duration of live trading", "mdd-description": ["MDD stands for 'Maximum Draw Down'. it is a measure of an asset's largest price drop from a peak to a trough, before a new peak is attained. MDD is an indicator of downside risk over a specified time period.", "MDD = (Trough Value - Peak Value) / Peak Value", "Example:", "Assume an investment portfolio has an initial value of $500,000. The portfolio increases to $750,000 over a period of time, before plunging to $400,000 in a ferocious bear market. It then rebounds to $600,000, before dropping again to $350,000. Subsequently, it more than doubles to $800,000.", "MDD in this case is ($350,000 - $750,000) / $750,000 = -53.33%", "The initial peak of $750,000 is used in the MDD calculation. The interim peak of $600,000 is not used, since it does not represent a new high. The new peak of $800,000 is also not used since the original drawdown began from the $750,000 peak. The MDD calculation takes into consideration the lowest portfolio value ($350,000 in this case) before a new peak is made, and not just the first drop to $400,000."], "mdd-description-short": ["MDD stands for 'Maximum Draw Down'. it is a measure of an asset's largest price drop from a peak to a trough, before a new peak is attained. MDD is an indicator of downside risk over a specified time period.", "MDD = (Trough Value - Peak Value) / Peak Value"], "mdd-stands-for-and-39-maximum-draw-down-it-is-a-measure-of-an-asset-and-39-s-largest-price-drop-from-a-peak-to-a-trough-before-a-new-peak-is-attained-mdd-is-an-indicator-of-downside-risk-over-a-specified-time-period-less-than-br-greater-than-mdd-trough-value-peak-value-peak-value-less-than-br-greater-than-example-less-than-br-greater-than-assume-an-investment-portfolio-has-an-initial-value-of-500-000-the-portfolio-increases-to-750-000-over-a-period-of-time-before-plunging-to-400-000-in-a-ferocious-bear-market-it-then-rebounds-to-600-000-before-dropping-again-to-350-000-subsequently-it-more-than-doubles-to-800-000-less-than-br-greater-than-mdd-in-this-case-is-350-000-750-000-750-000-53-33-less-than-br-greater-than-the-initial-peak-of-750-000-is-used-in-the-mdd-calculation-the-interim-peak-of-600-000-is-not-used-since-it-does-not-represent-a-new-high-the-new-peak-of-800-000-is-also-not-used-since-the-original-drawdown-began-from-the-750-000-peak-the-mdd-calculation-takes-into-consideration-the-lowest-portfolio-value-350-000-in-this-case-before-a-new-peak-is-made-and-not-just-the-first-drop-to-400-000": "MDD stands for 'Maximum Draw Down. it is a measure of an asset's largest price drop from a peak to a trough, before a new peak is attained. MDD is an indicator of downside risk over a specified time period. \nMDD = (Trough Value - Peak Value) / Peak Value \nExample: \nAssume an investment portfolio has an initial value of $500,000. The portfolio increases to $750,000 over a period of time, before plunging to $400,000 in a ferocious bear market. It then rebounds to $600,000, before dropping again to $350,000. Subsequently, it more than doubles to $800,000.\nMDD in this case is ($350,000 - $750,000) / $750,000 = -53.33% \nThe initial peak of $750,000 is used in the MDD calculation. The interim peak of $600,000 is not used, since it does not represent a new high. The new peak of $800,000 is also not used since the original drawdown began from the $750,000 peak. The MDD calculation takes into consideration the lowest portfolio value ($350,000 in this case) before a new peak is made, and not just the first drop to $400,000.", "mdd-stands-for-and-apos-maximum-draw-down": "MDD stands for &apos;Maximum Draw Down. it is a measure of an asset&apos;s\nlargest price drop from a peak to a trough, before a new peak is attained.\nMDD is an indicator of downside risk over a specified time period. MDD =\n(Trough Value - Peak Value) / Peak Value Example: Assume an investment\nportfolio has an initial value of $500,000. The portfolio increases to\n$750,000 over a period of time, before plunging to $400,000 in a ferocious\nbear market. It then rebounds to $600,000, before dropping again to\n$350,000. Subsequently, it more than doubles to $800,000. MDD in this case\nis ($350,000 - $750,000) / $750,000 = -53.33% The initial peak of $750,000\nis used in the MDD calculation. The interim peak of $600,000 is not used,\nsince it does not represent a new high. The new peak of $800,000 is also\nnot used since the original drawdown began from the $750,000 peak. The MDD\ncalculation takes into consideration the lowest portfolio value ($350,000\nin this case) before a new peak is made, and not just the first drop to\n$400,000.", "member": "Member", "member-clubs": "Member Clubs", "member-from": "Member from", "members": "Members", "message": "Message", "metrics": "Metrics", "minimum-deposit-less-than-0-greater-than-1-0-tokenname-less-than-0-greater-than": "Minimum deposit <0>1.0 {{tokenName}}</0>", "minimumal-withdrawal-amount-5-usdt": "** Minimumal withdrawal amount: 5 USDT", "ml-acknowledgment-message": "and copied messages for ML Trading.", "ml-description": "Call your trained <PERSON><PERSON><PERSON><PERSON> model in the strategy", "mngt-fee": "Mngt. Fee", "mo": "mo", "modal": {"arena-registration": {"Cannot switch to the Isolated Margin mode and Hedge mode": "2. <PERSON><PERSON> switch to the Isolated Margin mode and Hedge mode.", "There are positions of different leverages": "1. There are positions of different leverages.", "There is a violation of leverage limits applied by the exchanges": "3. There is a violation of leverage limits applied by the exchanges.", "You can follow": "You can follow", "live-trade-fail": "Live Trade Failed", "register-sucess": "Successfully Registered!", "this document": "this document", "to adjust your leverage setting in exchange or contact CA cutsomer service": "to adjust your leverage setting in exchange or contact CA cutsomer service.", "we detected the leverage cannot be adjusted due to the following reasons": "Ｗe detected the leverage cannot be adjusted due to the following reasons:"}, "code": {"confirm-binary": "Are you sure to edit code by uploading a pyc file? This will remove all your code which has been edited before.", "confirm-lang": "You have edited the code in {{oldLang}} last time. Are you sure to change to {{newLang}} for this strategy?", "error-invalid-file-size": "The file size is too large. Please try again and select a smaller file.", "error-invalid-format": "This is an unsupported file format. Please try again and select a pyc file.", "error-multiple-file": "You can only upload a single file.", "title-binary": "Edit in Binary Code", "title-invalid-file": "Unsupported File", "title-lang": "Change Code Language"}, "idle-logout": {"count-down": "You are going to be logged out in {{timeLeft}} due to inactivity. Please press continue to stay logged in.", "logged-out": "You've been logged out due to {{idleTime}} of inactivity. Please log in again to continue your account.", "title": "Automatic Logout"}, "options": {"error-ascii": " Only ASCII characters are allowed.", "error-name-required": "Variable name is required.", "error-no-default": " You must provide a default value.", "error-type": "The default value doesn't match the type {{type}}.", "error-used": "This variable name has been used.", "title-add-fail": " Fail to Add Parameter {{variable}}"}, "portfolio": {"confirm-stop": "Are you sure to stop <0>{{strategyName}}</0> {{taskType}}? This action cannot be undone after it is stopped.", "title-performance": "{{taskType}} Performance", "title-stop": "Stop {{taskType}}?"}, "register": {"confirm-stop": "Are you sure to stop registering strategy and end the registration status?", "confirm-stop-team": "Are you sure to cancel strategy registry?", "confirm-submit": "Confirm before submitting the strategy. The strategy cannot be edited and deleted after the submission", "confirm-submit-team": "Confirm before submitting the strategy for {{pair}} pair?", "title-error": "Fail to Register Strategy", "title-error-team": "Fail to Register Team", "title-stop": "Stop Registration", "title-stop-fail": "Fail to Stop Registration", "title-stop-team": "Cancel Strategy Registry", "title-submit": "Submit to Verification", "title-submit-team": "Submit to Verification", "title-success": "Submit successfully", "title-success-team": "Strategy Registered Successfully"}, "scoreboard": {"title-filter": "Filter"}, "strategy": {"confirm-backtest-leave": "You will lose the backtest result once leave.", "confirm-delete": "Are you sure to remove this strategy? Once you do this, all the backtest and trade history will also be removed.", "confirm-save-backtest": "The strategy will be saved before you backtest it.", "confirm-save-leave": "If you leave editor without saving, you will lose all unsaved changes.", "error-in-used-delete": "The strategy is in a backtest / simulate / trade, so it can't be removed. Please wait for backtest finishes or stop trade first.", "load-template": "Load Template", "override-msg": "You have code in this strategy. Are you sure to override this code with template?", "override-title": "Override Code with Template", "title-backtest": "Leave the backtest?", "title-backtest-fail": "Unable to Backtest", "title-copy-fail": "Fail to Copy My Strategy", "title-delete": "Delete My Strategy", "title-delete-fail": "Fail to Delete My Strategy", "title-live-fail": "Unable to Live Trade", "title-save": "Save the changes?", "title-simulate-fail": "Unable to Simulate"}, "strategy-edit": {"erase-connector": "Are you sure you want to delete this source?", "failed-add-connector": "Fail to add connector to the strategy", "failed-erase-connector": "Failed to remove Connector", "market-data-editable": "The change here will apply to newly launched bots and will not affect existing bots.", "market-data-read-only": "You cannot modify the following after registering to Arena/Club", "missing-connector-message": "We noticed that you’ve changed to the code base for {{sourceName}}, do you want to add {{sourceDesc}}?", "new-code-base": "Yes, I understand the current code base will be replaced with the new one.", "new-code-base-message": "We noticed that you’ve added {{sourceName}}, do you want to change to the code base for {{sourceDesc}}{{extra}}", "no-new-code-base": "No, I want to stick with my current code base."}, "task": {"confirm-delete-backtest": "Are you sure to remove this Backtest history?", "confirm-delete-live": "Are you sure to remove this Live Trade history?", "confirm-delete-simulate": "Are you sure to remove this Simulate history?", "error-running-task-delete": "You can't delete a running task", "title-delete-backtest": "Delete Backtest History", "title-delete-backtest-fail": "Fail to delete this Backtest record", "title-delete-live": "Delete Live Trade History", "title-delete-simulate": "Delete Simulate History", "title-delete-simulate-fail": "Fail to delete this Simulate record"}}, "monthly": "monthly", "monthly-p-and-l": "Monthly P&L", "monthly-subscription-price": "Monthly Subscription, {{price}} USDT/mon", "more": "more", "most-popular": "Most popular", "msg": {"drag-browse-pkl-hint": "Drag & drop your code in a pkl file <0/> or <1>browse</1> your file", "drag-browse-pyc-hint": "Drag & drop your code in a pyc file <0/> or <1>browse</1> your file", "drop-pyc-hint": "Drop to upload the pyc file", "fetch-fail": "Fail to fetch data.", "invest-not-enough-balance": "Investment amount can’t be bigger than wallet balance.", "live-after-lock": "Balance after lock:", "live-ca-wallet-balance": "Available Balance in CA Wallet:", "live-deposit-more": "Need to Deposit More:", "live-lock-split": "Lock Splitting Amount:", "live-split-hint": "Splitting Calculation (Profit Sharing Ratio: {{sharingPercent}} %)", "live-step-allocation": "{{step}} / {{total}} : Choose trade pair and allocate investment amount.", "live-step-api-key": "{{step}} / {{total}} : Select an effective API key or create a new one.", "live-step-notification": "{{step}} / {{total}} : Set notifcations.", "live-step-risk-management": "{{step}} / {{total}} : Set stop loss and take profit point.", "live-stop-loss-hint": "Stop trading if the accumulated loss reaches to: <0>{{amount}} {{baseCrypto}}</0>", "live-stop-take-hint": "Stop trading if the accumulated profit reaches to: <0>{{amount}} {{baseCrypto}}</0>", "multi-pair-base-exchange-mismatch": "This multi-pair strategy only support {{baseExchange}} exchange.", "no-api-key-available": "No API key available", "no-backtest-logs": "You have no logs", "no-backtest-logs-hint": "Click the button above to start backtesting.", "no-data-for-chart": "No enough data to draw the chart", "no-data-statistics-hint": "N/A: No enough or applicable data to calculate", "no-multi-pair-live-trade-for-max": "However, Live Trade for MAX exchange is currently not supported.", "no-multi-simulate-for-max": "This multi-pair strategy uses MAX exchange but Simulate for MAX exchange is currently unsupported.", "no-register-hint": "Go to <0>$t(strategies:tab.cloud)</0> and click <1/> to register your strategies to <2>$t(MaiCoin Prize)</2>.", "no-register-hint-csie5434": "Go to <0>$t(strategies:tab.cloud)</0> and click <1/> to register your strategies to <2>$t(CSIE5434 Competition)</2>.", "no-register-hint-maicoin": "Go to <0>$t(strategies:tab.cloud)</0> and click <1/> to register your strategies to <2>$t(MaiCoin Prize)</2>.", "no-register-multi-for-max": "This multi-pair strategy uses MAX exchange but registration for MAX is currently unsupported.", "no-registered-strategy": "You have no strategy in registration.", "no-registered-strategy-competition": "You have no strategy in competitions ", "no-registered-strategy-csie5434": "You have no strategy in CSIE5434 Competition.", "no-registered-strategy-maicoin": "You have no strategy in MaiCoin Prize.", "no-strategy-watchlist": "You have no strategy in $t(Watchlist).", "no-strategy-watchlist-hint": "Go to <0>$t(Battlefield)</0> to add strategies to $t(Watchlist).", "no-task-portfolio": "You have no running {{taskType}} now.", "no-task-portfolio-hint": "Go to My Strategy to start a {{taskType}}.", "register-step1": "The verification backtesting will be executed based on the settings. And traders will be suggested to trade based on the settings as well.", "register-step2": "You can set limitations of the amount invested in the strategy here. The items are optional.", "register-step3": "You can suggest traders stop loss and take profit. And set the profit sharing ratio as your strategy fee once the strategy makes profits for them.", "select-api-key-hint": "Select an API key", "select-base-currency": "Please select a base crypto.", "watchlist-added": "Strategy added to Watchlist.", "watchlist-go": "Go to Watchlist", "watchlist-removed": "Strategy removed from Watchlist."}, "my-wallet": "My Wallet", "n days": "{{ n }} Days", "nav": {"language": "Language"}, "network": "Network", "network-error-please-contact-our-customer-service-for-help": "Network Error, please contact our customer service for help.", "new-address": "New Address", "new-login-password": "New Login Password", "new-user-name": "New User Name", "newest": "Newest", "newest-joined": "Newest Joined", "no-active-competition": "No Active Competition", "no-data": "No Data", "no-desc": "No description", "no-expiration-date": "No Expiration Date", "no-member-joined-yet": "No member joined yet.", "no-notifications-yet": "No Notifications Yet.", "none": "None", "not-enabled": "Not Enabled", "not-supported": "Not Supported!", "note-the-strategy-will-be-opened-to": "Note: The strategy will be opened to", "notifications": "Notifications", "now": "now", "num-of-trades": "Num of Trades", "number-of-api-key-registration": "Number of API Key Registration", "number-of-backtesting-month": "Number of Backtesting / Month", "number-of-simulation-nand-live-trading-month": "Number of Simulation\nand Live-Trading / Month", "ocd-description": "Leverage blockchain data and node to find alpha other than price data", "odds-ratio": "<PERSON>s <PERSON>", "odds-ratio-description": ["Odds Ratio is also known as Profit/Loss Ratio. Odds Ratio is the average profit on winning trades divided by the average loss on losing trades over a specified time period.", "Odds Ratio = (Total Profit / Number of Winning Trades) / (Total Loss / Number of Losing Trades)", "Odds Ratio measures how a trading strategy or system is performing. Obviously, the higher the ratio the better. Many trading books call for at least a 2:1 ratio. For example, if a system had a winning average of $750 per trade and an average loss over the same time of $250 per trade, then odds ratio would be 3:1. A consistently solid odds ratio can encourage a trader to leverage bets on the same strategy in an attempt to generate greater absolute profits."], "odds-ratio-description_short": ["Odds Ratio is also known as Profit/Loss Ratio. Odds Ratio is the average profit on winning trades divided by the average loss on losing trades over a specified time period.", "Odds Ratio = (Total Profit / Number of Winning Trades) / (Total Loss / Number of Losing Trades)", "Odds Ratio measures how a trading strategy or system is performing. Obviously, the higher the ratio the better. Many trading books call for at least a 2:1 ratio."], "odds-ratio-is-also-known-as-profit-loss-ratio": "Odds Ratio is also known as Profit/Loss Ratio. Odds Ratio is the average\nprofit on winning trades divided by the average loss on losing trades over\na specified time period. Odds Ratio = (Total Profit / Number of Winning\nTrades) / (Total Loss / Number of Losing Trades) Odds Ratio measures how a\ntrading strategy or system is performing. Obviously, the higher the ratio\nthe better. Many trading books call for at least a 2:1 ratio. For example,\nif a system had a winning average of $750 per trade and an average loss\nover the same time of $250 per trade, then odds ratio would be 3:1. A\nconsistently solid odds ratio can encourage a trader to leverage bets on\nthe same strategy in an attempt to generate greater absolute profits.", "og": {"description": "Crypto-Arsenal is revolutionizing the game with its \"Algo-Driven Trading Alliance\", offering KOLs a risk-free, fully branded copy trading platform!\nAs an ambitious trading KOL, what you can gain:\n- Boost your affiliate rebates effortlessly through automated trading\n- Earn extra commissions to diversify your income streams\n- Unlock exciting new business opportunities and expand your network\nBest of all, It's completely FREE! Join us today and turn your influence into profit machine while leading the future of algo-driven trading!", "title": "Crypto Arsenal"}, "on": "on", "oops-no-room-to-register-api-keys-please-upgrade-your-plan": "Oops! No room to register API keys, please upgrade your plan.", "open-position": "Open Position", "open-position-handling": "Open Position Handling", "open-position-holding": "Open Position Holding", "operation": "Operation", "or": "or", "order-placement": "Order", "order-type-market": "Market", "otp-is-invalid-or-expired": "OTP is invalid or expired.", "overwrite": "Overwrite", "owner": "Owner", "owners": "Owners", "page-error": {"404": {"msg": "The page you’re looking for does not exist", "title": "Oops! Page Not Found"}, "500": {"msg": "An unexpected error occurred", "title": "Oops! Internal Server Error"}, "ssr": {"msg": "An unexpected error occurred", "title": "Oops! Unknown Error"}}, "paid": "Paid", "parameter-label-hint": "The label exists, are you sure to overwrite?", "partners": "Partners", "passcode": "Passcode", "password-is-not-consistent-to-previous-one": "Password is not consistent to previous one.", "password-is-not-correct": "Password is not correct", "pay-attention-to-the-notice-below-and-choose-properly": "Pay attention to the notice below and choose properly.", "payment": "payment", "people found this helpful": "people found this helpful", "performance": "Performance", "performance-metrics": "Performance Metrics", "permission": "Permission", "pf-description": ["Profit Factor is defined as the gross profit divided by the gross loss (including transaction fee) for the entire trading period.", "Profit Factor (PF) = Gross Profit / |Gross Loss|", "PF metric helps traders analyze the degree to which wins are greater than losses. Usually, PF is expected to be greater than 1.5. For PF less than 1, it would be a losing system; however, the trading system may be considered over optimized while PF much larger than 1."], "phone": "Phone", "pickle-text-header-link": "browse", "pkl": "pkl", "plan": "Plan", "please-enter-gift-code-and-press-redeem-button-either-starter-elite-or-premium-plan-will-be-shown-above-enjoy-it": "Please enter gift code and press 'Redeem' button; either 'Starter', 'Elite', or 'Premium' plan will be shown above. Enjoy it!", "please-go-back-to-the-tab-and-reload-for-latest-update": "Please go back to the tab and reload for latest update.", "please-keep-the-above-key-code-in-a-safe-place-as-it-will-allow-you-to-recover-your-google-authentication-in-case-of-phone-loss": "Please keep the above key code in a safe place as It will allow you to recover your Google Authentication in case of phone loss.", "please-sell-them-all-with-the-market-price": "Please sell them all with the market price.", "popular": "POPULAR", "portfolio": {"category": {"live": "Live Trade", "simulate": "Simulate"}, "filter-modal": {"mdd-info": "Maximum Drawdown (MDD) represents the largest percentage drop in value a trading strategy has experienced from its peak. It indicates the potential loss you might face during unfavorable market conditions."}, "idle-logout": {"count-down": "You are going to be logged out in {{timeLeft}} due to inactivity. Please press continue to stay logged in.", "logged-out": "You've been logged out due to {{idleTime}} of inactivity. Please log in again to continue your account.", "title": "Automatic Logout"}, "removed-strategy": "- Removed Strategy -", "top-board": {"allocated": "Allocated", "available": "Available", "equity": "Equity", "profit": "Profit"}}, "premium": "PREMIUM", "price": "Price", "price-amount-message": "Price/Amount Message", "processing": "Processing", "profit-factor": "Profit Factor", "profit-factor-is-defined-as-the-gross-profit-divided-by-the-gross-loss": "Profit Factor is defined as the gross profit divided by the gross loss\n(including transaction fee) for the entire trading period. Profit Factor\n(PF) = Gross Profit / |Gross Loss| PF metric helps traders analyze the\ndegree to which wins are greater than losses. Usually, PF is expected to\nbe greater than 1.5. For PF less than 1, it would be a losing system;\nhowever, the trading system may be considered over optimized while PF much\nlarger than 1.", "profit-factor-is-defined-as-the-gross-profit-divided-by-the-gross-loss-including-transaction-fee-for-the-entire-trading-period-less-than-br-greater-than-profit-factor-pf-gross-profit-or-gross-loss-or-less-than-br-greater-than-pf-metric-helps-traders-analyze-the-degree-to-which-wins-are-greater-than-losses-usually-pf-is-expected-to-be-greater-than-1-5-for-pf-less-than-1-it-would-be-a-losing-system-however-the-trading-system-may-be-considered-over-optimized-while-pf-much-larger-than-1-less-than-br-greater-than": "Profit Factor is defined as the gross profit divided by the gross loss (including transaction fee) for the entire trading period. \nProfit Factor (PF) = Gross Profit / |Gross Loss| \nPF metric helps traders analyze the degree to which wins are greater than losses. Usually, PF is expected to be greater than 1.5. For PF less than 1, it would be a losing system; however, the trading system may be considered over optimized while PF much larger than 1.\n", "profit-sharing": "Profit-Sharing, {{sharingPercent}}%", "profit-sharing-ratio-50": "Profit Sharing Ratio ( ≤ 50% )", "provide-information-about-external-trading-signal": "Provide information about external trading signal.", "public": "Public", "qtr": "QTR", "quantity": "Quantity", "quote": "\"{{content}}\"", "realized-copiers-pnl": "Realized Copier's PNL", "realized-gain": "Realized Gain", "realized-pnl": "Realized PNL", "realized-roi": "Realized ROI", "realized-roi-gain": "Realized ROI / Gain", "received": "Received", "recent-withdrawal-history": "Recent Withdrawal History", "redeem": "Redeem", "referral": "Referral", "referral-code": "Referral Code", "refresh": "Refresh", "refreshing": "Refreshing", "registry-confirmation": "I confirmed to {{type}} the {{extra}}strategy.", "reload-the-web-page-to-check-your-balance": "Reload the web page to check your balance.", "renew-your-subscription": "renew your subscription", "required-while-local-strategy-development": "Required While Local Strategy Development", "reset": "Reset", "reset-password-0": "Reset Password", "reset-to-default": "Reset to De<PERSON>ult", "reset-to-free-plan-your-currentplannamewithperiod-plan-can-still-be-used-until-currentexpiredformateddate": "Reset to Free plan. Your {currentPlanNameWithPeriod} plan can still be used until {currentExpiredFormatedDate}", "resources": "Resources", "resume-less-than-0-greater-than-currentplannamewithperiod-less-than-0-greater-than-plan": "Resume <0>{{currentPlanNameWithPeriod}} </0> plan", "return-dd": "Return / DD", "roi": "ROI", "roi-description": "Total net profit (or loss) / lnitial cost", "role-management": "Role Management", "run-up-to-3-trading-bots": "You can run up to 3 trading bots per month for FREE on specified exchanges.", "sad-description": "Supercharge quant strategies with sentiment and qualitative data from social medias", "saved": "saved", "saved-0": "Saved", "scan-qr-code-with-or-enter-key-code-into-google-authenticator-app-with-your-mobile": "Scan QR Code with or enter Key Code into Google Authenticator App with your mobile", "scan-the-code-on-or-copy-the-address-above-and-paste-it-to-the-withdrawal-page-of-the-wallet-app": "Scan the code on or copy the address above and paste it to the withdrawal page of the wallet App.", "sciket-learn==1.0.2, lightgbm==3.3.2": "sciket-learn==1.0.2, lightgbm==3.3.2,", "search": "search", "search-0": "Search", "search-with-example": "ex: strategy name, developer, ...", "seats left": "seat left!", "seats-left": "seats left!", "secure-address-for-crypto-withdraw": "Secure Address for Crypto Withdraw", "select": {"dark": "Dark Theme", "light": "Light Theme"}, "select-competition": "Select Competition", "select-the-tier-you-would-like-to-open-up-to": "Select the tier you would like to open up to:", "select-the-trading-club-to-on-board-the-strategy": "Select the Trading Club to on-board the strategy.", "sell-w-mrkt-price": "Sell w. mrkt price", "send-only-less-than-0-greater-than-tokenname-less-than-0-greater-than-to-this-deposit-address": "Send only <0>{{tokenName}}</0> to this deposit address.", "sent": "<PERSON><PERSON>", "set-up-the-constraints-for-strategy-adoption-for-non-members": "Set up the constraints for strategy adoption for non-members.", "set-up-the-constraints-for-the-strategy-adoption-for-non-members": "Set up the constraints for the strategy adoption for non-members.", "set-up-the-leverage-limitation-for-the-strategy": "Set up the leverage limitation for the strategy.", "set-up-the-time-constraints-for-live-trading-for-non-members": "Set up the time constraints for live trading for non-members.", "setting": "Setting", "sharpe-ratio": " <PERSON>", "sharpe-ratio-description": ["Sharpe Ratio is used to help investors understand the return of an investment compared to its risk. It is the average return earned in excess of the risk-free rate per unit of volatility or total risk. Volatility is a measure of the price fluctuations of an asset or portfolio.", "Sharpe Ratio = (Return of Portfolio - Risk-Free Rate) / Standard Deviation of the portfolio's excess return.", "The Sharpe ratio is one of the most widely used methods for calculating risk-adjusted return. Modern Portfolio Theory (MPT) states that adding assets to a diversified portfolio that has low correlations can decrease portfolio risk without sacrificing return."], "sharpe-ratio-is-used-to-help-investors-understand-the-return-of-an": "Sharpe Ratio is used to help investors understand the return of an investment compared to its risk. It is the average return earned in excess of the risk-free rate per unit of volatility or total risk. Volatility is a measure of the price fluctuations of an asset or portfolio.\nSharpe Ratio = (Return of Portfolio - Risk-Free Rate) / Standard Deviation of the portfolio's excess return\nThe Sharpe ratio is one of the most widely used methods for calculating risk-adjusted return. Modern Portfolio Theory (MPT) states that adding assets to a diversified portfolio that has low correlations can decrease portfolio risk without sacrificing return.", "sharpe-ratio-is-used-to-help-investors-understand-the-return-of-an-investment-compared-to-its-risk-it-is-the-average-return-earned-in-excess-of-the-risk-free-rate-per-unit-of-volatility-or-total-risk-volatility-is-a-measure-of-the-price-fluctuations-of-an-asset-or-portfolio-less-than-br-greater-than-sharpe-ratio-return-of-portfolio-risk-free-rate-standard-deviation-of-the-portfolio-and-39-s-excess-return-less-than-br-greater-than-the-sharpe-ratio-is-one-of-the-most-widely-used-methods-for-calculating-risk-adjusted-return-modern-portfolio-theory-mpt-states-that-adding-assets-to-a-diversified-portfolio-that-has-low-correlations-can-decrease-portfolio-risk-without-sacrificing-return": "Sharpe Ratio is used to help investors understand the return of an investment compared to its risk. It is the average return earned in excess of the risk-free rate per unit of volatility or total risk. Volatility is a measure of the price fluctuations of an asset or portfolio. \nSharpe Ratio = (Return of Portfolio - Risk-Free Rate) / Standard Deviation of the portfolio's excess return. \nThe Sharpe ratio is one of the most widely used methods for calculating risk-adjusted return. Modern Portfolio Theory (MPT) states that adding assets to a diversified portfolio that has low correlations can decrease portfolio risk without sacrificing return.", "show less": "show less", "show more": "show more", "show-more": "show more", "simulation-and-live-trading-launched": "Simulation and Live-Trading Launched", "simulation-and-live-trading-month": "Simulation and Live-Trading / Month", "simulation-limit-message": "The number of simulation has reached the limit of your subscription.", "simulation-performance": "Simulation Performance", "since-your-balance-in-ca-wallet-is-insufficient": "since your balance in CA Wallet is insufficient.", "social-links": "Social Links", "split": "Split", "spot": "Spot", "starter-plan-expire-warning": "Your Starter plan will expire in {{daysBeforeExpired}} days({{formatedExpiredDate}})\nsince {{reason}} \n If you want to {{action}}", "status": {"backtest": "Backtest", "backtesting": "Backtesting", "buy": "Buy", "close-long": "Close Long", "close-short": "Close Short", "initializing": "Initializing...", "live-trading": "Live Trading", "loading": "Loading...", "locked": "Locked", "manage-aborted": "Aborted", "manage-offboard": "Off Board", "manage-onboard": "On Board", "manage-onboard-rejected": "On Board / Rejected", "manage-rejected": "Rejected", "manage-submitted": "Submitted", "manage-verified": "Verified", "manage-verifying": "Verifying", "none": "None", "open-long": "Open Long", "open-short": "Open Short", "register-onboard": "ON BOARD", "register-rejected": "REJECTED", "register-submitted": "SUBMITTED", "register-verified": "VERIFIED", "registration": "Registration", "requesting": "Requesting...", "sell": "<PERSON>ll", "simulating": "Simulating", "total-usdt": "Total USDT", "trading": "Trading"}, "stop": "Stop", "stop-loss": "Stop Loss", "stop-loss-or-take-profit-if-a-pre-determined-stop-loss-or-take-profit-level-is-reached": "Stop Loss or Take Profit: If a pre-determined 'STOP LOSS' or 'TAKE PROFIT' level is reached.", "stopped": "Stopped", "strategy": "Strategy", "strategy-name": "Strategy Name", "strategy-order-size-value": "From your TradingView strategy's Order Size", "strategy-overview": "Strategy Overview", "strategy-permission": "Strategy Permission", "strong": "strong", "submit-strategy-to-arena-for-free": "Submit Strategy to Arena for Free", "submitting": "Submitting", "subscription": "Subscription", "subscription-confirmation": "Subscription Confirmation", "subscription-error": "Subscription Error", "subscription-payment-failure-if-the-subscription-fee-cannot-be-deducted-from-your-ca-wallet-on-the-next-payment-date-all-of-your-live-trading-bots-except-for-the-quota-cap-of-freemium-plan-will-be-stopped": "Subscription Payment Failure: If the subscription fee cannot be deducted from your CA-Wallet on the next payment date, all of your live-trading bots, except for the quota cap of freemium plan, will be stopped.", "subscription-warning": "Subscription Warning", "successfully": "Successfully", "successfully-copied": "Successfully Copied!", "successfully-created": "Successfully Created!", "successfully-deleted": "Successfully Deleted!", "successfully-duplicated": "Successfully Duplicated!", "successfully-redeem-the-gift-code": "Successfully redeem the gift code!", "successfully-updated": "Successfully Updated!", "successfully-withdrew": "Successfully Withdrew!", "suggested-stop-loss-1-100": "Suggested Stop Loss ( 1~100% )", "suggested-take-profit-1": "Suggested Take Profit ( ≥ 1% )", "sync-by-position": "Sync By Position", "tab": {"BACKTEST": "BACKTEST", "EDITOR": "EDITOR", "INFO": "LIVE TRADING INFO", "LIVE TRADE": "LIVE TRADE", "RECORD": "RECORD", "SIMULATION": "SIMULATION", "all": "All Strategy", "backtest": "Backtest", "cloud": "Cloud Strategy", "code": "Source Code", "code-backtest": "Backtest", "cumulated-strategy": "Cumulated Strategy", "history": "History", "intro": "Introduction", "live": "Live Trade", "local": "Local Strategy", "log": "Log", "metrics": "Metrics", "performance-metrics": "Performance Metrics", "register": "Registered Strategy", "register-competition": "Registered Competition Strategies", "register-csie5434": "Registered Strategies@CSIE5434", "register-csie5434-team": "Registered Team Strategies@CSIE5434", "register-maicoin": "Registered Strategies@MaiCoin", "score-backtest": "Backtesting Score", "score-invest-amount": "Investment Amount", "score-trader-rank": "Trader Ranking", "setting": "Setting", "simulate": "Simulate", "single-strategy": "Single Strategy", "variable": "Variable", "watch-crypto": "Crypto", "watch-list": "Watchlist", "watch-topgun": "Top Gun"}, "take-profit": "Take Profit", "team": "Team", "telegram": "Telegram", "the-addressess-that-are-verfied-and-can-be-fully-trusted-by-you-alone-for-withdrawal": "The addressess that are verfied and can be fully trusted by YOU alone for withdrawal.", "the-aggregate-trading-volume-cap-for-all-the-n-strategies-you-can-execute": "The aggregate trading volume cap for all the\n strategies you can execute.", "the-amount-of-splits-locked-from-your-ca-wallet-as-you-had-adopted-strategies-from-others-please-note-that-if-adopted-strategies-made-no-profit-for-you-the-locked-crypto-will-be-unlocked-and-go-back-to-your-ca-wallet-when-transactions-end": "The amount of splits locked from your CA-Wallet as you had adopted strategies from others. Please note that if adopted strategies made no profit for you, the locked crypto will be unlocked and go back to your CA-Wallet when transactions end.", "the-amount-of-splits-that-you-had-paid-to-the-developers-whose-strategies-were-adopted-by-and-made-profit-for-you": "The amount of splits that you had paid to the developers whose strategies were adopted by and made profit for you.", "the-amount-of-splits-that-you-had-received-from-the-traders-who-had-adopted-your-strategies-and-gained-profit": "The amount of splits that you had received from the traders who had adopted your strategies and gained profit.", "the-base-you-designed-the-strategy-for-it-should-not-be-modified-once-you-decided-to-submit-it-to-trading-club": "The base you designed the strategy for. It should not be modified once you decided to submit it to Trading Club.", "the-concept-of-the-ratio-is-similar-to-sharpe-ratio": " The concept of the ratio is similar to <PERSON> Ratio.", "the-cumulative-investment-made-by-all-the-adopters": "The cumulative investment made by all the adopters.", "the-cumulative-number-of-times-this-strategy-was-added-to-watchlist": "The cumulative number of times this strategy was added to watchlist.", "the-cumulative-number-of-users-launched-live-trade-of-this-strategy": "The cumulative number of users launched live-trade of this strategy.", "the-exchange-you-designed-the-strategy-for-it-should-not-be-modified-once-you-decided-to-submit-it-to-trading-club": "The exchange you designed the strategy for. It should not be modified once you decided to submit it to Trading Club.", "the-maximum-number-of-api-keys-you-can-register": "The maximum number of API keys you can register.", "the-maximum-number-of-backtestings-you-can-execute-strategies-per-month-the-quota-will-be-reset-to-0-at-the-end-of-each-month-note-that-every-record-is-stored-permanently-and-accessible-at-any-time-until-you-delete-it-manually": "The maximum number of backtestings you can execute strategies per month. The quota will be reset to 0 at the end of each month. Note that every record is stored permanently and accessible at any time until you delete it manually.", "the-more-confirmations-required-the-longer-the-waiting-time-may-be": "The more confirmations required, the longer the waiting time may be.", "the-number-of-backtest-has-reached-the-limit-of-your-subscription": "The number of backtest has reached the limit of your subscription.", "the-number-of-live-trade-has-reached-the-limit-of-your-subscription": "The number of live trade has reached the limit of your subscription.", "the-number-of-winning-trades-divided-by-the-total-number-of-trades": "The number of winning trades divided by the total number of trades. For\nexample, a trader who won on 75 of 100 trades would have a 75% win rate.", "the-percentage-of-splits-that-crypto-arsenal-takes-from-your-ca-wallet-when-you-received-splits-from-those-who-had-adopted-your-strategies-and-gained-profit-the-number-of-percentage-is-different-according-to-the-current-plan-you-had-subscribed": "The percentage of splits that Crypto-Arsenal takes from your CA-Wallet when you received splits from those who had adopted your strategies and gained profit. The number of percentage is different according to the current plan you had subscribed.", "the-quote-you-designed-the-strategy-for-it-should-not-be-modified-once-you-decided-to-submit-it-to-trading-club": "The quote you designed the strategy for. It should not be modified once you decided to submit it to Trading Club.", "the-strategy-will-be-opened-to": "The strategy will be opened to", "the-total-number-of-the-simulation-and-live-ntrading-you-can-execute-on-strategies": "The total number of the simulation and live-\\ntrading you can execute on strategies.", "there-are-no-clubs-exist": "There are no clubs exist.", "there-are-no-stores-exist": "There are no stores exist.", "there-are-no-strategies-yet": "There are no strategies yet.", "there-is-no-whitelisted-address-record-found": "There is NO whitelisted address record found!", "theyll-show-up-here": "they'll show up here.", "this-action-cannot-be-undone-after-it-is-stopped": "This action cannot be undone after it is stopped.", "this-is-a-ca-python-strategy-you-dont-need-to-provide-any-extra-information-about-this-strategy": "This is a CA Python strategy, you don’t need to provide any extra information about this strategy.", "this-is-an-external-trading-signal-strategy-e-g-tv-alert-please-click": "This is an external trading signal strategy (e.g. TV Alert), please click", "this-is-your-current-plan-and-its-expiration-date-the-relevant-services-and-current-usages-are-shown-below-and-reset-per-month-if-you-wish-to-upgrade-downgrade-or-cancel-your-current-plan-please-click-and-apos-change-plan-and-apos-button": "This is your current plan and its expiration date. The relevant services and current usages are shown below and reset per month. If you wish to upgrade, downgrade or cancel your current plan, please click &apos;Change Plan&apos; button.", "this-strategy-can-not-be-duplicated-because-the-owner-of-the-strategy-has-already-off-boarded-it": "This strategy can not be\nduplicated because the\nowner of the strategy has\nalready off-boarded it.", "this-strategy-has-already-been-on-boarded-to-trading-club": "This strategy has already been on-boarded to Trading Club.", "this-strategy-has-been-live-traded-on-baseexchange": "This strategy has been live traded on {{baseExchange}}.", "this-strategy-has-been-live-traded-on-exchange": "This strategy has been live traded on {{exchange}}.", "this-strategy-has-been-undergoing-simulated-trading-for-over-a-month": "This strategy has been undergoing simulated trading for over a month.',", "ti-description": "Feed your strategy on other exchange pairs", "time": {"day": "day", "dayWithCount": "{{count}} day", "dayWithCount_plural": "{{count}} days", "day_plural": "days", "hour": "hour", "hourWithCount": "{{count}} hour", "hourWithCount_plural": "{{count}} hours", "hour_plural": "hours", "lastWithTimeRange": "Within {{timeRange}}", "minute": "minute", "minuteWithCount": "{{count}} minute", "minuteWithCount_plural": "{{count}} minutes", "minute_plural": "minutes", "month": "month", "monthWithCount": "{{count}} month", "monthWithCount_other": "{{count}} months", "monthWithCount_plural": "{{count}} months", "month_plural": "months", "second": "second", "secondWithCount": "{{count}} second", "secondWithCount_plural": "{{count}} seconds", "second_plural": "seconds", "time": "Time", "week": "week", "weekWithCount": "{{count}} week", "weekWithCount_plural": "{{count}} weeks", "week_plural": "weeks", "year": "year", "yearWithCount": "{{count}} year", "yearWithCount_plural": "{{count}} years", "year_plural": "years"}, "to": "to", "to-create-one": "to create one.", "to-fill-out-the-relevant-information-about-this-strategy": "to fill out the relevant information about this strategy.", "to-on-board-your-strategy": "to on board your strategy.", "total": "Total", "total-balance": "Total Balance", "total-balance-in-ca-wallet": "Total Balance in CA Wallet", "total-number-of-adoption": "Total number of adoption", "total-withdrawal-amount": "Total Withdrawal Amount", "trade": {"coin-m-futures": "COIN-M Futures", "coin-m-futures-perp": "COIN-M Futures (Perp.)", "spot": "SPOT", "spot-perp": "SPOT (Perp.)", "usd-m-futures": "USDⓈ-M Futures", "usd-m-futures-perp": "USDⓈ-M Futures (Perp.)"}, "traders-realized-roi": "Traders’ Realized ROI", "traders-realized-total-profit": "Traders’ Realized Total Profit", "traders-unrealized-roi": "Traders’ Unrealized ROI", "traders-unrealized-total-profit": "Traders’ Unrealized Total Profit", "trading-team": "Trading Team", "trading-volume": "Trading Volume", "trading-volume-month": "Trading Volume / Month", "tradingview": "TradingView", "transaction-id": "Transaction ID", "trend": "Trend", "tutorial": "Tutorial", "tutorial-link": "https://docs.crypto-arsenal.io/docs/developer/get-started/python/hello-world", "tv-acknowledgment-message-alert": "<PERSON><PERSON>.", "tv-acknowledgment-message-link": "TradingView", "tv-acknowledgment-message-set-up": "and set up", "tv-alert-api-signal": "TV Alert / API Signal", "tv-description": "Trade with Tradingview alert webhook", "type": "Type", "unable-to-create-api-key-successfully-please-try-again-later": "Unable to create API Key successfully. Please try again later.", "unknown": "Unknown", "unlimited": "Unlimited", "unlocked": "unlocked", "unrealized-roi": "Unrealized ROI", "unrealized-roi-refers-to-the-roi-of-unsold-open-positions": "Unrealized ROI refers to the ROI of unsold open positions. It will be\nupdated according to the way you pre-determined to handle whenever the\ntrading bot is terminated. If you determined to sell all positions with\nmarket price, unrealized ROI becomes to 0% unless the amount is too small\nto sell; if you determined to hold all positions, unrealized ROI is\ncalculated and freezed based on the market price of the moment while the\ntrading bot is stopped.", "unsuccessfully": "Unsuccessfully", "unsuccessfully-withdrew": "Unsuccessfully Withdrew!", "up": "UP", "upcoming": "Upcoming", "upgrade": "Upgrade", "upper-leverage-limit": "Upper Leverage Limit", "usds-m-futures": "USDⓈ-M Futures", "user-setting-of-live-trading": "User Setting of Live Trading", "user-wallet-available-balance-is-not-sufficient": "User wallet available balance is not sufficient.", "view": "View", "view-members": "View Members", "view-the": "View the", "wallet in": " wallet in ", "we-currently-dont-support-subscription-change-from": "We currently don't support subscription change from", "we-will-verify-your-strategy-soon": "We will verify your strategy soon!", "weak": "weak", "web": "Web", "website": "Website", "when-you-get-notifications": "When you get notifications,", "white-verified-message": "This strategy has been undergoing simulated trading for over a month.", "whitelisted-address-book": "Whitelisted Address Book", "whitelisted-address-for-crypto-withdrawal": "Whitelisted Address for Crypto Withdrawal", "win-rate": "Win Rate", "win-rate-description": "The number of winning trades divided by the total number of trades. For example, a trader who won on 75 of 100 trades would have a 75% win rate.", "with-less-than-0-greater-than-periodunit-less-than-0-greater-than-payment": "with <0> {{periodUnit}} </0> payment", "with-yearly-payment": "with yearly payment", "withdraw": "Withdraw", "withdrawal-address": "Withdrawal address.", "withdrawal-confirmation": "<PERSON><PERSON><PERSON> Confirmation", "withdrew": "Withdrew!", "working-on": "We’re working on this now!", "yearly": "yearly", "yellow-verified-message": "This strategy has been live traded.", "you": "You", "you-are-not-allowed-to-live-trade-this-strategy-due-to-the-following-reasons-you-are-not-a-private-domain-copy-trader-or-you-are-currently-on-the-free-plan": "You are not allowed to live trade this strategy due to the following reasons: you are not a private domain copy trader or you are currently on the Free plan.", "you-can-label-the-withdrawal-address-above-which-will-be-added-into-your-whitelisted-address-book-for-the-future-usage": "You can label the withdrawal address above, which will be added into your whitelisted address book for the future usage.", "you-have-already-on-boarded-the-strategy-to-this-club": "You have already on-boarded the strategy to this club.", "you-have-canceled-your-subscription": "you have canceled your subscription.", "you-have-upgraded-to-less-than-0-greater-than-newplannamewithperiod-less-than-0-greater-than-plan": "You have upgraded to <0> {newPlanNameWithPeriod} </0> plan!", "you-haven-and-apos-t-been-verified-yet-less-than-br-greater-than-to-create-a-club-please-hit-apply-below-to-fill-the-application-form": "You haven't been verified yet. \nTo create a club, please hit Apply below to fill the application form.", "you-havent-created-any-api-key-nplease-click-on-add-new-api-key-below": "You haven’t created any API Key.\nPlease click on “Add New API Key” below.", "you-havent-created-any-strategies-yet": "You haven't created any strategies yet.", "you-havent-created-any-trading-club-yet": "You haven't created any Trading Club yet.", "you-havent-joined-nor-created-any-clubs-yet-less-than-br-greater-than-to-create-a-club-please-hit-the-button-below-to-fill-the-form": "You haven't joined nor created any clubs yet. \nTo create a club, please hit the button below to fill the form.", "you-upgraded-from-less-than-0-greater-than-currentplannamewithperiod-less-than-0-greater-than-to-less-than-1-greater-than-newplannamewithperiod-less-than-1-greater-than-plan": "You upgraded from <0> {{currentPlanNameWithPeriod}} </0> to{' '} <1> {{newPlanNameWithPeriod}} </1> plan!", "you-will-be-charged-price-newpriceunit-in-total-to-upgrade-to-newplannamewithperiod-plan": "You will be charged {{price}} {{newPriceUnit}} in total to upgrade to {{newPlanNameWithPeriod}} plan", "you-will-be-charged-price-usd-in-total": "You will be charged {{price}} USD in total.", "you-withdrew": "You withdrew", "your code, please make sure": "your code, please make sure", "your-current-currentplannamewithperiod-plan-continues-until-currentexpiredformateddate-newplannamewithperiod-plan-will-start-from-newstartedformateddate-by-which-you-will-be-charged-nextpaymentamount-nextpaymentunit-in-total": "Your current {{currentPlanNameWithPeriod}} plan continues until {{currentExpiredFormatedDate}}. {{newPlanNameWithPeriod}} plan will start from {{newStartedFormatedDate}} by which you will be charged {{nextPaymentAmount}} {{nextPaymentUnit}} in total.", "your-current-currentplannamewithperiod-plan-continues-until-expireddate-newplannamewithperiod-plan-will-be-activated-on-newstartedformateddate-and-we-will-start-to-charge-nextpaymentamount-nextpaymentunit-each-month": "Your current {{currentPlanNameWithPeriod}} plan continues until {{expiredDate}}. {{newPlanNameWithPeriod}} plan will be activated on {{newStartedFormatedDate}} and we will start to charge {{nextPaymentAmount}} {{nextPaymentUnit}} each month.", "your-current-currentplannamewithperiod-plan-continues-until-reneweddate-newplannamewithperiod-plan-will-be-activated-afterward-with-the-expiry-date-extended-to-newexpireddate": "Your current {{currentPlanNameWithPeriod}} plan continues until {{ renewedDate}}. {{newPlanNameWithPeriod}} plan will be activated afterward with the expiry date extended to {{newExpiredDate}}.", "your-current-currentplannamewithperiod-plan-has-diff-days-left-to-upgrade-to-newplannamewithperiod-plan-you-will-be-charged-paymentamount-paymentunit-to-make-up-the-price-difference": "Your current {{currentPlanNameWithPeriod}} plan has {{diff}} days left. To upgrade to {{newPlanNameWithPeriod}} plan, you will be charged {{paymentAmount}} {{paymentUnit}} to make up the price difference", "your-less-than-0-greater-than-currentplannamewithperiod-less-than-0-greater-than-plan-can-still-be-used-until-less-than-1-greater-than-currentexpiredformateddate-less-than-1-greater-than": "Your <0>{currentPlanNameWithPeriod}</0> plan can still be used until <1> {currentExpiredFormatedDate}</1>.", "your-less-than-0-greater-than-newplannamewithperiod-less-than-0-greater-than-plan-will-be-activated-and-charged-right-after-less-than-1-greater-than-currentplannamewithperiod-less-than-1-greater-than-plan-ends-on-less-than-2-greater-than-currentexpiredformateddate-less-than-2-greater-than": "Your <0> {{newPlanNameWithPeriod}} </0> plan will be activated and charged right after <1> {{currentPlanNameWithPeriod}} </1> plan ends on <2> {{currentExpiredFormatedDate}}</2>.", "your-less-than-0-greater-than-newplannamewithperiod-less-than-0-greater-than-plan-will-be-activated-on-less-than-1-greater-than-newstartedformateddate-less-than-1-greater-than": "Your <0> {{newPlanNameWithPeriod}} </0> plan will be activated on <1> {{newStartedFormatedDate}}</1>.", "your-less-than-0-greater-than-newplannamewithperiod-less-than-0-greater-than-plan-will-be-activated-on-less-than-1-greater-than-reneweddate-less-than-1-greater-than-with-the-expiry-date-extended-to-less-than-2-greater-than-newexpireddate-less-than-2-greater-than": "Your <0> {{newPlanNameWithPeriod}} </0> plan will be activated on <1> {{renewedDate}} </1> with the expiry date extended to <2> {{newExpiredDate}}</2>.", "your-live-trading-bots-will-be-automatically-stopped-based-on-the-following-three-conditions": "Your live-trading bots will be automatically stopped based on the following three conditions:", "your-referrer": "Your Referrer"}